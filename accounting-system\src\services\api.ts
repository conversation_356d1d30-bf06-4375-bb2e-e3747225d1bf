import { Transaction, QueryConditions, TransactionStats, ApiResponse } from '../types';
import { DatabaseService } from './database';

export class ApiService {
  // 創建交易記錄
  static async createTransaction(
    transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<ApiResponse<number>> {
    try {
      const id = await DatabaseService.createTransaction(transactionData);
      return { success: true, data: id };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '創建交易記錄失敗' 
      };
    }
  }

  // 獲取所有交易記錄
  static async getAllTransactions(): Promise<ApiResponse<Transaction[]>> {
    try {
      const transactions = await DatabaseService.getAllTransactions();
      return { success: true, data: transactions };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '獲取交易記錄失敗' 
      };
    }
  }

  // 根據ID獲取交易記錄
  static async getTransactionById(id: number): Promise<ApiResponse<Transaction>> {
    try {
      const transaction = await DatabaseService.getTransactionById(id);
      if (!transaction) {
        return { success: false, error: '找不到指定的交易記錄' };
      }
      return { success: true, data: transaction };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '獲取交易記錄失敗' 
      };
    }
  }

  // 更新交易記錄
  static async updateTransaction(
    id: number, 
    updates: Partial<Transaction>
  ): Promise<ApiResponse<number>> {
    try {
      const result = await DatabaseService.updateTransaction(id, updates);
      return { success: true, data: result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '更新交易記錄失敗' 
      };
    }
  }

  // 刪除交易記錄
  static async deleteTransaction(id: number): Promise<ApiResponse<void>> {
    try {
      await DatabaseService.deleteTransaction(id);
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '刪除交易記錄失敗' 
      };
    }
  }

  // 查詢交易記錄
  static async queryTransactions(conditions: QueryConditions): Promise<ApiResponse<Transaction[]>> {
    try {
      const transactions = await DatabaseService.queryTransactions(conditions);
      return { success: true, data: transactions };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '查詢交易記錄失敗' 
      };
    }
  }

  // 獲取交易統計
  static async getTransactionStats(conditions?: QueryConditions): Promise<ApiResponse<TransactionStats>> {
    try {
      const stats = await DatabaseService.getTransactionStats(conditions);
      return { success: true, data: stats };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '獲取統計資料失敗' 
      };
    }
  }

  // 獲取所有類別
  static async getAllCategories(): Promise<ApiResponse<string[]>> {
    try {
      const categories = await DatabaseService.getAllCategories();
      return { success: true, data: categories };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '獲取類別失敗' 
      };
    }
  }

  // 搜尋交易記錄
  static async searchTransactions(keyword: string): Promise<ApiResponse<Transaction[]>> {
    try {
      if (!keyword.trim()) {
        return { success: false, error: '搜尋關鍵字不能為空' };
      }
      const transactions = await DatabaseService.searchTransactions(keyword);
      return { success: true, data: transactions };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '搜尋交易記錄失敗' 
      };
    }
  }

  // 驗證交易資料
  static validateTransaction(transaction: Partial<Transaction>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!transaction.date) {
      errors.push('日期為必填項目');
    }

    if (!transaction.amount || transaction.amount <= 0) {
      errors.push('金額必須大於0');
    }

    if (!transaction.type || !['income', 'expense'].includes(transaction.type)) {
      errors.push('交易類型必須是收入或支出');
    }

    if (!transaction.category || transaction.category.trim() === '') {
      errors.push('類別為必填項目');
    }

    if (!transaction.description || transaction.description.trim() === '') {
      errors.push('描述為必填項目');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 格式化金額顯示
  static formatAmount(amount: number, currency: string = 'TWD'): string {
    return new Intl.NumberFormat('zh-TW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }

  // 格式化日期顯示
  static formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('zh-TW', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(date);
  }
}
