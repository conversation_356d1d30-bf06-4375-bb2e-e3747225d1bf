{"version": 3, "sources": ["../../dexie/node_modules/.pnpm/tslib@2.3.1/node_modules/tslib/tslib.es6.js", "../../dexie/src/globals/global.ts", "../../dexie/src/functions/utils.ts", "../../dexie/src/errors/errors.js", "../../dexie/src/functions/chaining-functions.js", "../../dexie/src/helpers/debug.ts", "../../dexie/src/helpers/promise.js", "../../dexie/src/functions/temp-transaction.ts", "../../dexie/src/globals/constants.ts", "../../dexie/src/functions/combine.ts", "../../dexie/src/dbcore/keyrange.ts", "../../dexie/src/functions/workaround-undefined-primkey.ts", "../../dexie/src/classes/entity/Entity.ts", "../../dexie/src/functions/cmp.ts", "../../dexie/src/classes/table/table.ts", "../../dexie/src/helpers/Events.js", "../../dexie/src/functions/make-class-constructor.ts", "../../dexie/src/classes/table/table-constructor.ts", "../../dexie/src/classes/collection/collection-helpers.ts", "../../dexie/src/helpers/prop-modification.ts", "../../dexie/src/classes/collection/collection.ts", "../../dexie/src/classes/collection/collection-constructor.ts", "../../dexie/src/functions/compare-functions.ts", "../../dexie/src/classes/where-clause/where-clause-helpers.ts", "../../dexie/src/classes/where-clause/where-clause.ts", "../../dexie/src/classes/where-clause/where-clause-constructor.ts", "../../dexie/src/functions/event-wrappers.ts", "../../dexie/src/globals/global-events.ts", "../../dexie/src/classes/transaction/transaction.ts", "../../dexie/src/classes/transaction/transaction-constructor.ts", "../../dexie/src/helpers/index-spec.ts", "../../dexie/src/helpers/table-schema.ts", "../../dexie/src/functions/quirks.ts", "../../dexie/src/dbcore/get-key-extractor.ts", "../../dexie/src/dbcore/dbcore-indexeddb.ts", "../../dexie/src/classes/dexie/generate-middleware-stacks.ts", "../../dexie/src/classes/version/schema-helpers.ts", "../../dexie/src/classes/version/version.ts", "../../dexie/src/classes/version/version-constructor.ts", "../../dexie/src/helpers/database-enumerator.ts", "../../dexie/src/classes/dexie/vip.ts", "../../dexie/node_modules/.pnpm/safari-14-idb-fix@3.0.0/node_modules/safari-14-idb-fix/dist/index.js", "../../dexie/src/helpers/rangeset.ts", "../../dexie/src/live-query/extend-observability-set.ts", "../../dexie/src/live-query/obs-sets-overlap.ts", "../../dexie/src/live-query/cache/cache.ts", "../../dexie/src/live-query/cache/signalSubscribers.ts", "../../dexie/src/classes/dexie/dexie-open.ts", "../../dexie/src/helpers/yield-support.ts", "../../dexie/src/classes/dexie/transaction-helpers.ts", "../../dexie/src/dbcore/virtual-index-middleware.ts", "../../dexie/src/functions/get-object-diff.ts", "../../dexie/src/dbcore/get-effective-keys.ts", "../../dexie/src/hooks/hooks-middleware.ts", "../../dexie/src/dbcore/cache-existing-values-middleware.ts", "../../dexie/src/live-query/cache/is-cachable-context.ts", "../../dexie/src/live-query/cache/is-cachable-request.ts", "../../dexie/src/live-query/observability-middleware.ts", "../../dexie/src/live-query/cache/adjust-optimistic-request-from-failures.ts", "../../dexie/src/live-query/cache/is-within-range.ts", "../../dexie/src/live-query/cache/apply-optimistic-ops.ts", "../../dexie/src/live-query/cache/are-ranges-equal.ts", "../../dexie/src/live-query/cache/is-super-range.ts", "../../dexie/src/live-query/cache/find-compatible-query.ts", "../../dexie/src/live-query/cache/subscribe-cachentry.ts", "../../dexie/src/live-query/cache/cache-middleware.ts", "../../dexie/src/helpers/vipify.ts", "../../dexie/src/classes/dexie/dexie.ts", "../../dexie/src/classes/observable/observable.ts", "../../dexie/src/classes/dexie/dexie-dom-dependencies.ts", "../../dexie/src/live-query/live-query.ts", "../../dexie/src/classes/dexie/dexie-static-props.ts", "../../dexie/src/live-query/propagate-locally.ts", "../../dexie/src/live-query/enable-broadcast.ts", "../../dexie/src/support-bfcache.ts", "../../dexie/src/functions/propmods/add.ts", "../../dexie/src/functions/propmods/remove.ts", "../../dexie/src/functions/propmods/replace-prefix.ts", "../../dexie/src/index.ts", "../../dexie/src/index-umd.ts", "../../dexie/import-wrapper.mjs"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n", "declare var global;\nexport const _global: any =\n    typeof globalThis !== 'undefined' ? globalThis :\n    typeof self !== 'undefined' ? self :\n    typeof window !== 'undefined' ? window :\n    global;\n", "﻿import { _global } from \"../globals/global\";\nexport const keys = Object.keys;\nexport const isArray = Array.isArray;\nif (typeof Promise !== 'undefined' && !_global.Promise){\n    // In jsdom, this it can be the case that Promise is not put on the global object.\n    // If so, we need to patch the global object for the rest of the code to work as expected.\n    // Other dexie code expects Promise to be on the global object (like normal browser environments)\n    _global.Promise = Promise;\n}\nexport { _global }\n\nexport function extend<T extends object,X extends object>(obj: T, extension: X): T & X  {\n    if (typeof extension !== 'object') return obj as T & X;\n    keys(extension).forEach(function (key) {\n        obj[key] = extension[key];\n    });\n    return obj as T & X;\n}\n\nexport const getProto = Object.getPrototypeOf;\nexport const _hasOwn = {}.hasOwnProperty;\nexport function hasOwn(obj, prop) {\n    return _hasOwn.call(obj, prop);\n}\n\nexport function props (proto, extension) {\n    if (typeof extension === 'function') extension = extension(getProto(proto));\n    (typeof Reflect === \"undefined\" ? keys : Reflect.ownKeys)(extension).forEach(key => {\n        setProp(proto, key, extension[key]);\n    });\n}\n\nexport const defineProperty = Object.defineProperty;\n\nexport function setProp(obj, prop, functionOrGetSet, options?) {\n    defineProperty(obj, prop, extend(functionOrGetSet && hasOwn(functionOrGetSet, \"get\") && typeof functionOrGetSet.get === 'function' ?\n        {get: functionOrGetSet.get, set: functionOrGetSet.set, configurable: true} :\n        {value: functionOrGetSet, configurable: true, writable: true}, options));\n}\n\nexport function derive(Child) {\n    return {\n        from: function (Parent) {\n            Child.prototype = Object.create(Parent.prototype);\n            setProp(Child.prototype, \"constructor\", Child);\n            return {\n                extend: props.bind(null, Child.prototype)\n            };\n        }\n    };\n}\n\nexport const getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\nexport function getPropertyDescriptor(obj, prop) {\n    const pd = getOwnPropertyDescriptor(obj, prop);\n    let proto;\n    return pd || (proto = getProto(obj)) && getPropertyDescriptor (proto, prop);\n}\n\nconst _slice = [].slice;\nexport function slice(args, start?, end?) {\n    return _slice.call(args, start, end);\n}\n\nexport function override(origFunc, overridedFactory) {\n    return overridedFactory(origFunc);\n}\n\nexport function assert (b) {\n    if (!b) throw new Error(\"Assertion Failed\");\n}\n\nexport function asap(fn) {\n    // @ts-ignore\n    if (_global.setImmediate) setImmediate(fn); else setTimeout(fn, 0);\n}\n\nexport function getUniqueArray(a) {\n    return a.filter((value, index, self) => self.indexOf(value) === index);\n}\n\n/** Generate an object (hash map) based on given array.\n * @param extractor Function taking an array item and its index and returning an array of 2 items ([key, value]) to\n *        instert on the resulting object for each item in the array. If this function returns a falsy value, the\n *        current item wont affect the resulting object.\n */\nexport function arrayToObject<T,R> (array: T[], extractor: (x:T, idx: number)=>[string, R]): {[name: string]: R} {\n    return array.reduce((result, item, i) => {\n        var nameAndValue = extractor(item, i);\n        if (nameAndValue) result[nameAndValue[0]] = nameAndValue[1];\n        return result;\n    }, {});\n}\n\nexport function trycatcher(fn, reject) {\n    return function () {\n        try {\n            fn.apply(this, arguments);\n        } catch (e) {\n            reject(e);\n        }\n    };\n}\n\nexport function tryCatch(fn: (...args: any[])=>void, onerror, args?) : void {\n    try {\n        fn.apply(null, args);\n    } catch (ex) {\n        onerror && onerror(ex);\n    }\n}\n\nexport function getByKeyPath(obj, keyPath) {\n    // http://www.w3.org/TR/IndexedDB/#steps-for-extracting-a-key-from-a-value-using-a-key-path\n    if (typeof keyPath === 'string' && hasOwn(obj, keyPath)) return obj[keyPath]; // This line is moved from last to first for optimization purpose.\n    if (!keyPath) return obj;\n    if (typeof keyPath !== 'string') {\n        var rv = [];\n        for (var i = 0, l = keyPath.length; i < l; ++i) {\n            var val = getByKeyPath(obj, keyPath[i]);\n            rv.push(val);\n        }\n        return rv;\n    }\n    var period = keyPath.indexOf('.');\n    if (period !== -1) {\n        var innerObj = obj[keyPath.substr(0, period)];\n        return innerObj == null ? undefined : getByKeyPath(innerObj, keyPath.substr(period + 1));\n    }\n    return undefined;\n}\n\nexport function setByKeyPath(obj, keyPath, value) {\n    if (!obj || keyPath === undefined) return;\n    if ('isFrozen' in Object && Object.isFrozen(obj)) return;\n    if (typeof keyPath !== 'string' && 'length' in keyPath) {\n        assert(typeof value !== 'string' && 'length' in value);\n        for (var i = 0, l = keyPath.length; i < l; ++i) {\n            setByKeyPath(obj, keyPath[i], value[i]);\n        }\n    } else {\n        var period = keyPath.indexOf('.');\n        if (period !== -1) {\n            var currentKeyPath = keyPath.substr(0, period);\n            var remainingKeyPath = keyPath.substr(period + 1);\n            if (remainingKeyPath === \"\")\n                if (value === undefined) {\n                    if (isArray(obj) && !isNaN(parseInt(currentKeyPath))) obj.splice(currentKeyPath, 1);\n                    else delete obj[currentKeyPath];\n                } else obj[currentKeyPath] = value;\n            else {\n                var innerObj = obj[currentKeyPath];\n                if (!innerObj || !hasOwn(obj, currentKeyPath)) innerObj = (obj[currentKeyPath] = {});\n                setByKeyPath(innerObj, remainingKeyPath, value);\n            }\n        } else {\n            if (value === undefined) {\n                if (isArray(obj) && !isNaN(parseInt(keyPath))) obj.splice(keyPath, 1);\n                else delete obj[keyPath];\n            } else obj[keyPath] = value;\n        }\n    }\n}\n\nexport function delByKeyPath(obj, keyPath) {\n    if (typeof keyPath === 'string')\n        setByKeyPath(obj, keyPath, undefined);\n    else if ('length' in keyPath)\n        [].map.call(keyPath, function(kp) {\n            setByKeyPath(obj, kp, undefined);\n        });\n}\n\nexport function shallowClone(obj) {\n    var rv = {};\n    for (var m in obj) {\n        if (hasOwn(obj, m)) rv[m] = obj[m];\n    }\n    return rv;\n}\n\nconst concat = [].concat;\nexport function flatten<T> (a: (T | T[])[]) : T[] {\n    return concat.apply([], a);\n}\n\n//https://developer.mozilla.org/en-US/docs/Web/API/Web_Workers_API/Structured_clone_algorithm\nconst intrinsicTypeNames =\n    \"BigUint64Array,BigInt64Array,Array,Boolean,String,Date,RegExp,Blob,File,FileList,FileSystemFileHandle,FileSystemDirectoryHandle,ArrayBuffer,DataView,Uint8ClampedArray,ImageBitmap,ImageData,Map,Set,CryptoKey\"\n    .split(',').concat(\n        flatten([8,16,32,64].map(num=>[\"Int\",\"Uint\",\"Float\"].map(t=>t+num+\"Array\")))\n    ).filter(t=>_global[t]);\nconst intrinsicTypes = new Set(intrinsicTypeNames.map(t=>_global[t]));\n\n/** Deep clone a simple object tree.\n * \n * Copies object tree deeply, but does not deep-copy arrays,\n * typed arrays, Dates or other intrinsic types.\n * \n * Does not check for cyclic references.\n * \n * This function is 6 times faster than structuredClone() on chromium 111.\n * \n * This function can safely be used for cloning ObservabilitySets and RangeSets.\n * \n * @param o Object to clone\n * @returns Cloned object\n */\nexport function cloneSimpleObjectTree<T extends object>(o: T): T {\n    const rv = {} as T;\n    for (const k in o) if (hasOwn(o, k)) {\n        const v = o[k];\n        rv[k] = !v || typeof v !== 'object' || intrinsicTypes.has(v.constructor) ? v : cloneSimpleObjectTree(v);\n    }\n    return rv;\n}\n\nexport function objectIsEmpty(o: object) {\n    for (const k in o) if (hasOwn(o, k)) return false;\n    return true;\n}\n\nlet circularRefs: null | WeakMap<any,any> = null;\n\n/** Deep clone an object or array.\n * \n * \n * @param any \n * @returns \n */\nexport function deepClone<T>(any: T): T {\n    circularRefs = new WeakMap();\n    const rv = innerDeepClone(any);\n    circularRefs = null;\n    return rv;\n}\n\nfunction innerDeepClone<T>(x: T): T {\n    if (!x || typeof x !== 'object') return x;\n    let rv = circularRefs.get(x); // Resolve circular references\n    if (rv) return rv;\n    if (isArray(x)) {\n        rv = [];\n        circularRefs.set(x, rv);\n        for (var i = 0, l = x.length; i < l; ++i) {\n            rv.push(innerDeepClone(x[i]));\n        }\n    } else if (intrinsicTypes.has(x.constructor)) {\n        // For performance, we're less strict than structuredClone - we're only\n        // cloning arrays and custom objects.\n        // Typed arrays, Dates etc are not cloned.\n        rv = x;\n    } else {\n        // We're nicer to custom classes than what structuredClone() is -\n        // we preserve the proto of each object.\n        const proto = getProto(x);\n        rv = proto === Object.prototype ? {} : Object.create(proto);\n        circularRefs.set(x, rv);\n        for (var prop in x) {\n            if (hasOwn(x, prop)) {\n                rv[prop] = innerDeepClone(x[prop]);\n            }\n        }\n    }\n    return rv;\n}\n\nconst {toString} = {};\nexport function toStringTag(o: Object) {\n    return toString.call(o).slice(8, -1);\n}\n\n// If first argument is iterable or array-like, return it as an array\nexport const iteratorSymbol = typeof Symbol !== 'undefined' ?\n    Symbol.iterator :\n    '@@iterator';\nexport const getIteratorOf = typeof iteratorSymbol === \"symbol\" ? function(x) {\n    var i;\n    return x != null && (i = x[iteratorSymbol]) && i.apply(x);\n} : function () { return null; };\nexport const asyncIteratorSymbol = typeof Symbol !== 'undefined'\n    ? Symbol.asyncIterator || Symbol.for(\"Symbol.asyncIterator\")\n    : '@asyncIterator';\n\nexport function delArrayItem(a: any[], x: any) {\n    const i = a.indexOf(x);\n    if (i >= 0) a.splice(i, 1);\n    return i >= 0;\n}\n\nexport const NO_CHAR_ARRAY = {};\n// Takes one or several arguments and returns an array based on the following criteras:\n// * If several arguments provided, return arguments converted to an array in a way that\n//   still allows javascript engine to optimize the code.\n// * If single argument is an array, return a clone of it.\n// * If this-pointer equals NO_CHAR_ARRAY, don't accept strings as valid iterables as a special\n//   case to the two bullets below.\n// * If single argument is an iterable, convert it to an array and return the resulting array.\n// * If single argument is array-like (has length of type number), convert it to an array.\nexport function getArrayOf (arrayLike) {\n    var i, a, x, it;\n    if (arguments.length === 1) {\n        if (isArray(arrayLike)) return arrayLike.slice();\n        if (this === NO_CHAR_ARRAY && typeof arrayLike === 'string') return [arrayLike];\n        if ((it = getIteratorOf(arrayLike))) {\n            a = [];\n            while ((x = it.next()), !x.done) a.push(x.value);\n            return a;\n        }\n        if (arrayLike == null) return [arrayLike];\n        i = arrayLike.length;\n        if (typeof i === 'number') {\n            a = new Array(i);\n            while (i--) a[i] = arrayLike[i];\n            return a;\n        }\n        return [arrayLike];\n    }\n    i = arguments.length;\n    a = new Array(i);\n    while (i--) a[i] = arguments[i];\n    return a;\n}\nexport const isAsyncFunction = typeof Symbol !== 'undefined'\n    ? (fn: Function) => fn[Symbol.toStringTag] === 'AsyncFunction'\n    : ()=>false;\n", "import { derive, setProp } from '../functions/utils';\n\nvar dexieErrorNames = [\n    'Modify',\n    'Bulk',\n    'OpenFailed',\n    'VersionChange',\n    'Schema',\n    'Upgrade',\n    'InvalidTable',\n    'MissingAPI',\n    'NoSuchDatabase',\n    'InvalidArgument',\n    'SubTransaction',\n    'Unsupported',\n    'Internal',\n    'DatabaseClosed',\n    'PrematureCommit',\n    'ForeignAwait'\n];\n\nvar idbDomErrorNames = [\n    'Unknown',\n    'Constraint',\n    'Data',\n    'TransactionInactive',\n    'ReadOnly',\n    'Version',\n    'NotFound',\n    'InvalidState',\n    'InvalidAccess',\n    'Abort',\n    'Timeout',\n    'QuotaExceeded',\n    'Syntax',\n    'DataClone'\n];\n\nvar errorList = dexieErrorNames.concat(idbDomErrorNames);\n\nvar defaultTexts = {\n    VersionChanged: \"Database version changed by other database connection\",\n    DatabaseClosed: \"Database has been closed\",\n    Abort: \"Transaction aborted\",\n    TransactionInactive: \"Transaction has already completed or failed\",\n    MissingAPI: \"IndexedDB API missing. Please visit https://tinyurl.com/y2uuvskb\"\n};\n\n//\n// DexieError - base class of all out exceptions.\n//\nexport function DexieError (name, msg) {\n    // Reason we don't use ES6 classes is because:\n    // 1. It bloats transpiled code and increases size of minified code.\n    // 2. It doesn't give us much in this case.\n    // 3. It would require sub classes to call super(), which\n    //    is not needed when deriving from Error.\n    this.name = name;\n    this.message = msg;\n}\n\nderive(DexieError).from(Error).extend({\n    toString: function(){ return this.name + \": \" + this.message; }\n});\n\nfunction getMultiErrorMessage (msg, failures) {\n    return msg + \". Errors: \" + Object.keys(failures)\n        .map(key=>failures[key].toString())\n        .filter((v,i,s)=>s.indexOf(v) === i) // Only unique error strings\n        .join('\\n');\n}\n\n//\n// ModifyError - thrown in Collection.modify()\n// Specific constructor because it contains members failures and failedKeys.\n//\nexport function ModifyError (msg, failures, successCount, failedKeys) {\n    this.failures = failures;\n    this.failedKeys = failedKeys;\n    this.successCount = successCount;\n    this.message = getMultiErrorMessage(msg, failures);\n}\nderive(ModifyError).from(DexieError);\n\nexport function BulkError (msg, failures) {\n    this.name = \"BulkError\";\n    this.failures = Object.keys(failures).map(pos => failures[pos]);\n    this.failuresByPos = failures;\n    this.message = getMultiErrorMessage(msg, this.failures);\n}\nderive(BulkError).from(DexieError);\n\n//\n//\n// Dynamically generate error names and exception classes based\n// on the names in errorList.\n//\n//\n\n// Map of {ErrorName -> ErrorName + \"Error\"}\nexport var errnames = errorList.reduce((obj,name)=>(obj[name]=name+\"Error\",obj),{});\n\n// Need an alias for DexieError because we're gonna create subclasses with the same name.\nconst BaseException = DexieError;\n// Map of {ErrorName -> exception constructor}\nexport var exceptions = errorList.reduce((obj,name)=>{\n    // Let the name be \"DexieError\" because this name may\n    // be shown in call stack and when debugging. DexieError is\n    // the most true name because it derives from DexieError,\n    // and we cannot change Function.name programatically without\n    // dynamically create a Function object, which would be considered\n    // 'eval-evil'.\n    var fullName = name + \"Error\";\n    function DexieError (msgOrInner, inner){\n        this.name = fullName;\n        if (!msgOrInner) {\n            this.message = defaultTexts[name] || fullName;\n            this.inner = null;\n        } else if (typeof msgOrInner === 'string') {\n            this.message = `${msgOrInner}${!inner ? '' : '\\n ' + inner}`;\n            this.inner = inner || null;\n        } else if (typeof msgOrInner === 'object') {\n            this.message = `${msgOrInner.name} ${msgOrInner.message}`;\n            this.inner = msgOrInner;\n        }\n    }\n    derive(DexieError).from(BaseException);\n    obj[name]=DexieError;\n    return obj;\n},{});\n\n// Use ECMASCRIPT standard exceptions where applicable:\nexceptions.Syntax = SyntaxError;\nexceptions.Type = TypeError;\nexceptions.Range = RangeError;\n\nexport var exceptionMap = idbDomErrorNames.reduce((obj, name)=>{\n    obj[name + \"Error\"] = exceptions[name];\n    return obj;\n}, {});\n\nexport function mapError (domError, message) {\n    if (!domError || domError instanceof DexieError || domError instanceof TypeError || domError instanceof SyntaxError || !domError.name || !exceptionMap[domError.name])\n        return domError;\n    var rv = new exceptionMap[domError.name](message || domError.message, domError);\n    if (\"stack\" in domError) {\n        // Derive stack from inner exception if it has a stack\n        setProp(rv, \"stack\", {get: function(){\n            return this.inner.stack;\n        }});\n    }\n    return rv;\n}\n\nexport var fullNameExceptions = errorList.reduce((obj, name)=>{\n    if ([\"Syntax\",\"Type\",\"Range\"].indexOf(name) === -1)\n        obj[name + \"Error\"] = exceptions[name];\n    return obj;\n}, {});\n\nfullNameExceptions.ModifyError = ModifyError;\nfullNameExceptions.DexieError = DexieError;\nfullNameExceptions.BulkError = BulkError;\n", "import {extend} from './utils';\n\nexport function nop() { }\nexport function mirror(val) { return val; }\nexport function pureFunctionChain(f1, f2) {\n    // Enables chained events that takes ONE argument and returns it to the next function in chain.\n    // This pattern is used in the hook(\"reading\") event.\n    if (f1 == null || f1 === mirror) return f2;\n    return function (val) {\n        return f2(f1(val));\n    };\n}\n\nexport function callBoth(on1, on2) {\n    return function () {\n        on1.apply(this, arguments);\n        on2.apply(this, arguments);\n    };\n}\n\nexport function hookCreatingChain(f1, f2) {\n    // Enables chained events that takes several arguments and may modify first argument by making a modification and then returning the same instance.\n    // This pattern is used in the hook(\"creating\") event.\n    if (f1 === nop) return f2;\n    return function () {\n        var res = f1.apply(this, arguments);\n        if (res !== undefined) arguments[0] = res;\n        var onsuccess = this.onsuccess, // In case event listener has set this.onsuccess\n            onerror = this.onerror;     // In case event listener has set this.onerror\n        this.onsuccess = null;\n        this.onerror = null;\n        var res2 = f2.apply(this, arguments);\n        if (onsuccess) this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror) this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n        return res2 !== undefined ? res2 : res;\n    };\n}\n\nexport function hookDeletingChain(f1, f2) {\n    if (f1 === nop) return f2;\n    return function () {\n        f1.apply(this, arguments);\n        var onsuccess = this.onsuccess, // In case event listener has set this.onsuccess\n            onerror = this.onerror;     // In case event listener has set this.onerror\n        this.onsuccess = this.onerror = null;\n        f2.apply(this, arguments);\n        if (onsuccess) this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror) this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n    };\n}\n\nexport function hookUpdatingChain(f1, f2) {\n    if (f1 === nop) return f2;\n    return function (modifications) {\n        var res = f1.apply(this, arguments);\n        extend(modifications, res); // If f1 returns new modifications, extend caller's modifications with the result before calling next in chain.\n        var onsuccess = this.onsuccess, // In case event listener has set this.onsuccess\n            onerror = this.onerror;     // In case event listener has set this.onerror\n        this.onsuccess = null;\n        this.onerror = null;\n        var res2 = f2.apply(this, arguments);\n        if (onsuccess) this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror) this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n        return res === undefined ?\n            (res2 === undefined ? undefined : res2) :\n            (extend(res, res2));\n    };\n}\n\nexport function reverseStoppableEventChain(f1, f2) {\n    if (f1 === nop) return f2;\n    return function () {\n        if (f2.apply(this, arguments) === false) return false;\n        return f1.apply(this, arguments);\n    };\n}\n\nexport function nonStoppableEventChain(f1, f2) {\n    if (f1 === nop) return f2;\n    return function () {\n        f1.apply(this, arguments);\n        f2.apply(this, arguments);\n    };\n}\n\nexport function promisableChain(f1, f2) {\n    if (f1 === nop) return f2;\n    return function () {\n        var res = f1.apply(this, arguments);\n        if (res && typeof res.then === 'function') {\n            var thiz = this,\n                i = arguments.length,\n                args = new Array(i);\n            while (i--) args[i] = arguments[i];\n            return res.then(function () {\n                return f2.apply(thiz, args);\n            });\n        }\n        return f2.apply(this, arguments);\n    };\n}\n", "// By default, debug will be true only if platform is a web platform and its page is served from localhost.\n// When debug = true, error's stacks will contain asyncronic long stacks.\nexport var debug = typeof location !== 'undefined' &&\n        // By default, use debug mode if served from localhost.\n        /^(http|https):\\/\\/(localhost|127\\.0\\.0\\.1)/.test(location.href);\n\nexport function setDebug(value, filter) {\n    debug = value;\n}\n\nexport function deprecated<T> (what: string, fn: (...args)=>T) {\n    return function () {\n        console.warn(`${what} is deprecated. See https://dexie.org/docs/Deprecations}`);\n        return fn.apply(this, arguments);\n    } as (...args)=>T\n}\n", "/*\n * Copyright (c) 2014-2017 <PERSON>\n * Apache License Version 2.0, January 2004, http://www.apache.org/licenses/LICENSE-2.0\n */\nimport { _global } from '../globals/global';\nimport {tryCatch, props, setProp,\n    getPropertyDescriptor, getArrayOf, extend, getProto} from '../functions/utils';\nimport {nop, callBoth, mirror} from '../functions/chaining-functions';\nimport {debug} from './debug';\nimport {exceptions} from '../errors';\n\n//\n// Promise and Zone (PSD) for Dexie library\n//\n// I started out writing this Promise class by copying promise-light (https://github.com/taylorhakes/promise-light) by\n// https://github.com/taylorhakes - an A+ and ECMASCRIPT 6 compliant Promise implementation.\n//\n// In previous versions this was fixed by not calling setTimeout when knowing that the resolve() or reject() came from another\n// tick. In Dexie v1.4.0, I've rewritten the Promise class entirely. Just some fragments of promise-light is left. I use\n// another strategy now that simplifies everything a lot: to always execute callbacks in a new micro-task, but have an own micro-task\n// engine that is indexedDB compliant across all browsers.\n// Promise class has also been optimized a lot with inspiration from bluebird - to avoid closures as much as possible.\n//\n// Specific non-standard features of this Promise class:\n// * Custom zone support (a.k.a. PSD) with ability to keep zones also when using native promises as well as\n//   native async / await.\n// * Promise.follow() method built upon the custom zone engine, that allows user to track all promises created from current stack frame\n//   and below + all promises that those promises creates or awaits.\n// * Detect any unhandled promise in a PSD-scope (PSD.onunhandled). \n//\n// David Fahlander, https://github.com/dfahlander\n//\n\n// Just a pointer that only this module knows about.\n// Used in Promise constructor to emulate a private constructor.\nvar INTERNAL = {};\n\nconst\n    ZONE_ECHO_LIMIT = 100,\n    [resolvedNativePromise, nativePromiseProto, resolvedGlobalPromise] = typeof Promise === 'undefined' ?\n        [] :\n        (()=>{\n            let globalP = Promise.resolve();\n            if (typeof crypto === 'undefined' || !crypto.subtle)\n                return [globalP, getProto(globalP), globalP];\n            // Generate a native promise (as window.Promise may have been patched)\n            const nativeP = crypto.subtle.digest(\"SHA-512\", new Uint8Array([0]));\n            return [\n                nativeP,\n                getProto(nativeP),\n                globalP\n            ];\n        })(),\n    nativePromiseThen = nativePromiseProto && nativePromiseProto.then;\n\nexport const NativePromise = resolvedNativePromise && resolvedNativePromise.constructor;\nconst patchGlobalPromise = !!resolvedGlobalPromise;\n\n/* The default function used only for the very first promise in a promise chain.\n   As soon as then promise is resolved or rejected, all next tasks will be executed in micro ticks\n   emulated in this module. For indexedDB compatibility, this means that every method needs to \n   execute at least one promise before doing an indexedDB operation. Dexie will always call \n   db.ready().then() for every operation to make sure the indexedDB event is started in an\n   indexedDB-compatible emulated micro task loop.\n*/\nfunction schedulePhysicalTick() {\n    queueMicrotask(physicalTick);\n}\n\n// Configurable through Promise.scheduler.\n// Don't export because it would be unsafe to let unknown\n// code call it unless they do try..catch within their callback.\n// This function can be retrieved through getter of Promise.scheduler though,\n// but users must not do Promise.scheduler = myFuncThatThrowsException\nvar asap = function (callback, args) {\n    microtickQueue.push([callback, args]);\n    if (needsNewPhysicalTick) {\n        schedulePhysicalTick();\n        needsNewPhysicalTick = false;\n    }\n};\n\nvar isOutsideMicroTick = true, // True when NOT in a virtual microTick.\n    needsNewPhysicalTick = true, // True when a push to microtickQueue must also schedulePhysicalTick()\n    unhandledErrors = [], // Rejected promises that has occured. Used for triggering 'unhandledrejection'.\n    rejectingErrors = [], // Tracks if errors are being re-rejected during onRejected callback.\n    rejectionMapper = mirror; // Remove in next major when removing error mapping of DOMErrors and DOMExceptions\n    \nexport var globalPSD = {\n    id: 'global',\n    global: true,\n    ref: 0,\n    unhandleds: [],\n    onunhandled: nop,\n    pgp: false,\n    env: {},\n    finalize: nop\n};\n\nexport var PSD = globalPSD;\n\nexport var microtickQueue = []; // Callbacks to call in this or next physical tick.\nexport var numScheduledCalls = 0; // Number of listener-calls left to do in this physical tick.\nexport var tickFinalizers = []; // Finalizers to call when there are no more async calls scheduled within current physical tick.\n\nexport default function DexiePromise(fn) {\n    if (typeof this !== 'object') throw new TypeError('Promises must be constructed via new');    \n    this._listeners = [];\n    \n    // A library may set `promise._lib = true;` after promise is created to make resolve() or reject()\n    // execute the microtask engine implicitely within the call to resolve() or reject().\n    // To remain A+ compliant, a library must only set `_lib=true` if it can guarantee that the stack\n    // only contains library code when calling resolve() or reject().\n    // RULE OF THUMB: ONLY set _lib = true for promises explicitely resolving/rejecting directly from\n    // global scope (event handler, timer etc)!\n    this._lib = false;\n    // Current async scope\n    var psd = (this._PSD = PSD);\n    \n    if (typeof fn !== 'function') {\n        if (fn !== INTERNAL) throw new TypeError('Not a function');\n        // Private constructor (INTERNAL, state, value).\n        // Used internally by Promise.resolve() and Promise.reject().\n        this._state = arguments[1];\n        this._value = arguments[2];\n        if (this._state === false)\n            handleRejection(this, this._value); // Map error, set stack and addPossiblyUnhandledError().\n        return;\n    }\n    \n    this._state = null; // null (=pending), false (=rejected) or true (=resolved)\n    this._value = null; // error or result\n    ++psd.ref; // Refcounting current scope\n    executePromiseTask(this, fn);\n}\n\n// Prepare a property descriptor to put onto Promise.prototype.then\nconst thenProp = {\n    get: function() {\n        var psd = PSD, microTaskId = totalEchoes;\n\n        function then (onFulfilled, onRejected) {\n            var possibleAwait = !psd.global && (psd !== PSD || microTaskId !== totalEchoes);\n            const cleanup = possibleAwait && !decrementExpectedAwaits();\n            var rv = new DexiePromise((resolve, reject) => {\n                propagateToListener(this, new Listener(\n                    nativeAwaitCompatibleWrap(onFulfilled, psd, possibleAwait, cleanup),\n                    nativeAwaitCompatibleWrap(onRejected, psd, possibleAwait, cleanup),\n                    resolve,\n                    reject,\n                    psd));\n            });\n            if (this._consoleTask) rv._consoleTask = this._consoleTask;\n            return rv;\n        }\n\n        then.prototype = INTERNAL; // For idempotense, see setter below.\n\n        return then;\n    },\n    // Be idempotent and allow another framework (such as zone.js or another instance of a Dexie.Promise module) to replace Promise.prototype.then\n    // and when that framework wants to restore the original property, we must identify that and restore the original property descriptor.\n    set: function (value) {\n        setProp (this, 'then', value && value.prototype === INTERNAL ?\n            thenProp : // Restore to original property descriptor.\n            {\n                get: function(){\n                    return value; // Getter returning provided value (behaves like value is just changed)\n                },\n                set: thenProp.set // Keep a setter that is prepared to restore original.\n            }\n        );\n    }\n};\n\nprops(DexiePromise.prototype, {\n    then: thenProp, // Defined above.\n    _then: function (onFulfilled, onRejected) {\n        // A little tinier version of then() that don't have to create a resulting promise.\n        propagateToListener(this, new Listener(null, null, onFulfilled, onRejected, PSD));        \n    },\n\n    catch: function (onRejected) {\n        if (arguments.length === 1) return this.then(null, onRejected);\n        // First argument is the Error type to catch\n        var type = arguments[0],\n            handler = arguments[1];\n        return typeof type === 'function' ? this.then(null, err =>\n            // Catching errors by its constructor type (similar to java / c++ / c#)\n            // Sample: promise.catch(TypeError, function (e) { ... });\n            err instanceof type ? handler(err) : PromiseReject(err))\n        : this.then(null, err =>\n            // Catching errors by the error.name property. Makes sense for indexedDB where error type\n            // is always DOMError but where e.name tells the actual error type.\n            // Sample: promise.catch('ConstraintError', function (e) { ... });\n            err && err.name === type ? handler(err) : PromiseReject(err));\n    },\n\n    finally: function (onFinally) {\n        return this.then(value => {\n            return DexiePromise.resolve(onFinally()).then(()=>value);\n        }, err => {\n            return DexiePromise.resolve(onFinally()).then(()=>PromiseReject(err));\n        });\n    },\n    \n    timeout: function (ms, msg) {\n        return ms < Infinity ?\n            new DexiePromise((resolve, reject) => {\n                var handle = setTimeout(() => reject(new exceptions.Timeout(msg)), ms);\n                this.then(resolve, reject).finally(clearTimeout.bind(null, handle));\n            }) : this;\n    }\n});\n\nif (typeof Symbol !== 'undefined' && Symbol.toStringTag)\n    setProp(DexiePromise.prototype, Symbol.toStringTag, 'Dexie.Promise');\n\n// Now that Promise.prototype is defined, we have all it takes to set globalPSD.env.\n// Environment globals snapshotted on leaving global zone\nglobalPSD.env = snapShot();\n\nfunction Listener(onFulfilled, onRejected, resolve, reject, zone) {\n    this.onFulfilled = typeof onFulfilled === 'function' ? onFulfilled : null;\n    this.onRejected = typeof onRejected === 'function' ? onRejected : null;\n    this.resolve = resolve;\n    this.reject = reject;\n    this.psd = zone;\n}\n\n// Promise Static Properties\nprops (DexiePromise, {\n    all: function () {\n        var values = getArrayOf.apply(null, arguments) // Supports iterables, implicit arguments and array-like.\n            .map(onPossibleParallellAsync); // Handle parallell async/awaits \n        return new DexiePromise(function (resolve, reject) {\n            if (values.length === 0) resolve([]);\n            var remaining = values.length;\n            values.forEach((a,i) => DexiePromise.resolve(a).then(x => {\n                values[i] = x;\n                if (!--remaining) resolve(values);\n            }, reject));\n        });\n    },\n    \n    resolve: value => {\n        if (value instanceof DexiePromise) return value;\n        if (value && typeof value.then === 'function') return new DexiePromise((resolve, reject)=>{\n            value.then(resolve, reject);\n        });\n        var rv = new DexiePromise(INTERNAL, true, value);\n        return rv;\n    },\n    \n    reject: PromiseReject,\n    \n    race: function () {\n        var values = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n        return new DexiePromise((resolve, reject) => {\n            values.map(value => DexiePromise.resolve(value).then(resolve, reject));\n        });\n    },\n\n    PSD: {\n        get: ()=>PSD,\n        set: value => PSD = value\n    },\n\n    totalEchoes: {get: ()=>totalEchoes},\n\n    //task: {get: ()=>task},\n    \n    newPSD: newScope,\n    \n    usePSD: usePSD,\n    \n    scheduler: {\n        get: () => asap,\n        set: value => {asap = value}\n    },\n    \n    rejectionMapper: {\n        get: () => rejectionMapper,\n        set: value => {rejectionMapper = value;} // Map reject failures\n    },\n            \n    follow: (fn, zoneProps) => {\n        return new DexiePromise((resolve, reject) => {\n            return newScope((resolve, reject) => {\n                var psd = PSD;\n                psd.unhandleds = []; // For unhandled standard- or 3rd party Promises. Checked at psd.finalize()\n                psd.onunhandled = reject; // Triggered directly on unhandled promises of this library.\n                psd.finalize = callBoth(function () {\n                    // Unhandled standard or 3rd part promises are put in PSD.unhandleds and\n                    // examined upon scope completion while unhandled rejections in this Promise\n                    // will trigger directly through psd.onunhandled\n                    run_at_end_of_this_or_next_physical_tick(()=>{\n                        this.unhandleds.length === 0 ? resolve() : reject(this.unhandleds[0]);\n                    });\n                }, psd.finalize);\n                fn();\n            }, zoneProps, resolve, reject);\n        });\n    }\n});\n\nif (NativePromise) {\n    if (NativePromise.allSettled) setProp (DexiePromise, \"allSettled\", function() {\n        const possiblePromises = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n        return new DexiePromise(resolve => {\n            if (possiblePromises.length === 0) resolve([]);\n            let remaining = possiblePromises.length;\n            const results = new Array(remaining);\n            possiblePromises.forEach((p, i) => DexiePromise.resolve(p).then(\n                value => results[i] = {status: \"fulfilled\", value},\n                reason => results[i] = {status: \"rejected\", reason})\n                .then(()=>--remaining || resolve(results)));\n        });\n    });\n    if (NativePromise.any && typeof AggregateError !== 'undefined') setProp(DexiePromise, \"any\", function() {\n        const possiblePromises = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n        return new DexiePromise((resolve, reject) => {\n            if (possiblePromises.length === 0) reject(new AggregateError([]));\n            let remaining = possiblePromises.length;\n            const failures = new Array(remaining);\n            possiblePromises.forEach((p, i) => DexiePromise.resolve(p).then(\n                value => resolve(value),\n                failure => {\n                    failures[i] = failure;\n                    if (!--remaining) reject(new AggregateError(failures));\n                }));\n        });\n    });\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers\n    if (NativePromise.withResolvers) DexiePromise.withResolvers = NativePromise.withResolvers;\n}\n\n/**\n* Take a potentially misbehaving resolver function and make sure\n* onFulfilled and onRejected are only called once.\n*\n* Makes no guarantees about asynchrony.\n*/\nfunction executePromiseTask (promise, fn) {\n    // Promise Resolution Procedure:\n    // https://github.com/promises-aplus/promises-spec#the-promise-resolution-procedure\n    try {\n        fn(value => {\n            if (promise._state !== null) return; // Already settled\n            if (value === promise) throw new TypeError('A promise cannot be resolved with itself.');\n            var shouldExecuteTick = promise._lib && beginMicroTickScope();\n            if (value && typeof value.then === 'function') {\n                executePromiseTask(promise, (resolve, reject) => {\n                    value instanceof DexiePromise ?\n                        value._then(resolve, reject) :\n                        value.then(resolve, reject);\n                });\n            } else {\n                promise._state = true;\n                promise._value = value;\n                propagateAllListeners(promise);\n            }\n            if (shouldExecuteTick) endMicroTickScope();\n        }, handleRejection.bind(null, promise)); // If Function.bind is not supported. Exception is handled in catch below\n    } catch (ex) {\n        handleRejection(promise, ex);\n    }\n}\n\nfunction handleRejection (promise, reason) {\n    rejectingErrors.push(reason);\n    if (promise._state !== null) return;\n    var shouldExecuteTick = promise._lib && beginMicroTickScope();\n    reason = rejectionMapper(reason);\n    promise._state = false;\n    promise._value = reason;\n    // Add the failure to a list of possibly uncaught errors\n    addPossiblyUnhandledError(promise);\n    propagateAllListeners(promise);\n    if (shouldExecuteTick) endMicroTickScope();\n}\n\nfunction propagateAllListeners (promise) {\n    //debug && linkToPreviousPromise(promise);\n    var listeners = promise._listeners;\n    promise._listeners = [];\n    for (var i = 0, len = listeners.length; i < len; ++i) {\n        propagateToListener(promise, listeners[i]);\n    }\n    var psd = promise._PSD;\n    --psd.ref || psd.finalize(); // if psd.ref reaches zero, call psd.finalize();\n    if (numScheduledCalls === 0) {\n        // If numScheduledCalls is 0, it means that our stack is not in a callback of a scheduled call,\n        // and that no deferreds where listening to this rejection or success.\n        // Since there is a risk that our stack can contain application code that may\n        // do stuff after this code is finished that may generate new calls, we cannot\n        // call finalizers here.\n        ++numScheduledCalls;\n        asap(()=>{\n            if (--numScheduledCalls === 0) finalizePhysicalTick(); // Will detect unhandled errors\n        }, []);\n    }\n}\n\nfunction propagateToListener(promise, listener) {\n    if (promise._state === null) {\n        promise._listeners.push(listener);\n        return;\n    }\n\n    var cb = promise._state ? listener.onFulfilled : listener.onRejected;\n    if (cb === null) {\n        // This Listener doesnt have a listener for the event being triggered (onFulfilled or onReject) so lets forward the event to any eventual listeners on the Promise instance returned by then() or catch()\n        return (promise._state ? listener.resolve : listener.reject) (promise._value);\n    }\n    ++listener.psd.ref;\n    ++numScheduledCalls;\n    asap (callListener, [cb, promise, listener]);\n}\n\nfunction callListener (cb, promise, listener) {\n    try {\n        // Call callback and resolve our listener with it's return value.\n        var ret, value = promise._value;\n            \n        if (!promise._state && rejectingErrors.length) rejectingErrors = [];\n        // cb is onResolved\n        ret = debug && promise._consoleTask ? promise._consoleTask.run(()=>cb (value)) : cb (value);\n        if (!promise._state && rejectingErrors.indexOf(value) === -1) {\n            markErrorAsHandled(promise); // Callback didnt do Promise.reject(err) nor reject(err) onto another promise.\n        }\n        listener.resolve(ret);\n    } catch (e) {\n        // Exception thrown in callback. Reject our listener.\n        listener.reject(e);\n    } finally {\n        if (--numScheduledCalls === 0) finalizePhysicalTick();\n        --listener.psd.ref || listener.psd.finalize();\n    }\n}\n\n/* The callback to schedule with queueMicrotask().\n   It runs a virtual microtick and executes any callback registered in microtickQueue.\n */\nfunction physicalTick() {\n    usePSD(globalPSD, ()=>{\n        // Make sure to reset the async context to globalPSD before\n        // executing any of the microtick subscribers.\n        beginMicroTickScope() && endMicroTickScope();\n    });\n}\n\nexport function beginMicroTickScope() {\n    var wasRootExec = isOutsideMicroTick;\n    isOutsideMicroTick = false;\n    needsNewPhysicalTick = false;\n    return wasRootExec;\n}\n\n/* Executes micro-ticks without doing try..catch.\n   This can be possible because we only use this internally and\n   the registered functions are exception-safe (they do try..catch\n   internally before calling any external method). If registering\n   functions in the microtickQueue that are not exception-safe, this\n   would destroy the framework and make it instable. So we don't export\n   our asap method.\n*/\nexport function endMicroTickScope() {\n    var callbacks, i, l;\n    do {\n        while (microtickQueue.length > 0) {\n            callbacks = microtickQueue;\n            microtickQueue = [];\n            l = callbacks.length;\n            for (i = 0; i < l; ++i) {\n                var item = callbacks[i];\n                item[0].apply(null, item[1]);\n            }\n        }\n    } while (microtickQueue.length > 0);\n    isOutsideMicroTick = true;\n    needsNewPhysicalTick = true;\n}\n\nfunction finalizePhysicalTick() {\n    var unhandledErrs = unhandledErrors;\n    unhandledErrors = [];\n    unhandledErrs.forEach(p => {\n        p._PSD.onunhandled.call(null, p._value, p);\n    });\n    var finalizers = tickFinalizers.slice(0); // Clone first because finalizer may remove itself from list.\n    var i = finalizers.length;\n    while (i) finalizers[--i]();    \n}\n\nfunction run_at_end_of_this_or_next_physical_tick (fn) {\n    function finalizer() {\n        fn();\n        tickFinalizers.splice(tickFinalizers.indexOf(finalizer), 1);\n    }\n    tickFinalizers.push(finalizer);\n    ++numScheduledCalls;\n    asap(()=>{\n        if (--numScheduledCalls === 0) finalizePhysicalTick();\n    }, []);\n}\n\nfunction addPossiblyUnhandledError(promise) {\n    // Only add to unhandledErrors if not already there. The first one to add to this list\n    // will be upon the first rejection so that the root cause (first promise in the\n    // rejection chain) is the one listed.\n    if (!unhandledErrors.some(p => p._value === promise._value))\n        unhandledErrors.push(promise);\n}\n\nfunction markErrorAsHandled(promise) {\n    // Called when a reject handled is actually being called.\n    // Search in unhandledErrors for any promise whos _value is this promise_value (list\n    // contains only rejected promises, and only one item per error)\n    var i = unhandledErrors.length;\n    while (i) if (unhandledErrors[--i]._value === promise._value) {\n        // Found a promise that failed with this same error object pointer,\n        // Remove that since there is a listener that actually takes care of it.\n        unhandledErrors.splice(i, 1);\n        return;\n    }\n}\n\nfunction PromiseReject (reason) {\n    return new DexiePromise(INTERNAL, false, reason);\n}\n\nexport function wrap (fn, errorCatcher) {\n    var psd = PSD;\n    return function() {\n        var wasRootExec = beginMicroTickScope(),\n            outerScope = PSD;\n\n        try {\n            switchToZone(psd, true);\n            return fn.apply(this, arguments);\n        } catch (e) {\n            errorCatcher && errorCatcher(e);\n        } finally {\n            switchToZone(outerScope, false);\n            if (wasRootExec) endMicroTickScope();\n        }\n    };\n}\n\n\n//\n// variables used for native await support\n//\nconst task = { awaits: 0, echoes: 0, id: 0}; // The ongoing macro-task when using zone-echoing.\nvar taskCounter = 0; // ID counter for macro tasks.\nvar zoneStack = []; // Stack of left zones to restore asynchronically.\nvar zoneEchoes = 0; // When > 0, zoneLeaveEcho is queued. When 0 and task.echoes is also 0, nothing is queued.\nvar totalEchoes = 0; // ID counter for micro-tasks. Used to detect possible native await in our Promise.prototype.then.\n\n\nvar zone_id_counter = 0;\nexport function newScope (fn, props, a1, a2) {\n    var parent = PSD,\n        psd = Object.create(parent);\n    psd.parent = parent;\n    psd.ref = 0;\n    psd.global = false;\n    psd.id = ++zone_id_counter;\n    // Prepare for promise patching (done in usePSD):\n    var globalEnv = globalPSD.env;\n    psd.env = patchGlobalPromise ? {\n        Promise: DexiePromise, // Changing window.Promise could be omitted for Chrome and Edge, where IDB+Promise plays well!\n        PromiseProp: {value: DexiePromise, configurable: true, writable: true},\n        all: DexiePromise.all,\n        race: DexiePromise.race,\n        allSettled: DexiePromise.allSettled,\n        any: DexiePromise.any,\n        resolve: DexiePromise.resolve,\n        reject: DexiePromise.reject,\n    } : {};\n    if (props) extend(psd, props);\n    \n    // unhandleds and onunhandled should not be specifically set here.\n    // Leave them on parent prototype.\n    // unhandleds.push(err) will push to parent's prototype\n    // onunhandled() will call parents onunhandled (with this scope's this-pointer though!)\n    ++parent.ref;\n    psd.finalize = function () {\n        --this.parent.ref || this.parent.finalize();\n    }\n    var rv = usePSD (psd, fn, a1, a2);\n    if (psd.ref === 0) psd.finalize();\n    return rv;\n}\n\n// Function to call if scopeFunc returns NativePromise\n// Also for each NativePromise in the arguments to Promise.all()\nexport function incrementExpectedAwaits() {\n    if (!task.id) task.id = ++taskCounter;\n    ++task.awaits;\n    task.echoes += ZONE_ECHO_LIMIT;\n    return task.id;\n}\n\n// Function to call when 'then' calls back on a native promise where onAwaitExpected() had been called.\n// Also call this when a native await calls then method on a promise. In that case, don't supply\n// sourceTaskId because we already know it refers to current task.\nexport function decrementExpectedAwaits() {\n    if (!task.awaits) return false;\n    if (--task.awaits === 0) task.id = 0;\n    task.echoes = task.awaits * ZONE_ECHO_LIMIT; // Will reset echoes to 0 if awaits is 0.\n    return true;\n}\n\nif ((''+nativePromiseThen).indexOf('[native code]') === -1) {\n    // If the native promise' prototype is patched, we cannot rely on zone echoing.\n    // Disable that here:\n    incrementExpectedAwaits = decrementExpectedAwaits = nop;\n}\n\n// Call from Promise.all() and Promise.race()\nexport function onPossibleParallellAsync (possiblePromise) {\n    if (task.echoes && possiblePromise && possiblePromise.constructor === NativePromise) {\n        incrementExpectedAwaits(); \n        return possiblePromise.then(x => {\n            decrementExpectedAwaits();\n            return x;\n        }, e => {\n            decrementExpectedAwaits();\n            return rejection(e);\n        });\n    }\n    return possiblePromise;\n}\n\nfunction zoneEnterEcho(targetZone) {\n    ++totalEchoes;\n    //console.log(\"Total echoes \", totalEchoes);\n    //if (task.echoes === 1) console.warn(\"Cancelling echoing of async context.\");\n    if (!task.echoes || --task.echoes === 0) {\n        task.echoes = task.awaits = task.id = 0; // Cancel echoing.\n    }\n\n    zoneStack.push(PSD);\n    switchToZone(targetZone, true);\n}\n\nfunction zoneLeaveEcho() {\n    var zone = zoneStack[zoneStack.length-1];\n    zoneStack.pop();\n    switchToZone(zone, false);\n}\n\nfunction switchToZone (targetZone, bEnteringZone) {\n    var currentZone = PSD;\n    if (bEnteringZone ? task.echoes && (!zoneEchoes++ || targetZone !== PSD) : zoneEchoes && (!--zoneEchoes || targetZone !== PSD)) {\n        // Enter or leave zone asynchronically as well, so that tasks initiated during current tick\n        // will be surrounded by the zone when they are invoked.\n        queueMicrotask(bEnteringZone ? zoneEnterEcho.bind(null, targetZone) : zoneLeaveEcho);\n    }\n    if (targetZone === PSD) return;\n\n    PSD = targetZone; // The actual zone switch occurs at this line.\n\n    // Snapshot on every leave from global zone.\n    if (currentZone === globalPSD) globalPSD.env = snapShot();\n\n    if (patchGlobalPromise) {\n        // Let's patch the global and native Promises (may be same or may be different)\n        var GlobalPromise = globalPSD.env.Promise;\n        // Swich environments (may be PSD-zone or the global zone. Both apply.)\n        var targetEnv = targetZone.env;\n\n        if (currentZone.global || targetZone.global) {\n            // Leaving or entering global zone. It's time to patch / restore global Promise.\n\n            // Set this Promise to window.Promise so that transiled async functions will work on Firefox, Safari and IE, as well as with Zonejs and angular.\n            Object.defineProperty(_global, 'Promise', targetEnv.PromiseProp);\n\n            // Support Promise.all() etc to work indexedDB-safe also when people are including es6-promise as a module (they might\n            // not be accessing global.Promise but a local reference to it)\n            GlobalPromise.all = targetEnv.all;\n            GlobalPromise.race = targetEnv.race;\n            GlobalPromise.resolve = targetEnv.resolve;\n            GlobalPromise.reject = targetEnv.reject;\n            if (targetEnv.allSettled) GlobalPromise.allSettled = targetEnv.allSettled;\n            if (targetEnv.any) GlobalPromise.any = targetEnv.any;\n        }\n    }\n}\n\nfunction snapShot () {\n    var GlobalPromise = _global.Promise;\n    return patchGlobalPromise ? {\n        Promise: GlobalPromise,\n        PromiseProp: Object.getOwnPropertyDescriptor(_global, \"Promise\"),\n        all: GlobalPromise.all,\n        race: GlobalPromise.race,\n        allSettled: GlobalPromise.allSettled,\n        any: GlobalPromise.any,\n        resolve: GlobalPromise.resolve,\n        reject: GlobalPromise.reject,\n    } : {};\n}\n\nexport function usePSD (psd, fn, a1, a2, a3) {\n    var outerScope = PSD;\n    try {\n        switchToZone(psd, true);\n        return fn(a1, a2, a3);\n    } finally {\n        switchToZone(outerScope, false);\n    }\n}\n\nfunction nativeAwaitCompatibleWrap(fn, zone, possibleAwait, cleanup) {\n    return typeof fn !== 'function' ? fn : function () {\n        var outerZone = PSD;\n        if (possibleAwait) incrementExpectedAwaits();\n        switchToZone(zone, true);\n        try {\n            return fn.apply(this, arguments);\n        } finally {\n            switchToZone(outerZone, false);\n            if (cleanup) queueMicrotask(decrementExpectedAwaits);\n        }\n    };\n}\n\n/** Execute callback in global context */\nexport function execInGlobalContext(cb) {\n    if (Promise === NativePromise && task.echoes === 0) {\n        if (zoneEchoes === 0) {\n            cb();\n        } else {\n            enqueueNativeMicroTask(cb);\n        }\n    } else {\n        setTimeout(cb, 0);\n    }\n}\n\nexport var rejection = DexiePromise.reject;\n\nexport {DexiePromise};\n", "import { PSD, rejection, newScope } from \"../helpers/promise\";\nimport { DexieOptions } from \"../public/types/dexie-constructor\";\nimport { errnames, exceptions } from \"../errors\";\nimport { nop } from \"./chaining-functions\";\nimport { Transaction } from \"../classes/transaction\";\nimport { <PERSON>ie } from '../classes/dexie';\n\n/* Generate a temporary transaction when db operations are done outside a transaction scope.\n*/\nexport function tempTransaction (\n  db: Dexie,\n  mode: IDBTransactionMode,\n  storeNames: string[],\n  fn: (resolve, reject, trans: Transaction) => any)\n  // Last argument is \"writeLocked\". But this doesnt apply to oneshot direct db operations, so we ignore it.\n{\n  if (!db.idbdb || (!db._state.openComplete && (!PSD.letThrough && !db._vip))) {\n    if (db._state.openComplete) {\n      // db.idbdb is falsy but openComplete is true. Must have been an exception durin open.\n      // Don't wait for openComplete as it would lead to infinite loop.\n      return rejection(new exceptions.DatabaseClosed(db._state.dbOpenError));\n    }\n    if (!db._state.isBeingOpened) {\n      if (!db._state.autoOpen)\n        return rejection(new exceptions.DatabaseClosed());\n      db.open().catch(nop); // Open in background. If if fails, it will be catched by the final promise anyway.\n    }\n    return db._state.dbReadyPromise.then(() => tempTransaction(db, mode, storeNames, fn));\n  } else {\n    var trans = db._createTransaction(mode, storeNames, db._dbSchema);\n    try {\n      trans.create();\n      db._state.PR1398_maxLoop = 3;\n    } catch (ex) {\n      if (ex.name === errnames.InvalidState && db.isOpen() && --db._state.PR1398_maxLoop > 0) {\n        console.warn('Dexie: Need to reopen db');\n        db.close({disableAutoOpen: false});\n        return db.open().then(()=>tempTransaction(db, mode, storeNames, fn));\n      }\n      return rejection(ex);\n    }\n    return trans._promise(mode, (resolve, reject) => {\n      return newScope(() => { // OPTIMIZATION POSSIBLE? newScope() not needed because it's already done in _promise.\n        PSD.trans = trans;\n        return fn(resolve, reject, trans);\n      });\n    }).then(result => {\n      // Instead of resolving value directly, wait with resolving it until transaction has completed.\n      // Otherwise the data would not be in the DB if requesting it in the then() operation.\n      // Specifically, to ensure that the following expression will work:\n      //\n      //   db.friends.put({name: \"Arne\"}).then(function () {\n      //       db.friends.where(\"name\").equals(\"Arne\").count(function(count) {\n      //           assert (count === 1);\n      //       });\n      //   });\n      //\n      if (mode === 'readwrite') try {trans.idbtrans.commit();} catch {}\n      return mode === 'readonly' ? result : trans._completion.then(() => result);\n    });/*.catch(err => { // Don't do this as of now. If would affect bulk- and modify methods in a way that could be more intuitive. But wait! Maybe change in next major.\n          trans._reject(err);\n          return rejection(err);\n      });*/\n  }\n}\n", "import { <PERSON><PERSON> } from \"../classes/dexie\";\n\nexport const DEXIE_VERSION = '{version}'; // Replaced by build-script.\nexport const maxString = String.fromCharCode(65535);\nexport const minKey = -Infinity; // minKey can be constant. maxKey must be a prop of <PERSON><PERSON> (_maxKey)\nexport const INVALID_KEY_ARGUMENT =\n  \"Invalid key provided. Keys must be of type string, number, Date or Array<string | number | Date>.\";\nexport const STRING_EXPECTED = \"String expected.\";\nexport const connections: Dexie[] = [];\nexport const dexieStackFrameFilter = frame => !/(dexie\\.js|dexie\\.min\\.js)/.test(frame);\nexport const DBNAMES_DB = '__dbnames';\nexport const READONLY = 'readonly';\nexport const READWRITE = 'readwrite';\n", "export function combine(filter1, filter2) {\n  return filter1 ?\n      filter2 ?\n          function () { return filter1.apply(this, arguments) && filter2.apply(this, arguments); } :\n          filter1 :\n      filter2;\n}\n", "import { DBCoreKeyRange, DBCoreRangeType } from '../public/types/dbcore';\n\nexport const AnyRange: DBCoreKeyRange = {\n  type: DBCoreRangeType.Any,\n  lower: -Infinity,\n  lowerOpen: false,\n  upper: [[]],\n  upperOpen: false\n}\n\nexport const NeverRange: DBCoreKeyRange = {\n  type: DBCoreRangeType.Never,\n  lower: -Infinity,\n  lowerOpen: true,\n  upper: -Infinity,\n  upperOpen: true\n}\n", "import { deep<PERSON>lone, delBy<PERSON>eyPath, getByKeyPath } from './utils';\n\n// This workaround is needed since obj could be a custom-class instance with an\n// uninitialized keyPath. See the following comment for more context:\n// https://github.com/dfahlander/Dexie.js/issues/1280#issuecomment-823557881\nexport function workaroundForUndefinedPrimKey(keyPath: string | ArrayLike<string>) {\n  // Workaround only needed for plain non-dotted keyPaths\n  return typeof keyPath === \"string\" && !/\\./.test(keyPath) \n  ? (obj: object) => {\n    if (obj[keyPath] === undefined && (keyPath in obj)) {\n      // property exists but is undefined. This will not be liked by Indexeddb.\n      // Need to remove the property before adding it but we need to clone it before\n      // doing that to not be intrusive.\n      obj = deepClone(obj);\n      delete obj[keyPath];\n    }\n    return obj;\n  }\n  : (obj: object) => obj;\n}", "import { exceptions } from \"../../errors\";\n\nexport function Entity(){\n  throw exceptions.Type();\n}\n", "// Implementation of https://www.w3.org/TR/IndexedDB-3/#compare-two-keys\n\nimport { toStringTag } from './utils';\n\n// ... with the adjustment to return NaN instead of throwing.\nexport function cmp(a: any, b: any): number {\n  try {\n    const ta = type(a);\n    const tb = type(b);\n    if (ta !== tb) {\n      if (ta === 'Array') return 1;\n      if (tb === 'Array') return -1;\n      if (ta === 'binary') return 1;\n      if (tb === 'binary') return -1;\n      if (ta === 'string') return 1;\n      if (tb === 'string') return -1;\n      if (ta === 'Date') return 1;\n      if (tb !== 'Date') return NaN;\n      return -1;\n    }\n    switch (ta) {\n      case 'number':\n      case 'Date':\n      case 'string':\n        return a > b ? 1 : a < b ? -1 : 0;\n      case 'binary': {\n        return compareUint8Arrays(getUint8Array(a), getUint8Array(b));\n      }\n      case 'Array':\n        return compareArrays(a, b);\n    }\n  } catch {}\n  return NaN; // Return value if any given args are valid keys.\n}\n\nexport function compareArrays(a: any[], b: any[]): number {\n  const al = a.length;\n  const bl = b.length;\n  const l = al < bl ? al : bl;\n  for (let i = 0; i < l; ++i) {\n    const res = cmp(a[i], b[i]);\n    if (res !== 0) return res;\n  }\n  return al === bl ? 0 : al < bl ? -1 : 1;\n}\n\nexport function compareUint8Arrays(\n  a: Uint8Array,\n  b: Uint8Array\n) {\n  const al = a.length;\n  const bl = b.length;\n  const l = al < bl ? al : bl;\n  for (let i = 0; i < l; ++i) {\n    if (a[i] !== b[i]) return a[i] < b[i] ? -1 : 1;\n  }\n  return al === bl ? 0 : al < bl ? -1 : 1;\n}\n\n// Implementation of https://www.w3.org/TR/IndexedDB-3/#key-type\nfunction type(x: any) {\n  const t = typeof x;\n  if (t !== 'object') return t;\n  if (ArrayBuffer.isView(x)) return 'binary';\n  const tsTag = toStringTag(x); // Cannot use instanceof in Safari\n  return tsTag === 'ArrayBuffer' ? 'binary' : (tsTag as 'Array' | 'Date');\n}\n\ntype BinaryType =\n  | ArrayBuffer\n  | DataView\n  | Uint8ClampedArray\n  | ArrayBufferView\n  | Uint8Array\n  | Int8Array\n  | Uint16Array\n  | Int16Array\n  | Uint32Array\n  | Int32Array\n  | Float32Array\n  | Float64Array\n  | BigInt64Array \n  | BigUint64Array;\n\nfunction getUint8Array(a: BinaryType): Uint8Array {\n  if (a instanceof Uint8Array) return a;\n  if (ArrayBuffer.isView(a))\n    // TypedArray or DataView\n    return new Uint8Array(a.buffer, a.byteOffset, a.byteLength);\n  return new Uint8Array(a); // ArrayBuffer\n}\n", "import { BulkError, exceptions } from '../../errors';\nimport { Table as ITable } from '../../public/types/table';\nimport { TableSchema } from '../../public/types/table-schema';\nimport { TableHooks } from '../../public/types/table-hooks';\nimport { DexiePromise as Promise, PSD, newScope, rejection, beginMicroTickScope, endMicroTickScope } from '../../helpers/promise';\nimport { Transaction } from '../transaction';\nimport { Dexie } from '../dexie';\nimport { tempTransaction } from '../../functions/temp-transaction';\nimport { Collection } from '../collection';\nimport { isArray, keys, getByKeyPath, setByKeyPath, extend, getProto } from '../../functions/utils';\nimport { maxString } from '../../globals/constants';\nimport { combine } from '../../functions/combine';\nimport { PromiseExtended } from \"../../public/types/promise-extended\";\nimport { IndexableType } from '../../public/types/indexable-type';\nimport { debug } from '../../helpers/debug';\nimport { DBCoreTable } from '../../public/types/dbcore';\nimport { AnyRange } from '../../dbcore/keyrange';\nimport { workaroundForUndefinedPrimKey } from '../../functions/workaround-undefined-primkey';\nimport { Entity } from '../entity/Entity';\nimport { UpdateSpec } from '../../public';\nimport { cmp } from '../../functions/cmp';\n\n/** class Table\n * \n * https://dexie.org/docs/Table/Table\n */\nexport class Table implements ITable<any, IndexableType> {\n  db: Dexie;\n  _tx?: Transaction;\n  name: string;\n  schema: TableSchema;\n  hook: TableHooks;\n  core: DBCoreTable;\n\n  _trans(\n    mode: IDBTransactionMode,\n    fn: (idbtrans: IDBTransaction, dxTrans: Transaction) => PromiseLike<any> | void,\n    writeLocked?: boolean | string) : PromiseExtended<any>\n  {\n    const trans: Transaction = this._tx || PSD.trans;\n    const tableName = this.name;\n    // @ts-ignore: Use Chrome's Async Stack Tagging API to allow tracing and simplify debugging for dexie users.\n    const task = debug && typeof console !== 'undefined' && console.createTask && console.createTask(`Dexie: ${mode === 'readonly' ? 'read' : 'write' } ${this.name}`);\n    \n    function checkTableInTransaction(resolve, reject, trans: Transaction) {\n      if (!trans.schema[tableName])\n        throw new exceptions.NotFound(\"Table \" + tableName + \" not part of transaction\");\n      return fn(trans.idbtrans, trans) as Promise<any>;\n    }\n    // Surround all in a microtick scope.\n    // Reason: Browsers (modern Safari + older others)\n    // still as of 2018-10-10 has problems keeping a transaction\n    // alive between micro ticks. Safari because if transaction\n    // is created but not used in same microtick, it will go\n    // away. That specific issue could be solved in DBCore\n    // by opening the transaction just before using it instead.\n    // But older Firefoxes and IE11 (with Promise polyfills)\n    // will still have probs.\n    // The beginMicrotickScope()/endMicrotickScope() works\n    // in cooperation with Dexie.Promise to orchestrate\n    // the micro-ticks in endMicrotickScope() rather than\n    // in native engine.\n    const wasRootExec = beginMicroTickScope();\n    try {\n      let p = trans && trans.db._novip === this.db._novip ?\n        trans === PSD.trans ?\n          trans._promise(mode, checkTableInTransaction, writeLocked) :\n          newScope(() => trans._promise(mode, checkTableInTransaction, writeLocked), { trans: trans, transless: PSD.transless || PSD }) :\n        tempTransaction(this.db, mode, [this.name], checkTableInTransaction);\n      if (task) { // Dexie.debug = true so we trace errors\n        p._consoleTask = task;\n        p = p.catch(err => {\n          console.trace(err);\n          return rejection(err);\n        });\n      }\n      return p;  \n    } finally {\n      if (wasRootExec) endMicroTickScope();\n    }\n  }\n\n  /** Table.get()\n   * \n   * https://dexie.org/docs/Table/Table.get()\n   * \n   **/\n  get(keyOrCrit, cb?) {\n    if (keyOrCrit && keyOrCrit.constructor === Object)\n      return this.where(keyOrCrit as { [key: string]: IndexableType }).first(cb);\n    if (keyOrCrit == null) return rejection(new exceptions.Type(`Invalid argument to Table.get()`));\n\n    return this._trans('readonly', (trans) => {\n      return this.core.get({trans, key: keyOrCrit})\n        .then(res => this.hook.reading.fire(res));\n    }).then(cb);\n  }\n\n  /** Table.where()\n   * \n   * https://dexie.org/docs/Table/Table.where()\n   * \n   **/\n  where(indexOrCrit: string | string[] | { [key: string]: IndexableType }) {\n    if (typeof indexOrCrit === 'string')\n      return new this.db.WhereClause(this, indexOrCrit);\n    if (isArray(indexOrCrit))\n      return new this.db.WhereClause(this, `[${indexOrCrit.join('+')}]`);\n    // indexOrCrit is an object map of {[keyPath]:value} \n    const keyPaths = keys(indexOrCrit);\n    if (keyPaths.length === 1)\n      // Only one critera. This was the easy case:\n      return this\n        .where(keyPaths[0])\n        .equals(indexOrCrit[keyPaths[0]]);\n\n    // Multiple criterias.\n    // Let's try finding a compound index that matches all keyPaths in\n    // arbritary order:\n    const compoundIndex = this.schema.indexes.concat(this.schema.primKey).filter(ix => {\n      if (\n        ix.compound &&\n        keyPaths.every(keyPath => ix.keyPath.indexOf(keyPath) >= 0)) {\n          for (let i=0; i<keyPaths.length; ++i) {\n            if (keyPaths.indexOf(ix.keyPath[i]) === -1) return false;\n          }\n          return true;\n        }\n        return false;\n      }).sort((a,b) => a.keyPath.length - b.keyPath.length)[0];\n            \n    if (compoundIndex && this.db._maxKey !== maxString) {\n      // Cool! We found such compound index\n      // and this browser supports compound indexes (maxKey !== maxString)!\n      const keyPathsInValidOrder = (compoundIndex.keyPath as string[]).slice(0, keyPaths.length);\n      return this\n        .where(keyPathsInValidOrder)\n        .equals(keyPathsInValidOrder.map(kp => indexOrCrit[kp]));\n    }\n\n    if (!compoundIndex && debug) console.warn(\n      `The query ${JSON.stringify(indexOrCrit)} on ${this.name} would benefit from a ` +\n      `compound index [${keyPaths.join('+')}]`);\n\n    // Ok, now let's fallback to finding at least one matching index\n    // and filter the rest.\n    const { idxByName } = this.schema;\n\n    function equals(a, b) {\n      return cmp(a, b) === 0; // Works with all indexable types including binary keys.\n    }\n\n    const [idx, filterFunction] = keyPaths.reduce(([prevIndex, prevFilterFn], keyPath) => {\n      const index = idxByName[keyPath];\n      const value = indexOrCrit[keyPath];\n      return [\n        prevIndex || index, // idx::=Pick index of first matching keypath\n        prevIndex || !index ? // filter::=null if not needed, otherwise combine function filter\n          combine(\n            prevFilterFn,\n            index && index.multi ?\n              x => {\n                const prop = getByKeyPath(x, keyPath);\n                return isArray(prop) && prop.some(item => equals(value, item));\n              } : x => equals(value, getByKeyPath(x, keyPath)))\n          : prevFilterFn\n      ];\n    }, [null, null]);\n\n    return idx ?\n      this.where(idx.name).equals(indexOrCrit[idx.keyPath])\n        .filter(filterFunction) :\n      compoundIndex ?\n        this.filter(filterFunction) : // Has compound but browser bad. Allow filter.\n        this.where(keyPaths).equals(''); // No index at all. Fail lazily with \"[a+b+c] is not indexed\"\n  }\n\n  /** Table.filter()\n   * \n   * https://dexie.org/docs/Table/Table.filter()\n   * \n   **/\n  filter(filterFunction: (obj: any) => boolean) {\n    return this.toCollection().and(filterFunction);\n  }\n\n  /** Table.count()\n   * \n   * https://dexie.org/docs/Table/Table.count()\n   * \n   **/\n  count(thenShortcut?: any) {\n    return this.toCollection().count(thenShortcut);\n  }\n\n  /** Table.offset()\n   * \n   * https://dexie.org/docs/Table/Table.offset()\n   * \n   **/\n  offset(offset: number) {\n    return this.toCollection().offset(offset);\n  }\n\n  /** Table.limit()\n   * \n   * https://dexie.org/docs/Table/Table.limit()\n   * \n   **/\n  limit(numRows: number) {\n    return this.toCollection().limit(numRows);\n  }\n\n  /** Table.each()\n   * \n   * https://dexie.org/docs/Table/Table.each()\n   * \n   **/\n  each(callback: (obj: any, cursor: { key: IndexableType, primaryKey: IndexableType }) => any) {\n    return this.toCollection().each(callback);\n  }\n\n  /** Table.toArray()\n   * \n   * https://dexie.org/docs/Table/Table.toArray()\n   * \n   **/\n  toArray(thenShortcut?: any) {\n    return this.toCollection().toArray(thenShortcut);\n  }\n\n  /** Table.toCollection()\n   * \n   * https://dexie.org/docs/Table/Table.toCollection()\n   * \n   **/\n  toCollection() {\n    return new this.db.Collection(new this.db.WhereClause(this));\n  }\n\n  /** Table.orderBy()\n   * \n   * https://dexie.org/docs/Table/Table.orderBy()\n   * \n   **/\n  orderBy(index: string | string[]) {\n    return new this.db.Collection(\n      new this.db.WhereClause(this, isArray(index) ?\n        `[${index.join('+')}]` :\n        index));\n  }\n\n  /** Table.reverse()\n   * \n   * https://dexie.org/docs/Table/Table.reverse()\n   * \n   **/\n  reverse(): Collection {\n    return this.toCollection().reverse();\n  }\n\n  /** Table.mapToClass()\n   * \n   * https://dexie.org/docs/Table/Table.mapToClass()\n   * \n   **/\n  mapToClass(constructor: Function) {\n    const {db, name: tableName} = this;\n    this.schema.mappedClass = constructor;\n    if (constructor.prototype instanceof Entity) {\n      constructor = class extends (constructor as any) {\n        get db () { return db; }\n        table() { return tableName; }\n      }\n    }\n    // Collect all inherited property names (including method names) by\n    // walking the prototype chain. This is to avoid overwriting them from\n    // database data - so application code can rely on inherited props never\n    // becoming shadowed by database object props.\n    const inheritedProps = new Set<string>();\n    for (let proto = constructor.prototype; proto; proto = getProto(proto)) {\n      Object.getOwnPropertyNames(proto).forEach(propName => inheritedProps.add(propName));\n    }\n  \n    // Now, subscribe to the when(\"reading\") event to make all objects that come out from this table inherit from given class\n    // no matter which method to use for reading (Table.get() or Table.where(...)... )\n    const readHook = (obj: Object) => {\n      if (!obj) return obj; // No valid object. (Value is null or undefined). Return as is.\n      // Create a new object that derives from constructor:\n      const res = Object.create(constructor.prototype);\n      // Clone members (but never those that collide with a property in the prototype\n      // hierchary (MUST BE ABLE TO RELY ON Entity methods and props!)):\n      for (let m in obj) if (!inheritedProps.has(m)) try { res[m] = obj[m]; } catch (_) { }\n      return res;\n    };\n\n    if (this.schema.readHook) {\n      this.hook.reading.unsubscribe(this.schema.readHook);\n    }\n    this.schema.readHook = readHook;\n    this.hook(\"reading\", readHook);\n    return constructor;\n  }\n\n  /** @deprecated */\n  defineClass() {\n    function Class (content){\n      extend(this, content);\n    };\n    return this.mapToClass(Class);\n  }\n\n  /** Table.add()\n   * \n   * https://dexie.org/docs/Table/Table.add()\n   * \n   **/\n  add(obj, key?: IndexableType): PromiseExtended<IndexableType> {\n    const {auto, keyPath} = this.schema.primKey;\n    let objToAdd = obj;\n    if (keyPath && auto) {\n      objToAdd = workaroundForUndefinedPrimKey(keyPath)(obj);\n    }\n    return this._trans('readwrite', trans => {\n      return this.core.mutate({trans, type: 'add', keys: key != null ? [key] : null, values: [objToAdd]});\n    }).then(res => res.numFailures ? Promise.reject(res.failures[0]) : res.lastResult)\n    .then(lastResult => {\n      if (keyPath) {\n        // This part should be here for backward compatibility.\n        // If ever feeling too bad about this, please wait to a new major before removing it,\n        // and document the change thoroughly.\n        try{setByKeyPath(obj, keyPath, lastResult);}catch(_){};\n      }\n      return lastResult;\n    });\n  }\n\n  /** Table.update()\n   * \n   * https://dexie.org/docs/Table/Table.update()\n   * \n   **/\n  update(keyOrObject, modifications: { [keyPath: string]: any; } | ((obj: any, ctx:{value: any, primKey: IndexableType}) => void | boolean)): PromiseExtended<number> {\n    if (typeof keyOrObject === 'object' && !isArray(keyOrObject)) {\n      const key = getByKeyPath(keyOrObject, this.schema.primKey.keyPath);\n      if (key === undefined) return rejection(new exceptions.InvalidArgument(\n        \"Given object does not contain its primary key\"));\n      /*// object to modify. Also modify given object with the modifications:\n      // This part should be here for backward compatibility.\n      // If ever feeling too bad about mutating given object, please wait to a new major before removing it,\n      // and document the change thoroughly. TODO: Document this change!\n      if (!Object.isFrozen(keyOrObject)) try {\n        if (typeof modifications !== \"function\") {\n          keys(modifications).forEach(keyPath => {\n            setByKeyPath(keyOrObject, keyPath, modifications[keyPath]);\n          });\n        } else {\n          // Now since we support function argument, we should have a similar behavior here as well\n          // (as long as we do this mutability stuff on the given object)\n          modifications(keyOrObject, {value: keyOrObject, primKey: key});\n        }\n      } catch {\n        // Maybe given object was frozen.\n        // This part is not essential. Just move on as nothing happened...\n      }*/\n      return this.where(\":id\").equals(key).modify(modifications);\n    } else {\n      // key to modify\n      return this.where(\":id\").equals(keyOrObject).modify(modifications);\n    }\n  }\n\n  /** Table.put()\n   * \n   * https://dexie.org/docs/Table/Table.put()\n   * \n   **/\n  put(obj, key?: IndexableType): PromiseExtended<IndexableType> {\n    const {auto, keyPath} = this.schema.primKey;\n    let objToAdd = obj;\n    if (keyPath && auto) {\n      objToAdd = workaroundForUndefinedPrimKey(keyPath)(obj);\n    }\n    return this._trans(\n      'readwrite',\n      trans => this.core.mutate({trans, type: 'put', values: [objToAdd], keys: key != null ? [key] : null}))\n    .then(res => res.numFailures ? Promise.reject(res.failures[0]) : res.lastResult)\n    .then(lastResult => {\n      if (keyPath) {\n        // This part should be here for backward compatibility.\n        // If ever feeling too bad about this, please wait to a new major before removing it,\n        // and document the change thoroughly.\n        try{setByKeyPath(obj, keyPath, lastResult);}catch(_){};\n      }\n      return lastResult;\n    });\n  }\n\n  /** Table.delete()\n   * \n   * https://dexie.org/docs/Table/Table.delete()\n   * \n   **/\n  delete(key: IndexableType): PromiseExtended<void> {\n    return this._trans('readwrite',\n      trans => this.core.mutate({trans, type: 'delete', keys: [key]}))\n    .then(res => res.numFailures ? Promise.reject(res.failures[0]) : undefined);\n  }\n\n  /** Table.clear()\n   * \n   * https://dexie.org/docs/Table/Table.clear()\n   * \n   **/\n  clear() {\n    return this._trans('readwrite',\n      trans => this.core.mutate({trans, type: 'deleteRange', range: AnyRange}))\n        .then(res => res.numFailures ? Promise.reject(res.failures[0]) : undefined);\n  }\n\n  /** Table.bulkGet()\n   * \n   * https://dexie.org/docs/Table/Table.bulkGet()\n   * \n   * @param keys \n   */\n  bulkGet(keys: IndexableType[]) {\n    return this._trans('readonly', trans => {\n      return this.core.getMany({\n        keys,\n        trans\n      }).then(result => result.map(res => this.hook.reading.fire(res)));\n    });\n  }\n\n  /** Table.bulkAdd()\n   * \n   * https://dexie.org/docs/Table/Table.bulkAdd()\n   * \n   **/\n  bulkAdd(\n    objects: readonly any[],\n    keysOrOptions?: ReadonlyArray<IndexableType> | { allKeys?: boolean },\n    options?: { allKeys?: boolean }\n  ) {    \n    const keys = Array.isArray(keysOrOptions) ? keysOrOptions : undefined;\n    options = options || (keys ? undefined : keysOrOptions as { allKeys?: boolean });\n    const wantResults = options ? options.allKeys : undefined;\n\n    return this._trans('readwrite', trans => {\n      const {auto, keyPath} = this.schema.primKey;\n      if (keyPath && keys)\n        throw new exceptions.InvalidArgument(\"bulkAdd(): keys argument invalid on tables with inbound keys\");\n      if (keys && keys.length !== objects.length)\n        throw new exceptions.InvalidArgument(\"Arguments objects and keys must have the same length\");\n\n      const numObjects = objects.length; // Pick length here to allow garbage collection of objects later\n      let objectsToAdd = keyPath && auto ?\n        objects.map(workaroundForUndefinedPrimKey(keyPath)) :\n        objects;\n      return this.core.mutate(\n        {trans, type: 'add', keys: keys as IndexableType[], values: objectsToAdd, wantResults}\n      )\n        .then(({numFailures, results,lastResult, failures}) => {\n          const result = wantResults ? results : lastResult;\n          if (numFailures === 0) return result;\n          throw new BulkError(\n            `${this.name}.bulkAdd(): ${numFailures} of ${numObjects} operations failed`, failures);\n        });\n    });\n  }\n\n  /** Table.bulkPut()\n   * \n   * https://dexie.org/docs/Table/Table.bulkPut()\n   * \n   **/\n  bulkPut(\n    objects: readonly any[],\n    keysOrOptions?: ReadonlyArray<IndexableType> | { allKeys?: boolean },\n    options?: { allKeys?: boolean }\n  ) {   \n    const keys = Array.isArray(keysOrOptions) ? keysOrOptions : undefined;\n    options = options || (keys ? undefined : keysOrOptions as { allKeys?: boolean });\n    const wantResults = options ? options.allKeys : undefined;\n\n    return this._trans('readwrite', trans => {\n      const {auto, keyPath} = this.schema.primKey;\n      if (keyPath && keys)\n        throw new exceptions.InvalidArgument(\"bulkPut(): keys argument invalid on tables with inbound keys\");\n      if (keys && keys.length !== objects.length)\n        throw new exceptions.InvalidArgument(\"Arguments objects and keys must have the same length\");\n\n      const numObjects = objects.length; // Pick length here to allow garbage collection of objects later\n      let objectsToPut = keyPath && auto ?\n        objects.map(workaroundForUndefinedPrimKey(keyPath)) :\n        objects;\n\n      return this.core.mutate(\n        {trans, type: 'put', keys: keys as IndexableType[], values: objectsToPut, wantResults}\n      )\n        .then(({numFailures, results, lastResult, failures}) => {\n          const result = wantResults ? results : lastResult;\n          if (numFailures === 0) return result;\n          throw new BulkError(\n            `${this.name}.bulkPut(): ${numFailures} of ${numObjects} operations failed`, failures);\n        });\n    });\n  }\n\n  /** Table.bulkUpdate()\n   *\n   * https://dexie.org/docs/Table.Table.bulkUpdate()\n   */\n   bulkUpdate(\n    keysAndChanges: readonly { key: any; changes: UpdateSpec<any> }[]\n  ): PromiseExtended<number> {\n    const coreTable = this.core;\n    const keys = keysAndChanges.map((entry) => entry.key);\n    const changeSpecs = keysAndChanges.map((entry) => entry.changes);\n    const offsetMap: number[] = [];\n    return this._trans('readwrite', (trans) => {\n      return coreTable.getMany({ trans, keys, cache: 'clone' }).then((objs) => {\n        const resultKeys: any[] = [];\n        const resultObjs: any[] = [];\n        keysAndChanges.forEach(({ key, changes }, idx) => {\n          const obj = objs[idx];\n          if (obj) {\n            for (const keyPath of Object.keys(changes)) {\n              const value = changes[keyPath];\n              if (keyPath === this.schema.primKey.keyPath) {\n                if (cmp(value, key) !== 0) {\n                  throw new exceptions.Constraint(\n                    `Cannot update primary key in bulkUpdate()`\n                  );\n                }\n              } else {\n                setByKeyPath(obj, keyPath, value);\n              }\n            }\n            offsetMap.push(idx);\n            resultKeys.push(key);\n            resultObjs.push(obj);\n          }\n        });\n        const numEntries = resultKeys.length;\n        return coreTable\n          .mutate({\n            trans,\n            type: 'put',\n            keys: resultKeys,\n            values: resultObjs,\n            updates: {\n              keys,\n              changeSpecs\n            }\n          })\n          .then(({ numFailures, failures }) => {\n            if (numFailures === 0) return numEntries;\n            // Failure. bulkPut() may have a subset of keys\n            // so we must translate returned 'failutes' into the offsets of given argument:\n            for (const offset of Object.keys(failures)) {\n              const mappedOffset = offsetMap[Number(offset)];\n              if (mappedOffset != null) {\n                const failure = failures[offset];\n                delete failures[offset];\n                failures[mappedOffset] = failure;\n              }\n            }\n            throw new BulkError(\n              `${this.name}.bulkUpdate(): ${numFailures} of ${numEntries} operations failed`,\n              failures\n            );\n          });\n      });\n    });\n  }\n\n  /** Table.bulkDelete()\n   * \n   * https://dexie.org/docs/Table/Table.bulkDelete()\n   * \n   **/\n  bulkDelete(keys: ReadonlyArray<IndexableType>): PromiseExtended<void> {\n    const numKeys = keys.length;\n    return this._trans('readwrite', trans => {\n      return this.core.mutate({trans, type: 'delete', keys: keys as IndexableType[]});\n    }).then(({numFailures, lastResult, failures}) => {\n      if (numFailures === 0) return lastResult;\n      throw new BulkError(\n        `${this.name}.bulkDelete(): ${numFailures} of ${numKeys} operations failed`, failures);\n    });\n  }\n}\n", "import {keys, isArray, asap} from '../functions/utils';\nimport {nop, mirror, reverseStoppableEventChain} from '../functions/chaining-functions';\nimport {exceptions} from '../errors';\n\nexport default function Events(ctx) {\n    var evs = {};\n    var rv = function (eventName, subscriber) {\n        if (subscriber) {\n            // Subscribe. If additional arguments than just the subscriber was provided, forward them as well.\n            var i = arguments.length, args = new Array(i - 1);\n            while (--i) args[i - 1] = arguments[i];\n            evs[eventName].subscribe.apply(null, args);\n            return ctx;\n        } else if (typeof (eventName) === 'string') {\n            // Return interface allowing to fire or unsubscribe from event\n            return evs[eventName];\n        }\n    };\n    rv.addEventType = add;\n    \n    for (var i = 1, l = arguments.length; i < l; ++i) {\n        add(arguments[i]);\n    }\n    \n    return rv;\n\n    function add(eventName, chainFunction, defaultFunction) {\n        if (typeof eventName === 'object') return addConfiguredEvents(eventName);\n        if (!chainFunction) chainFunction = reverseStoppableEventChain;\n        if (!defaultFunction) defaultFunction = nop;\n\n        var context = {\n            subscribers: [],\n            fire: defaultFunction,\n            subscribe: function (cb) {\n                if (context.subscribers.indexOf(cb) === -1) {\n                    context.subscribers.push(cb);\n                    context.fire = chainFunction(context.fire, cb);\n                }\n            },\n            unsubscribe: function (cb) {\n                context.subscribers = context.subscribers.filter(function (fn) { return fn !== cb; });\n                context.fire = context.subscribers.reduce(chainFunction, defaultFunction);\n            }\n        };\n        evs[eventName] = rv[eventName] = context;\n        return context;\n    }\n\n    function addConfiguredEvents(cfg) {\n        // events(this, {reading: [functionChain, nop]});\n        keys(cfg).forEach(function (eventName) {\n            var args = cfg[eventName];\n            if (isArray(args)) {\n                add(eventName, cfg[eventName][0], cfg[eventName][1]);\n            } else if (args === 'asap') {\n                // Rather than approaching event subscription using a functional approach, we here do it in a for-loop where subscriber is executed in its own stack\n                // enabling that any exception that occur wont disturb the initiator and also not nescessary be catched and forgotten.\n                var context = add(eventName, mirror, function fire() {\n                    // Optimazation-safe cloning of arguments into args.\n                    var i = arguments.length, args = new Array(i);\n                    while (i--) args[i] = arguments[i];\n                    // All each subscriber:\n                    context.subscribers.forEach(function (fn) {\n                        asap(function fireEvent() {\n                            fn.apply(null, args);\n                        });\n                    });\n                });\n            } else throw new exceptions.InvalidArgument(\"Invalid event config\");\n        });\n    }\n}\n", "import { arrayToObject, derive } from './utils';\n\n\nexport function makeClassConstructor<TConstructor> (prototype: Object, constructor: Function) {\n  /*const propertyDescriptorMap = arrayToObject(\n    Object.getOwnPropertyNames(prototype),\n    propKey => [propKey, Object.getOwnPropertyDescriptor(prototype, propKey)]);\n\n  // Both derive and clone the prototype.\n  //   derive: So that x instanceof T returns true when T is the class template.\n  //   clone: Optimizes method access a bit (but actually not nescessary)\n  const derivedPrototypeClone = Object.create(prototype, propertyDescriptorMap);\n  derivedPrototypeClone.constructor = constructor;\n  constructor.prototype = derivedPrototypeClone;\n  return constructor as any as TConstructor;*/\n\n  // Keep the above code in case we want to clone AND derive the parent prototype.\n  // Reason would be optimization of property access.\n  // The code below will only create a prototypal inheritance from given constructor function\n  // to given prototype.\n  derive(constructor).from({prototype});\n  return constructor as any as TConstructor;  \n}\n", "import { <PERSON><PERSON> } from '../dexie';\nimport { TableSchema } from '../../public/types/table-schema';\nimport { Transaction } from '../transaction/transaction';\nimport { hookCreating<PERSON>hain, pureFunction<PERSON>hain, nop, mirror, hookUpdating<PERSON>hain, hookDeleting<PERSON>hain } from '../../functions/chaining-functions';\nimport { TableHooks } from '../../public/types/table-hooks';\nimport { Table } from './table';\nimport Events from '../../helpers/Events';\nimport { makeClassConstructor } from '../../functions/make-class-constructor';\n\nexport interface TableConstructor {\n  new (name: string, tableSchema: TableSchema, optionalTrans?: Transaction) : Table;\n  prototype: Table;\n}\n\n/** Generates a Table constructor bound to given Dexie instance.\n * \n * The purpose of having dynamically created constructors, is to allow\n * addons to extend classes for a certain Dexie instance without affecting\n * other db instances.\n */\nexport function createTableConstructor (db: Dexie) {\n  return makeClassConstructor<TableConstructor>(\n    Table.prototype,\n\n    function Table (this: Table, name: string, tableSchema: TableSchema, trans?: Transaction) {\n      this.db = db;\n      this._tx = trans;\n      this.name = name;\n      this.schema = tableSchema;\n      this.hook = db._allTables[name] ? db._allTables[name].hook : Events(null, {\n        \"creating\": [hookCreatingChain, nop],\n        \"reading\": [pureFunctionChain, mirror],\n        \"updating\": [hookUpdatingChain, nop],\n        \"deleting\": [hookDeletingChain, nop]\n      }) as TableHooks;\n    }\n\n  );\n}\n", "import { combine } from \"../../functions/combine\";\nimport { exceptions } from \"../../errors\";\nimport { hasOwn } from \"../../functions/utils\";\nimport { wrap } from \"../../helpers/promise\";\nimport { Collection } from './';\nimport { DBCoreCursor, DBCoreTable, DBCoreTransaction, DBCoreTableSchema, DBCoreRangeType } from '../../public/types/dbcore';\nimport { nop } from '../../functions/chaining-functions';\n\ntype CollectionContext = Collection[\"_ctx\"];\n\nexport function isPlainKeyRange (ctx: CollectionContext, ignoreLimitFilter?: boolean) {\n  return !(ctx.filter || ctx.algorithm || ctx.or) &&\n      (ignoreLimitFilter ? ctx.justLimit : !ctx.replayFilter);\n}    \n\nexport function addFilter(ctx: CollectionContext, fn: Function) {\n  ctx.filter = combine(ctx.filter, fn);\n}\n\nexport function addReplayFilter (ctx: CollectionContext, factory, isLimitFilter?) {\n  var curr = ctx.replayFilter;\n  ctx.replayFilter = curr ? ()=>combine(curr(), factory()) : factory;\n  ctx.justLimit = isLimitFilter && !curr;\n}\n\nexport function addMatchFilter(ctx: CollectionContext, fn) {\n  ctx.isMatch = combine(ctx.isMatch, fn);\n}\n\nexport function getIndexOrStore(ctx: CollectionContext, coreSchema: DBCoreTableSchema) {\n  // TODO: Rewrite this. No need to know ctx.isPrimKey. ctx.index should hold the keypath.\n  // Still, throw if not found!\n  if (ctx.isPrimKey) return coreSchema.primaryKey;\n  const index = coreSchema.getIndexByKeyPath(ctx.index);\n  if (!index) throw new exceptions.Schema(\"KeyPath \" + ctx.index + \" on object store \" + coreSchema.name + \" is not indexed\");\n  return index;\n}\n\nexport function openCursor(ctx: CollectionContext, coreTable: DBCoreTable, trans: DBCoreTransaction) {\n  const index = getIndexOrStore(ctx, coreTable.schema);\n  return coreTable.openCursor({\n    trans,\n    values: !ctx.keysOnly,\n    reverse: ctx.dir === 'prev',\n    unique: !!ctx.unique,\n    query: {\n      index, \n      range: ctx.range\n    }\n  });\n}\n\nexport function iter (\n  ctx: CollectionContext, \n  fn: (item, cursor: DBCoreCursor, advance: Function)=>void,\n  coreTrans: DBCoreTransaction,\n  coreTable: DBCoreTable): Promise<any>\n{\n  const filter = ctx.replayFilter ? combine(ctx.filter, ctx.replayFilter()) : ctx.filter;\n  if (!ctx.or) {\n      return iterate(\n        openCursor(ctx, coreTable, coreTrans),\n        combine(ctx.algorithm, filter), fn, !ctx.keysOnly && ctx.valueMapper);\n  } else {\n      const set = {};\n\n      const union = (item: any, cursor: DBCoreCursor, advance) => {\n          if (!filter || filter(cursor, advance, result=>cursor.stop(result), err => cursor.fail(err))) {\n              var primaryKey = cursor.primaryKey;\n              var key = '' + primaryKey;\n              if (key === '[object ArrayBuffer]') key = '' + new Uint8Array(primaryKey);\n              if (!hasOwn(set, key)) {\n                  set[key] = true;\n                  fn(item, cursor, advance);\n              }\n          }\n      }\n\n      return Promise.all([\n        ctx.or._iterate(union, coreTrans),\n        iterate(openCursor(ctx, coreTable, coreTrans), ctx.algorithm, union, !ctx.keysOnly && ctx.valueMapper)\n      ]);\n  }\n}\n\nfunction iterate(cursorPromise: Promise<DBCoreCursor>, filter, fn, valueMapper): Promise<any> {\n  \n  // Apply valueMapper (hook('reading') or mappped class)\n  var mappedFn = valueMapper ? (x,c,a) => fn(valueMapper(x),c,a) : fn;\n  // Wrap fn with PSD and microtick stuff from Promise.\n  var wrappedFn = wrap(mappedFn);\n  \n  return cursorPromise.then(cursor => {\n    if (cursor) {\n      return cursor.start(()=>{\n        var c = ()=>cursor.continue();\n        if (!filter || filter(cursor, advancer => c = advancer, val=>{cursor.stop(val);c=nop}, e => {cursor.fail(e);c = nop;}))\n          wrappedFn(cursor.value, cursor, advancer => c = advancer);\n        c();\n      });\n    }\n  });\n}\n", "import { isArray } from \"../functions/utils\";\nimport { PropModSpec } from \"../public/types/prop-modification\";\n\n/** Consistent change propagation across offline synced data.\n * \n * This class is executed client- and server side on sync, making\n * an operation consistent across sync for full consistency and accuracy.\n * \n * Example: An object represents a bank account with a balance.\n * One offline user adds $ 1.00 to the balance.\n * Another user (online) adds $ 2.00 to the balance.\n * When first user syncs, the balance becomes the sum of every operation (3.00).\n * \n * -- initial: balance is 0\n * 1. db.bankAccounts.update(1, { balance: new ProdModification({add: 100})}) // user 1 (offline)\n * 2. db.bankAccounts.update(1, { balance: new ProdModification({add: 200})}) // user 2 (online)\n * -- before user 1 syncs, balance is 200 (representing money with integers * 100 to avoid rounding issues)\n * <user 1 syncs>\n * -- balance is 300\n * \n * When new operations are added, they need to be added to:\n * 1. PropModSpec interface\n * 2. Here in PropModification with the logic they represent\n * 3. (Optionally) a sugar function for it, such as const mathAdd = (amount: number | BigInt) => new PropModification({mathAdd: amount})\n */\nexport class PropModification {\n  [\"@@propmod\"]: PropModSpec;\n  execute(value: any): any {\n    const spec = this[\"@@propmod\"]\n    // add (mathematical or set-wise)\n    if (spec.add !== undefined) {\n      const term = spec.add;\n      // Set-addition on array representing a set of primitive types (strings, numbers)\n      if (isArray(term)) {\n        return [...(isArray(value) ? value : []), ...term].sort();\n      }\n      // Mathematical addition:\n      if (typeof term === 'number') return (Number(value) || 0) + term; // if value is not convertible to number, return 0 + term\n      if (typeof term === 'bigint') {\n        try {\n          return BigInt(value) + term;\n        } catch {\n          return BigInt(0) + term; // Unlike Number(value) that can return NaN, BigInt(value) throws if value is not BigInt, Number or numeric string\n        }\n      }\n      throw new TypeError(`Invalid term ${term}`);\n    }\n\n    // remove (mathematical or set-wise)\n    if (spec.remove !== undefined) {\n      const subtrahend = spec.remove;\n      // Set-addition on array representing a set of primitive types (strings, numbers)\n      if (isArray(subtrahend)) {\n        return isArray(value) ? value.filter(item => !subtrahend.includes(item)).sort() : [];\n      }        \n      // Mathematical addition:\n      if (typeof subtrahend === 'number') return Number(value) - subtrahend;\n      if (typeof subtrahend === 'bigint') {\n        try {\n          return BigInt(value) - subtrahend;\n        } catch {\n          return BigInt(0) - subtrahend; // Unlike Number(value) that can return NaN, BigInt(value) throws if value is not BigInt, Number or numeric string\n        }\n      }\n      throw new TypeError(`Invalid subtrahend ${subtrahend}`);\n    }\n\n    // Replace a prefix:\n    const prefixToReplace = spec.replacePrefix?.[0];\n    if (prefixToReplace && typeof value === 'string' && value.startsWith(prefixToReplace)) {\n      return spec.replacePrefix[1] + value.substring(prefixToReplace.length);\n    }\n    return value;\n  }\n\n  constructor(spec: PropModSpec) {\n    this[\"@@propmod\"] = spec;\n  }\n}\n", "import { Collection as ICollection } from \"../../public/types/collection\";\nimport { <PERSON><PERSON> } from \"../dexie\";\nimport { Table } from \"../table\";\nimport { IndexableType, IndexableTypeArrayReadonly } from \"../../public/types/indexable-type\";\nimport { PromiseExtended } from \"../../public/types/promise-extended\";\nimport { iter, isPlainKeyRange, getIndexOrStore, addReplayFilter, addFilter, addMatchFilter } from \"./collection-helpers\";\nimport { rejection } from \"../../helpers/promise\";\nimport { combine } from \"../../functions/combine\";\nimport { extend, hasOwn, deepClone, keys, setByKeyPath, getByKeyPath } from \"../../functions/utils\";\nimport { ModifyError } from \"../../errors\";\nimport { ThenShortcut } from \"../../public/types/then-shortcut\";\nimport { Transaction } from '../transaction';\nimport { DBCoreCursor, DBCoreTransaction, DBCoreRangeType, DBCoreMutateResponse, DBCoreKeyRange } from '../../public/types/dbcore';\nimport { cmp } from \"../../functions/cmp\";\nimport { PropModification } from \"../../helpers/prop-modification\";\nimport { UpdateSpec } from \"../../public/types/update-spec\";\n\n/** class Collection\n * \n * https://dexie.org/docs/Collection/Collection\n */\nexport class Collection implements ICollection {\n  db: Dexie;\n  _ctx: {\n    table: Table;\n    index?: string | null;\n    isPrimKey?: boolean;\n    range: DBCoreKeyRange;\n    keysOnly: boolean;\n    dir: \"next\" | \"prev\";\n    unique: \"\" | \"unique\";\n    algorithm?: Function | null;\n    filter?: Function | null;\n    replayFilter: Function | null;\n    justLimit: boolean; // True if a replayFilter is just a filter that performs a \"limit\" operation (or none at all)\n    isMatch: Function | null;\n    offset: number,\n    limit: number,\n    error: any, // If set, any promise must be rejected with this error\n    or: Collection,\n    valueMapper: (any) => any\n  }\n  \n  _ondirectionchange?: Function;\n\n  _read<T>(fn: (idbtrans: IDBTransaction, dxTrans: Transaction) => PromiseLike<T>, cb?): PromiseExtended<T> {\n    var ctx = this._ctx;\n    return ctx.error ?\n      ctx.table._trans(null, rejection.bind(null, ctx.error)) :\n      ctx.table._trans('readonly', fn).then(cb);\n  }\n\n  _write<T>(fn: (idbtrans: IDBTransaction, dxTrans: Transaction) => PromiseLike<T>): PromiseExtended<T> {\n    var ctx = this._ctx;\n    return ctx.error ?\n      ctx.table._trans(null, rejection.bind(null, ctx.error)) :\n      ctx.table._trans('readwrite', fn, \"locked\"); // When doing write operations on collections, always lock the operation so that upcoming operations gets queued.\n  }\n\n  _addAlgorithm(fn) {\n    var ctx = this._ctx;\n    ctx.algorithm = combine(ctx.algorithm, fn);\n  }\n\n  _iterate(\n    fn: (item, cursor: DBCoreCursor, advance: Function) => void,\n    coreTrans: DBCoreTransaction) : Promise<any>\n  {\n    return iter(this._ctx, fn, coreTrans, this._ctx.table.core);\n  }\n\n  /** Collection.clone()\n   * \n   * https://dexie.org/docs/Collection/Collection.clone()\n   * \n   **/\n  clone(props?): this {\n    var rv = Object.create(this.constructor.prototype),\n      ctx = Object.create(this._ctx);\n    if (props) extend(ctx, props);\n    rv._ctx = ctx;\n    return rv;\n  }\n\n  /** Collection.raw()\n   * \n   * https://dexie.org/docs/Collection/Collection.raw()\n   * \n   **/\n  raw(): this {\n    this._ctx.valueMapper = null;\n    return this;\n  }\n\n  /** Collection.each()\n   * \n   * https://dexie.org/docs/Collection/Collection.each()\n   * \n   **/\n  each(fn: (obj, cursor: DBCoreCursor) => any): PromiseExtended<void> {\n    var ctx = this._ctx;\n\n    return this._read(trans => iter(ctx, fn, trans, ctx.table.core));\n  }\n\n  /** Collection.count()\n   * \n   * https://dexie.org/docs/Collection/Collection.count()\n   * \n   **/\n  count(cb?) {\n    return this._read(trans => {\n      const ctx = this._ctx;\n      const coreTable = ctx.table.core;\n      if (isPlainKeyRange(ctx, true)) {\n        // This is a plain key range. We can use the count() method if the index.\n        return coreTable.count({\n          trans,\n          query: {\n            index: getIndexOrStore(ctx, coreTable.schema),\n            range: ctx.range\n          }\n        }).then(count => Math.min(count, ctx.limit));\n      } else {\n        // Algorithms, filters or expressions are applied. Need to count manually.\n        var count = 0;\n        return iter(ctx, () => { ++count; return false; }, trans, coreTable)\n        .then(()=>count);\n      }\n    }).then(cb);\n  }\n\n  /** Collection.sortBy()\n   * \n   * https://dexie.org/docs/Collection/Collection.sortBy()\n   * \n   **/\n  sortBy(keyPath: string): PromiseExtended<any[]>;\n  sortBy<R>(keyPath: string, thenShortcut: ThenShortcut<any[], R>) : PromiseExtended<R>;\n  sortBy(keyPath: string, cb?: ThenShortcut<any[], any>) {\n    const parts = keyPath.split('.').reverse(),\n      lastPart = parts[0],\n      lastIndex = parts.length - 1;\n    function getval(obj, i) {\n      if (i) return getval(obj[parts[i]], i - 1);\n      return obj[lastPart];\n    }\n    var order = this._ctx.dir === \"next\" ? 1 : -1;\n\n    function sorter(a, b) {\n      var aVal = getval(a, lastIndex),\n        bVal = getval(b, lastIndex);\n      return cmp(aVal, bVal) * order;\n    }\n    return this.toArray(function (a) {\n      return a.sort(sorter);\n    }).then(cb);\n  }\n\n  /** Collection.toArray()\n   * \n   * https://dexie.org/docs/Collection/Collection.toArray()\n   * \n   **/\n  toArray(cb?): PromiseExtended<any[]> {\n    return this._read(trans => {\n      var ctx = this._ctx;\n      if (ctx.dir === 'next' && isPlainKeyRange(ctx, true) && ctx.limit > 0) {\n        // Special optimation if we could use IDBObjectStore.getAll() or\n        // IDBKeyRange.getAll():\n        const {valueMapper} = ctx;\n        const index = getIndexOrStore(ctx, ctx.table.core.schema);\n        return ctx.table.core.query({\n          trans,\n          limit: ctx.limit,\n          values: true,\n          query: {\n            index,\n            range: ctx.range\n          }\n        }).then(({result}) => valueMapper ? result.map(valueMapper) : result);\n      } else {\n        // Getting array through a cursor.\n        const a = [];\n        return iter(ctx, item => a.push(item), trans, ctx.table.core).then(()=>a);\n      }\n    }, cb);\n  }\n\n  /** Collection.offset()\n   * \n   * https://dexie.org/docs/Collection/Collection.offset()\n   * \n   **/\n  offset(offset: number) : Collection{\n    var ctx = this._ctx;\n    if (offset <= 0) return this;\n    ctx.offset += offset; // For count()\n    if (isPlainKeyRange(ctx)) {\n      addReplayFilter(ctx, () => {\n        var offsetLeft = offset;\n        return (cursor, advance) => {\n          if (offsetLeft === 0) return true;\n          if (offsetLeft === 1) { --offsetLeft; return false; }\n          advance(() => {\n            cursor.advance(offsetLeft);\n            offsetLeft = 0;\n          });\n          return false;\n        };\n      });\n    } else {\n      addReplayFilter(ctx, () => {\n        var offsetLeft = offset;\n        return () => (--offsetLeft < 0);\n      });\n    }\n    return this;\n  }\n\n  /** Collection.limit()\n   * \n   * https://dexie.org/docs/Collection/Collection.limit()\n   * \n   **/\n  limit(numRows: number) : Collection {\n    this._ctx.limit = Math.min(this._ctx.limit, numRows); // For count()\n    addReplayFilter(this._ctx, () => {\n      var rowsLeft = numRows;\n      return function (cursor, advance, resolve) {\n        if (--rowsLeft <= 0) advance(resolve); // Stop after this item has been included\n        return rowsLeft >= 0; // If numRows is already below 0, return false because then 0 was passed to numRows initially. Otherwise we wouldnt come here.\n      };\n    }, true);\n    return this;\n  }\n\n  /** Collection.until()\n   * \n   * https://dexie.org/docs/Collection/Collection.until()\n   * \n   **/\n  until(filterFunction: (x) => boolean, bIncludeStopEntry?) {\n    addFilter(this._ctx, function (cursor, advance, resolve) {\n      if (filterFunction(cursor.value)) {\n        advance(resolve);\n        return bIncludeStopEntry;\n      } else {\n        return true;\n      }\n    });\n    return this;\n  }\n\n  /** Collection.first()\n   * \n   * https://dexie.org/docs/Collection/Collection.first()\n   * \n   **/\n  first(cb?) {\n    return this.limit(1).toArray(function (a) { return a[0]; }).then(cb);\n  }\n\n  /** Collection.last()\n   * \n   * https://dexie.org/docs/Collection/Collection.last()\n   * \n   **/\n  last(cb?) {\n    return this.reverse().first(cb);\n  }\n\n  /** Collection.filter()\n   * \n   * https://dexie.org/docs/Collection/Collection.filter()\n   * \n   **/\n  filter(filterFunction: (x) => boolean): Collection {\n    /// <param name=\"jsFunctionFilter\" type=\"Function\">function(val){return true/false}</param>\n    addFilter(this._ctx, function (cursor) {\n      return filterFunction(cursor.value);\n    });\n    // match filters not used in Dexie.js but can be used by 3rd part libraries to test a\n    // collection for a match without querying DB. Used by Dexie.Observable.\n    addMatchFilter(this._ctx, filterFunction);\n    return this;\n  }\n\n  /** Collection.and()\n   * \n   * https://dexie.org/docs/Collection/Collection.and()\n   * \n   **/\n  and(filter: (x) => boolean) {\n    return this.filter(filter);\n  }\n\n  /** Collection.or()\n   * \n   * https://dexie.org/docs/Collection/Collection.or()\n   * \n   **/\n  or(indexName: string) {\n    return new this.db.WhereClause(this._ctx.table, indexName, this);\n  }\n\n  /** Collection.reverse()\n   * \n   * https://dexie.org/docs/Collection/Collection.reverse()\n   * \n   **/\n  reverse() {\n    this._ctx.dir = (this._ctx.dir === \"prev\" ? \"next\" : \"prev\");\n    if (this._ondirectionchange) this._ondirectionchange(this._ctx.dir);\n    return this;\n  }\n\n  /** Collection.desc()\n   * \n   * https://dexie.org/docs/Collection/Collection.desc()\n   * \n   **/\n  desc() {\n    return this.reverse();\n  }\n\n  /** Collection.eachKey()\n   * \n   * https://dexie.org/docs/Collection/Collection.eachKey()\n   * \n   **/\n  eachKey(cb?) {\n    var ctx = this._ctx;\n    ctx.keysOnly = !ctx.isMatch;\n    return this.each(function (val, cursor) { cb(cursor.key, cursor); });\n  }\n\n  /** Collection.eachUniqueKey()\n   * \n   * https://dexie.org/docs/Collection/Collection.eachUniqueKey()\n   * \n   **/\n  eachUniqueKey(cb?) {\n    this._ctx.unique = \"unique\";\n    return this.eachKey(cb);\n  }\n\n  /** Collection.eachPrimaryKey()\n   * \n   * https://dexie.org/docs/Collection/Collection.eachPrimaryKey()\n   * \n   **/\n  eachPrimaryKey(cb?) {\n    var ctx = this._ctx;\n    ctx.keysOnly = !ctx.isMatch;\n    return this.each(function (val, cursor) { cb(cursor.primaryKey, cursor); });\n  }\n\n  /** Collection.keys()\n   * \n   * https://dexie.org/docs/Collection/Collection.keys()\n   * \n   **/\n  keys(cb?) {\n    var ctx = this._ctx;\n    ctx.keysOnly = !ctx.isMatch;\n    var a = [];\n    return this.each(function (item, cursor) {\n      a.push(cursor.key);\n    }).then(function () {\n      return a;\n    }).then(cb);\n  }\n\n  /** Collection.primaryKeys()\n   * \n   * https://dexie.org/docs/Collection/Collection.primaryKeys()\n   * \n   **/\n  primaryKeys(cb?) : PromiseExtended<IndexableType[]> {\n    var ctx = this._ctx;\n    if (ctx.dir === 'next' && isPlainKeyRange(ctx, true) && ctx.limit > 0) {\n      // Special optimation if we could use IDBObjectStore.getAllKeys() or\n      // IDBKeyRange.getAllKeys():\n      return this._read(trans => {\n        var index = getIndexOrStore(ctx, ctx.table.core.schema);\n        return ctx.table.core.query({\n          trans,\n          values: false,\n          limit: ctx.limit,\n          query: {\n            index,\n            range: ctx.range\n          }});\n      }).then(({result})=>result).then(cb);\n    }\n    ctx.keysOnly = !ctx.isMatch;\n    var a = [];\n    return this.each(function (item, cursor) {\n      a.push(cursor.primaryKey);\n    }).then(function () {\n      return a;\n    }).then(cb);\n  }\n\n  /** Collection.uniqueKeys()\n   * \n   * https://dexie.org/docs/Collection/Collection.uniqueKeys()\n   * \n   **/\n  uniqueKeys(cb?) {\n    this._ctx.unique = \"unique\";\n    return this.keys(cb);\n  }\n\n  /** Collection.firstKey()\n   * \n   * https://dexie.org/docs/Collection/Collection.firstKey()\n   * \n   **/\n  firstKey(cb?) {\n    return this.limit(1).keys(function (a) { return a[0]; }).then(cb);\n  }\n\n  /** Collection.lastKey()\n   * \n   * https://dexie.org/docs/Collection/Collection.lastKey()\n   * \n   **/\n  lastKey(cb?) {\n    return this.reverse().firstKey(cb);\n  }\n\n  /** Collection.distinct()\n   * \n   * https://dexie.org/docs/Collection/Collection.distinct()\n   * \n   **/\n  distinct() {\n    var ctx = this._ctx,\n      idx = ctx.index && ctx.table.schema.idxByName[ctx.index];\n    if (!idx || !idx.multi) return this; // distinct() only makes differencies on multiEntry indexes.\n    var set = {};\n    addFilter(this._ctx, function (cursor: DBCoreCursor) {\n      var strKey = cursor.primaryKey.toString(); // Converts any Date to String, String to String, Number to String and Array to comma-separated string\n      var found = hasOwn(set, strKey);\n      set[strKey] = true;\n      return !found;\n    });\n    return this;\n  }\n\n  //\n  // Methods that mutate storage\n  //\n\n  /** Collection.modify()\n   * \n   * https://dexie.org/docs/Collection/Collection.modify()\n   * \n   **/\n  modify(changes: UpdateSpec<any> | ((obj: any, ctx:{value: any, primKey: IndexableType}) => void | boolean)): PromiseExtended<number> {\n    var ctx = this._ctx;\n    return this._write(trans => {\n      var modifyer: (obj: any, ctx:{value: any, primKey: IndexableType}) => void | boolean\n      if (typeof changes === 'function') {\n        // Changes is a function that may update, add or delete propterties or even require a deletion the object itself (delete this.item)\n        modifyer = changes as (obj: any, ctx:{value: any, primKey: IndexableType}) => void | boolean;\n      } else {\n        // changes is a set of {keyPath: value} and no one is listening to the updating hook.\n        var keyPaths = keys(changes);\n        var numKeys = keyPaths.length;\n        modifyer = function (item) {\n          let anythingModified = false;\n          for (let i = 0; i < numKeys; ++i) {\n            let keyPath = keyPaths[i];\n            let val = changes[keyPath];\n            let origVal = getByKeyPath(item, keyPath);\n\n            if (val instanceof PropModification) {\n              setByKeyPath(item, keyPath, val.execute(origVal));\n              anythingModified = true;\n            } else if (origVal !== val) {\n              setByKeyPath(item, keyPath, val); // Adding {keyPath: undefined} means that the keyPath should be deleted. Handled by setByKeyPath\n              anythingModified = true;\n            }\n          }\n          return anythingModified;\n        };\n      }\n\n      const coreTable = ctx.table.core;\n      const {outbound, extractKey} = coreTable.schema.primaryKey;\n      let limit = 200;\n      const modifyChunkSize = this.db._options.modifyChunkSize;\n      if (modifyChunkSize) {\n        if (typeof modifyChunkSize == 'object') {\n          limit = modifyChunkSize[coreTable.name] || modifyChunkSize['*'] || 200;\n        } else {\n          limit = modifyChunkSize;\n        }\n      }\n      const totalFailures = [];\n      let successCount = 0;\n      const failedKeys: IndexableType[] = [];\n      const applyMutateResult = (expectedCount: number, res: DBCoreMutateResponse) => {\n        const {failures, numFailures} = res;\n        successCount += expectedCount - numFailures;\n        for (let pos of keys(failures)) {\n          totalFailures.push(failures[pos]);\n        }\n      }\n      return this.clone().primaryKeys().then(keys => {\n        const criteria = isPlainKeyRange(ctx) &&\n          ctx.limit === Infinity &&\n          (typeof changes !== 'function' || changes === deleteCallback) && {\n            index: ctx.index,\n            range: ctx.range\n          };\n\n        const nextChunk = (offset: number) => {\n          const count = Math.min(limit, keys.length - offset);\n          return coreTable.getMany({\n            trans,\n            keys: keys.slice(offset, offset + count),\n            cache: \"immutable\" // Optimize for 2 things:\n            // 1) observability-middleware can track changes better.\n            // 2) hooks middleware don't have to query the existing values again when tracking changes.\n            // We can use \"immutable\" because we promise to not touch the values we retrieve here!\n          }).then(values => {\n            const addValues = [];\n            const putValues = [];\n            const putKeys = outbound ? [] : null;\n            const deleteKeys = [];\n            for (let i=0; i<count; ++i) {\n              const origValue = values[i];\n              const ctx = {\n                value: deepClone(origValue),\n                primKey: keys[offset+i]\n              };\n              if (modifyer.call(ctx, ctx.value, ctx) !== false) {\n                if (ctx.value == null) {\n                  // Deleted\n                  deleteKeys.push(keys[offset+i]);\n                } else if (!outbound && cmp(extractKey(origValue), extractKey(ctx.value)) !== 0) {\n                  // Changed primary key of inbound\n                  deleteKeys.push(keys[offset+i]);\n                  addValues.push(ctx.value)\n                } else {\n                  // Changed value\n                  putValues.push(ctx.value);\n                  if (outbound) putKeys.push(keys[offset+i]);\n                }\n              }\n            }\n\n            return Promise.resolve(addValues.length > 0 &&\n              coreTable.mutate({trans, type: 'add', values: addValues})\n                .then(res => {\n                  for (let pos in res.failures) {\n                    // Remove from deleteKeys the key of the object that failed to change its primary key\n                    deleteKeys.splice(parseInt(pos), 1);\n                  }\n                  applyMutateResult(addValues.length, res);\n                })\n            ).then(()=>(putValues.length > 0 || (criteria && typeof changes === 'object')) &&\n                coreTable.mutate({\n                  trans,\n                  type: 'put',\n                  keys: putKeys,\n                  values: putValues,\n                  criteria,\n                  changeSpec: typeof changes !== 'function'\n                    && changes,\n                  isAdditionalChunk: offset > 0\n                }).then(res=>applyMutateResult(putValues.length, res))\n            ).then(()=>(deleteKeys.length > 0 || (criteria && changes === deleteCallback)) &&\n                coreTable.mutate({\n                  trans,\n                  type: 'delete',\n                  keys: deleteKeys,\n                  criteria,\n                  isAdditionalChunk: offset > 0\n                }).then(res=>applyMutateResult(deleteKeys.length, res))\n            ).then(()=>{\n              return keys.length > offset + count && nextChunk(offset + limit);\n            });\n          });\n        }\n\n        return nextChunk(0).then(()=>{\n          if (totalFailures.length > 0)\n            throw new ModifyError(\"Error modifying one or more objects\", totalFailures, successCount, failedKeys as IndexableTypeArrayReadonly);\n\n          return keys.length;\n        });\n      });\n\n    });\n  }\n\n  /** Collection.delete()\n   * \n   * https://dexie.org/docs/Collection/Collection.delete()\n   * \n   **/\n  delete() : PromiseExtended<number> {\n    var ctx = this._ctx,\n      range = ctx.range;\n      //deletingHook = ctx.table.hook.deleting.fire,\n      //hasDeleteHook = deletingHook !== nop;\n    if (isPlainKeyRange(ctx) &&\n      (ctx.isPrimKey || range.type === DBCoreRangeType.Any)) // if no range, we'll use clear().\n    {\n      // May use IDBObjectStore.delete(IDBKeyRange) in this case (Issue #208)\n      // For chromium, this is the way most optimized version.\n      // For IE/Edge, this could hang the indexedDB engine and make operating system instable\n      // (https://gist.github.com/dfahlander/5a39328f029de18222cf2125d56c38f7)\n      return this._write(trans => {\n        // Our API contract is to return a count of deleted items, so we have to count() before delete().\n        const {primaryKey} = ctx.table.core.schema;\n        const coreRange = range;\n        return ctx.table.core.count({trans, query: {index: primaryKey, range: coreRange}}).then(count => {\n          return ctx.table.core.mutate({trans, type: 'deleteRange', range: coreRange})\n          .then(({failures, lastResult, results, numFailures}) => {\n            if (numFailures) throw new ModifyError(\"Could not delete some values\",\n              Object.keys(failures).map(pos => failures[pos]),\n              count - numFailures);\n            return count - numFailures;\n          });\n        });\n      });\n    }\n\n    return this.modify(deleteCallback);\n  }\n}\n\nconst deleteCallback = (value, ctx) => ctx.value = null;\n", "import { <PERSON><PERSON> } from '../../classes/dexie';\nimport { makeClassConstructor } from '../../functions/make-class-constructor';\nimport { Collection } from './collection';\nimport { WhereClause } from '../where-clause/where-clause';\nimport { AnyRange } from '../../dbcore/keyrange';\nimport { DBCoreKeyRange } from '../../public/types/dbcore';\nimport { mirror } from '../../functions/chaining-functions';\n\n/** Constructs a Collection instance. */\nexport interface CollectionConstructor {\n  new(whereClause?: WhereClause | null, keyRangeGenerator?: () => DBCoreKeyRange): Collection;\n  prototype: Collection;\n}\n\n/** Generates a Collection constructor bound to given Dexie instance.\n * \n * The purpose of having dynamically created constructors, is to allow\n * addons to extend classes for a certain Dexie instance without affecting\n * other db instances.\n */\nexport function createCollectionConstructor(db: Dexie) {\n  return makeClassConstructor<CollectionConstructor>(\n    Collection.prototype,\n\n    function Collection(\n      this: Collection,\n      whereClause?: WhereClause | null,\n      keyRangeGenerator?: () => DBCoreKeyRange)\n    {\n      this.db = db;\n      let keyRange = AnyRange, error = null;\n      if (keyRangeGenerator) try {\n        keyRange = keyRangeGenerator();\n      } catch (ex) {\n        error = ex;\n      }\n\n      const whereCtx = whereClause._ctx;\n      const table = whereCtx.table;\n      const readingHook = table.hook.reading.fire;\n      this._ctx = {\n        table: table,\n        index: whereCtx.index,\n        isPrimKey: (!whereCtx.index || (table.schema.primKey.keyPath && whereCtx.index === table.schema.primKey.name)),\n        range: keyRange,\n        keysOnly: false,\n        dir: \"next\",\n        unique: \"\",\n        algorithm: null,\n        filter: null,\n        replayFilter: null,\n        justLimit: true, // True if a replayFilter is just a filter that performs a \"limit\" operation (or none at all)\n        isMatch: null,\n        offset: 0,\n        limit: Infinity,\n        error: error, // If set, any promise must be rejected with this error\n        or: whereCtx.or,\n        valueMapper: readingHook !== mirror ? readingHook : null\n      };\n    }\n  );\n}\n", "import { IndexableType } from '../public/types/indexable-type';\n\nexport function simpleCompare(a, b) {\n  return a < b ? -1 : a === b ? 0 : 1;\n}\n\nexport function simpleCompareReverse(a, b) {\n  return a > b ? -1 : a === b ? 0 : 1;\n}\n", "import { WhereClause } from './where-clause';\nimport { Collection } from '../collection';\nimport { STRING_EXPECTED } from '../../globals/constants';\nimport { simpleCompare, simpleCompareReverse } from '../../functions/compare-functions';\nimport { IndexableType } from '../../public';\nimport { DBCoreKeyRange, DBCoreRangeType } from '../../public/types/dbcore';\n\nexport function fail(collectionOrWhereClause: Collection | WhereClause, err, T?) {\n  var collection = collectionOrWhereClause instanceof WhereClause ?\n      new collectionOrWhereClause.Collection (collectionOrWhereClause) :\n      collectionOrWhereClause;\n      \n  collection._ctx.error = T ? new T(err) : new TypeError(err);\n  return collection;\n}\n\nexport function emptyCollection(whereClause: WhereClause) {\n  return new whereClause.Collection (whereClause, () => rangeEqual(\"\")).limit(0);\n}\n\nexport function upperFactory(dir: 'next' | 'prev') {\n  return dir === \"next\" ?\n    (s: string) => s.toUpperCase() :\n    (s: string) => s.toLowerCase();\n}\n\nexport function lowerFactory(dir: 'next' | 'prev') {\n  return dir === \"next\" ?\n    (s: string) => s.toLowerCase() :\n    (s: string) => s.toUpperCase();\n}\n\nexport function nextCasing(key, lowerKey, upperNeedle, lowerNeedle, cmp, dir) {\n  var length = Math.min(key.length, lowerNeedle.length);\n  var llp = -1;\n  for (var i = 0; i < length; ++i) {\n      var lwrKeyChar = lowerKey[i];\n      if (lwrKeyChar !== lowerNeedle[i]) {\n          if (cmp(key[i], upperNeedle[i]) < 0) return key.substr(0, i) + upperNeedle[i] + upperNeedle.substr(i + 1);\n          if (cmp(key[i], lowerNeedle[i]) < 0) return key.substr(0, i) + lowerNeedle[i] + upperNeedle.substr(i + 1);\n          if (llp >= 0) return key.substr(0, llp) + lowerKey[llp] + upperNeedle.substr(llp + 1);\n          return null;\n      }\n      if (cmp(key[i], lwrKeyChar) < 0) llp = i;\n  }\n  if (length < lowerNeedle.length && dir === \"next\") return key + upperNeedle.substr(key.length);\n  if (length < key.length && dir === \"prev\") return key.substr(0, upperNeedle.length);\n  return (llp < 0 ? null : key.substr(0, llp) + lowerNeedle[llp] + upperNeedle.substr(llp + 1));\n}\n\nexport function addIgnoreCaseAlgorithm(whereClause: WhereClause, match, needles, suffix) {\n  /// <param name=\"needles\" type=\"Array\" elementType=\"String\"></param>\n  var upper, lower, compare, upperNeedles, lowerNeedles, direction, nextKeySuffix,\n      needlesLen = needles.length;\n  if (!needles.every(s => typeof s === 'string')) {\n      return fail(whereClause, STRING_EXPECTED);\n  }\n  function initDirection(dir) {\n      upper = upperFactory(dir);\n      lower = lowerFactory(dir);\n      compare = (dir === \"next\" ? simpleCompare : simpleCompareReverse);\n      var needleBounds = needles.map(function (needle){\n          return {lower: lower(needle), upper: upper(needle)};\n      }).sort(function(a,b) {\n          return compare(a.lower, b.lower);\n      });\n      upperNeedles = needleBounds.map(function (nb){ return nb.upper; });\n      lowerNeedles = needleBounds.map(function (nb){ return nb.lower; });\n      direction = dir;\n      nextKeySuffix = (dir === \"next\" ? \"\" : suffix);\n  }\n  initDirection(\"next\");\n\n  var c = new whereClause.Collection (\n      whereClause,\n      ()=>createRange(upperNeedles[0], lowerNeedles[needlesLen-1] + suffix)\n  );\n\n  c._ondirectionchange = function (direction) {\n      // This event onlys occur before filter is called the first time.\n      initDirection(direction);\n  };\n\n  var firstPossibleNeedle = 0;\n\n  c._addAlgorithm(function (cursor, advance, resolve) {\n      /// <param name=\"cursor\" type=\"IDBCursor\"></param>\n      /// <param name=\"advance\" type=\"Function\"></param>\n      /// <param name=\"resolve\" type=\"Function\"></param>\n      var key = cursor.key;\n      if (typeof key !== 'string') return false;\n      var lowerKey = lower(key);\n      if (match(lowerKey, lowerNeedles, firstPossibleNeedle)) {\n          return true;\n      } else {\n          var lowestPossibleCasing = null;\n          for (var i=firstPossibleNeedle; i<needlesLen; ++i) {\n              var casing = nextCasing(key, lowerKey, upperNeedles[i], lowerNeedles[i], compare, direction);\n              if (casing === null && lowestPossibleCasing === null)\n                  firstPossibleNeedle = i + 1;\n              else if (lowestPossibleCasing === null || compare(lowestPossibleCasing, casing) > 0) {\n                  lowestPossibleCasing = casing;\n              }\n          }\n          if (lowestPossibleCasing !== null) {\n              advance(function () { cursor.continue(lowestPossibleCasing + nextKeySuffix); });\n          } else {\n              advance(resolve);\n          }\n          return false;\n      }\n  });\n  return c;\n}\n\nexport function createRange (lower: IndexableType, upper: IndexableType, lowerOpen?: boolean, upperOpen?: boolean): DBCoreKeyRange {\n    return {\n        type: DBCoreRangeType.Range,\n        lower,\n        upper,\n        lowerOpen,\n        upperOpen\n    };\n}\n\nexport function rangeEqual (value: IndexableType) : DBCoreKeyRange {\n    return {\n        type: DBCoreRangeType.Equal,\n        lower: value,\n        upper: value\n    };\n}\n", "import { WhereClause as IWhere<PERSON>lause } from \"../../public/types/where-clause\";\nimport { Collection } from \"../collection\";\nimport { Table } from \"../table\";\nimport { IndexableType } from \"../../public/types/indexable-type\";\nimport { emptyCollection, fail, addIgnoreCaseAlgorithm, createRange, rangeEqual } from './where-clause-helpers';\nimport { INVALID_KEY_ARGUMENT, STRING_EXPECTED, maxString, minKey } from '../../globals/constants';\nimport { getArrayOf, NO_CHAR_ARRAY } from '../../functions/utils';\nimport { exceptions } from '../../errors';\nimport { Dexie } from '../dexie';\nimport { Collection as ICollection} from \"../../public/types/collection\";\n\n/** class WhereClause\n * \n * https://dexie.org/docs/WhereClause/WhereClause\n */\nexport class Where<PERSON><PERSON><PERSON> implements IWhereClause {\n  db: Dexie;\n  _IDBKeyRange: typeof IDBKeyRange;\n  _ctx: {\n    table: Table;\n    index: string;\n    or: Collection;\n  }\n  _cmp: (a: IndexableType, b: IndexableType) => number;\n  _ascending: (a: IndexableType, b: IndexableType) => number;\n  _descending: (a: IndexableType, b: IndexableType) => number;\n  _min: (a: IndexableType, b: IndexableType) => IndexableType;\n  _max: (a: IndexableType, b: IndexableType) => IndexableType;\n\n  get Collection() {\n    return this._ctx.table.db.Collection;\n  }\n\n  /** WhereClause.between()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.between()\n   * \n   **/\n  between(lower: IndexableType, upper: IndexableType, includeLower?: boolean, includeUpper?: boolean) {\n    includeLower = includeLower !== false;   // Default to true\n    includeUpper = includeUpper === true;    // Default to false\n    try {\n      if ((this._cmp(lower, upper) > 0) ||\n        (this._cmp(lower, upper) === 0 && (includeLower || includeUpper) && !(includeLower && includeUpper)))\n        return emptyCollection(this); // Workaround for idiotic W3C Specification that DataError must be thrown if lower > upper. The natural result would be to return an empty collection.\n      return new this.Collection(this, ()=>createRange(lower, upper, !includeLower, !includeUpper));\n    } catch (e) {\n      return fail(this, INVALID_KEY_ARGUMENT);\n    }\n  }\n\n  /** WhereClause.equals()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.equals()\n   * \n   **/\n  equals(value: IndexableType) {\n    if (value == null) return fail(this, INVALID_KEY_ARGUMENT);\n    return new this.Collection(this, () => rangeEqual(value)) as ICollection;\n  }\n\n  /** WhereClause.above()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.above()\n   * \n   **/\n  above(value: IndexableType) {\n    if (value == null) return fail(this, INVALID_KEY_ARGUMENT);\n    return new this.Collection(this, () => createRange(value, undefined, true));\n  }\n\n  /** WhereClause.aboveOrEqual()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.aboveOrEqual()\n   * \n   **/\n  aboveOrEqual(value: IndexableType) {\n    if (value == null) return fail(this, INVALID_KEY_ARGUMENT);\n    return new this.Collection(this, () => createRange(value, undefined, false));\n  }\n\n  /** WhereClause.below()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.below()\n   * \n   **/\n  below(value: IndexableType) {\n    if (value == null) return fail(this, INVALID_KEY_ARGUMENT);\n    return new this.Collection(this, () => createRange(undefined, value, false, true));\n  }\n\n  /** WhereClause.belowOrEqual()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.belowOrEqual()\n   * \n   **/\n  belowOrEqual(value: IndexableType) {\n    if (value == null) return fail(this, INVALID_KEY_ARGUMENT);\n    return new this.Collection(this, () => createRange(undefined, value));\n  }\n\n  /** WhereClause.startsWith()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.startsWith()\n   * \n   **/\n  startsWith(str: string) {\n    if (typeof str !== 'string') return fail(this, STRING_EXPECTED);\n    return this.between(str, str + maxString, true, true);\n  }\n\n  /** WhereClause.startsWithIgnoreCase()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.startsWithIgnoreCase()\n   * \n   **/\n  startsWithIgnoreCase(str: string) {\n    if (str === \"\") return this.startsWith(str);\n    return addIgnoreCaseAlgorithm(this, (x, a) => x.indexOf(a[0]) === 0, [str], maxString);\n  }\n\n  /** WhereClause.equalsIgnoreCase()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.equalsIgnoreCase()\n   * \n   **/\n  equalsIgnoreCase(str: string) {\n    return addIgnoreCaseAlgorithm(this, (x, a) => x === a[0], [str], \"\");\n  }\n\n  /** WhereClause.anyOfIgnoreCase()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.anyOfIgnoreCase()\n   * \n   **/\n  anyOfIgnoreCase(...values: string[]): Collection;\n  anyOfIgnoreCase(values: string[]): Collection;\n  anyOfIgnoreCase() {\n    var set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n    if (set.length === 0) return emptyCollection(this);\n    return addIgnoreCaseAlgorithm(this, (x, a) => a.indexOf(x) !== -1, set, \"\");\n  }\n\n  /** WhereClause.startsWithAnyOfIgnoreCase()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.startsWithAnyOfIgnoreCase()\n   * \n   **/\n  startsWithAnyOfIgnoreCase(...values: string[]): Collection;\n  startsWithAnyOfIgnoreCase(values: string[]): Collection;\n  startsWithAnyOfIgnoreCase() {\n    var set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n    if (set.length === 0) return emptyCollection(this);\n    return addIgnoreCaseAlgorithm(this, (x, a) => a.some(n => x.indexOf(n) === 0), set, maxString);\n  }\n\n  /** WhereClause.anyOf()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.anyOf()\n   * \n   **/\n  anyOf(...values: string[]): Collection;\n  anyOf(values: string[]): Collection;\n  anyOf() {\n    const set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n    let compare = this._cmp;\n    try { set.sort(compare); } catch (e) { return fail(this, INVALID_KEY_ARGUMENT); }\n    if (set.length === 0) return emptyCollection(this);\n    const c = new this.Collection(this, () => createRange(set[0], set[set.length - 1]));\n\n    c._ondirectionchange = direction => {\n      compare = (direction === \"next\" ?\n        this._ascending :\n        this._descending);\n      set.sort(compare);\n    };\n\n    let i = 0;\n    c._addAlgorithm((cursor, advance, resolve) => {\n      const key = cursor.key;\n      while (compare(key, set[i]) > 0) {\n        // The cursor has passed beyond this key. Check next.\n        ++i;\n        if (i === set.length) {\n          // There is no next. Stop searching.\n          advance(resolve);\n          return false;\n        }\n      }\n      if (compare(key, set[i]) === 0) {\n        // The current cursor value should be included and we should continue a single step in case next item has the same key or possibly our next key in set.\n        return true;\n      } else {\n        // cursor.key not yet at set[i]. Forward cursor to the next key to hunt for.\n        advance(() => { cursor.continue(set[i]); });\n        return false;\n      }\n    });\n    return c;\n  }\n\n  /** WhereClause.notEqual()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.notEqual()\n   * \n   **/\n  notEqual(value: IndexableType) {\n    return this.inAnyRange([[minKey, value], [value, this.db._maxKey]], { includeLowers: false, includeUppers: false });\n  }\n\n  /** WhereClause.noneOf()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.noneOf()\n   * \n   **/\n  noneOf(...values: string[]): Collection;\n  noneOf(values: string[]): Collection;\n  noneOf() {\n    const set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n    if (set.length === 0) return new this.Collection(this); // Return entire collection.\n    try { set.sort(this._ascending); } catch (e) { return fail(this, INVALID_KEY_ARGUMENT); }\n    // Transform [\"a\",\"b\",\"c\"] to a set of ranges for between/above/below: [[minKey,\"a\"], [\"a\",\"b\"], [\"b\",\"c\"], [\"c\",maxKey]]\n    const ranges = set.reduce(\n      (res, val) => res ?\n        res.concat([[res[res.length - 1][1], val]]) :\n        [[minKey, val]],\n      null);\n    ranges.push([set[set.length - 1], this.db._maxKey]);\n    return this.inAnyRange(ranges, { includeLowers: false, includeUppers: false });\n  }\n\n  /** WhereClause.inAnyRange()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.inAnyRange()\n   * \n   **/\n  inAnyRange(\n    ranges: ReadonlyArray<{ 0: IndexableType, 1: IndexableType }>,\n    options?: { includeLowers?: boolean, includeUppers?: boolean })\n  {\n    const cmp = this._cmp,\n          ascending = this._ascending,\n          descending = this._descending,\n          min = this._min,\n          max = this._max;\n\n    if (ranges.length === 0) return emptyCollection(this);\n    if (!ranges.every(range =>\n      range[0] !== undefined &&\n      range[1] !== undefined &&\n      ascending(range[0], range[1]) <= 0)) {\n      return fail(\n        this,\n        \"First argument to inAnyRange() must be an Array of two-value Arrays [lower,upper] where upper must not be lower than lower\",\n        exceptions.InvalidArgument);\n    }\n    const includeLowers = !options || options.includeLowers !== false;   // Default to true\n    const includeUppers = options && options.includeUppers === true;    // Default to false\n\n    function addRange(ranges, newRange) {\n      let i = 0, l = ranges.length;\n      for (; i < l; ++i) {\n        const range = ranges[i];\n        if (cmp(newRange[0], range[1]) < 0 && cmp(newRange[1], range[0]) > 0) {\n          range[0] = min(range[0], newRange[0]);\n          range[1] = max(range[1], newRange[1]);\n          break;\n        }\n      }\n      if (i === l)\n        ranges.push(newRange);\n      return ranges;\n    }\n\n    let sortDirection = ascending;\n    function rangeSorter(a, b) { return sortDirection(a[0], b[0]); }\n\n    // Join overlapping ranges\n    let set;\n    try {\n      set = ranges.reduce(addRange, []);\n      set.sort(rangeSorter);\n    } catch (ex) {\n      return fail(this, INVALID_KEY_ARGUMENT);\n    }\n\n    let rangePos = 0;\n    const keyIsBeyondCurrentEntry = includeUppers ?\n      key => ascending(key, set[rangePos][1]) > 0 :\n      key => ascending(key, set[rangePos][1]) >= 0;\n\n    const keyIsBeforeCurrentEntry = includeLowers ?\n      key => descending(key, set[rangePos][0]) > 0 :\n      key => descending(key, set[rangePos][0]) >= 0;\n\n    function keyWithinCurrentRange(key) {\n      return !keyIsBeyondCurrentEntry(key) && !keyIsBeforeCurrentEntry(key);\n    }\n\n    let checkKey = keyIsBeyondCurrentEntry;\n\n    const c = new this.Collection(\n      this,\n      () => createRange(set[0][0], set[set.length - 1][1], !includeLowers, !includeUppers));\n\n    c._ondirectionchange = direction => {\n      if (direction === \"next\") {\n        checkKey = keyIsBeyondCurrentEntry;\n        sortDirection = ascending;\n      } else {\n        checkKey = keyIsBeforeCurrentEntry;\n        sortDirection = descending;\n      }\n      set.sort(rangeSorter);\n    };\n\n    c._addAlgorithm((cursor, advance, resolve) => {\n      var key = cursor.key;\n      while (checkKey(key)) {\n        // The cursor has passed beyond this key. Check next.\n        ++rangePos;\n        if (rangePos === set.length) {\n          // There is no next. Stop searching.\n          advance(resolve);\n          return false;\n        }\n      }\n      if (keyWithinCurrentRange(key)) {\n        // The current cursor value should be included and we should continue a single step in case next item has the same key or possibly our next key in set.\n        return true;\n      } else if (this._cmp(key, set[rangePos][1]) === 0 || this._cmp(key, set[rangePos][0]) === 0) {\n        // includeUpper or includeLower is false so keyWithinCurrentRange() returns false even though we are at range border.\n        // Continue to next key but don't include this one.\n        return false;\n      } else {\n        // cursor.key not yet at set[i]. Forward cursor to the next key to hunt for.\n        advance(() => {\n          if (sortDirection === ascending) cursor.continue(set[rangePos][0]);\n          else cursor.continue(set[rangePos][1]);\n        });\n        return false;\n      }\n    });\n    return c;\n  }\n\n  /** WhereClause.startsWithAnyOf()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.startsWithAnyOf()\n   * \n   **/\n  startsWithAnyOf(...prefixes: string[]): Collection;\n  startsWithAnyOf(prefixes: string[]): Collection;\n  startsWithAnyOf() {\n    const set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n\n    if (!set.every(s => typeof s === 'string')) {\n        return fail(this, \"startsWithAnyOf() only works with strings\");\n    }\n    if (set.length === 0) return emptyCollection(this);\n\n    return this.inAnyRange(set.map((str: string) => [str, str + maxString]));\n  }\n\n}\n", "import { <PERSON><PERSON> } from '../dexie';\nimport { makeClassConstructor } from '../../functions/make-class-constructor';\nimport { WhereClause } from './where-clause';\nimport { Table } from '../table';\nimport { Collection } from '../collection';\nimport { exceptions } from '../../errors';\nimport { cmp } from '../../functions/cmp';\n\nexport interface WhereClauseConstructor {\n  new(table: Table, index?: string, orCollection?: Collection): WhereClause;\n  prototype: WhereClause;\n}\n\n/** Generates a WhereClause constructor.\n * \n * The purpose of having dynamically created constructors, is to allow\n * addons to extend classes for a certain Dexie instance without affecting\n * other db instances.\n */\nexport function createWhereClauseConstructor(db: <PERSON>ie) {\n  return makeClassConstructor<WhereClauseConstructor>(\n    WhereClause.prototype,\n\n    function WhereClause(this: WhereClause, table: Table, index?: string, orCollection?: Collection) {\n      this.db = db;\n      this._ctx = {\n        table: table,\n        index: index === \":id\" ? null : index,\n        or: orCollection\n      };\n      this._cmp = this._ascending = cmp;\n      this._descending = (a, b) => cmp(b, a);\n      this._max = (a, b) => cmp(a,b) > 0 ? a : b;\n      this._min = (a, b) => cmp(a,b) < 0 ? a : b;\n      this._IDBKeyRange = db._deps.IDBKeyRange;\n      if (!this._IDBKeyRange) throw new exceptions.MissingAPI();\n    }\n  );\n}\n", "import { wrap } from \"../helpers/promise\";\n\nexport function eventReject<PERSON><PERSON>ler(reject) {\n  return wrap(function (event) {\n      preventDefault(event);\n      reject (event.target.error);\n      return false;\n  });\n}\n\nexport function eventSuccessHandler (resolve) {\n  return wrap(function (event){\n      resolve(event.target.result);\n  });\n}\n\nexport function hookedEventRejectHandler (reject) {\n  return wrap(function (event) {\n      // See comment on hookedEventSuccessHandler() why wrap() is needed only when supporting hooks.\n      \n      var req = event.target,\n          err = req.error,\n          ctx = req._hookCtx,// Contains the hook error handler. Put here instead of closure to boost performance.\n          hookErrorHandler = ctx && ctx.onerror;\n      hookErrorHandler && hookErrorHandler(err);\n      preventDefault(event);\n      reject (err);\n      return false;\n  });\n}\n\nexport function hookedEventSuccessHandler(resolve) {\n  // wrap() is needed when calling hooks because the rare scenario of:\n  //  * hook does a db operation that fails immediately (<PERSON><PERSON> throws exception)\n  //    For calling db operations on correct transaction, wrap makes sure to set PSD correctly.\n  //    wrap() will also execute in a virtual tick.\n  //  * If not wrapped in a virtual tick, direct exception will launch a new physical tick.\n  //  * If this was the last event in the bulk, the promise will resolve after a physical tick\n  //    and the transaction will have committed already.\n  // If no hook, the virtual tick will be executed in the reject()/resolve of the final promise,\n  // because it is always marked with _lib = true when created using Transaction._promise().\n  return wrap(function(event) {\n      var req = event.target,\n          ctx = req._hookCtx,// Contains the hook error handler. Put here instead of closure to boost performance.\n          result = ctx.value || req.result, // Pass the object value on updates. The result from IDB is the primary key.\n          hookSuccessHandler = ctx && ctx.onsuccess;\n      hookSuccessHandler && hookSuccessHandler(result);\n      resolve && resolve(result);\n  }, resolve);\n}\n\n\nexport function preventDefault(event) {\n  if (event.stopPropagation) // IndexedDBShim doesnt support this on Safari 8 and below.\n      event.stopPropagation();\n  if (event.preventDefault) // IndexedDBShim doesnt support this on Safari 8 and below.\n      event.preventDefault();\n}\n\nexport function BulkErrorHandlerCatchAll(errorList, done?, supportHooks?) {\n  return (supportHooks ? hookedEventRejectHandler : eventRejectHandler)(e => {\n      errorList.push(e);\n      done && done();\n  });\n}\n\n", "import Events from '../helpers/Events';\nimport { GlobalDexieEvents } from '../public/types/db-events';\n\nexport const DEXIE_STORAGE_MUTATED_EVENT_NAME = 'storagemutated' as 'storagemutated';\n\n// Name of the global event fired using DOM dispatchEvent (if not in node).\n// Reason for propagating this as a DOM event is for getting reactivity across\n// multiple versions of Dexie within the same app (as long as they are\n// compatible with regards to the event data).\n// If the ObservabilitySet protocol change in a way that would not be backward\n// compatible, make sure also update the event name to a new number at the end\n// so that two Dexie instances of different versions continue to work together\n//  - maybe not able to communicate but won't fail due to unexpected data in\n// the detail property of the CustomEvent. If so, also make sure to udpate\n// docs and explain at which Dexie version the new name and format of the event\n// is being used.\nexport const STORAGE_MUTATED_DOM_EVENT_NAME = 'x-storagemutated-1';\n\nexport const globalEvents = Events(null, DEXIE_STORAGE_MUTATED_EVENT_NAME) as GlobalDexieEvents;\n", "import { Transaction as ITransaction } from '../../public/types/transaction';\nimport { DexiePromise, wrap, rejection } from \"../../helpers/promise\";\nimport { DbSchema } from '../../public/types/db-schema';\nimport { assert, hasOwn } from '../../functions/utils';\nimport { PSD, usePSD } from '../../helpers/promise';\nimport { Dexie } from '../dexie';\nimport { exceptions } from '../../errors';\nimport { safariMultiStoreFix } from '../../functions/quirks';\nimport { preventDefault } from '../../functions/event-wrappers';\nimport { newScope } from '../../helpers/promise';\nimport * as Debug from '../../helpers/debug';\nimport { Table } from '../table';\nimport { globalEvents } from '../../globals/global-events';\n\n/** Transaction\n * \n * https://dexie.org/docs/Transaction/Transaction\n * \n **/\nexport class Transaction implements ITransaction {\n  db: <PERSON>ie;\n  active: boolean;\n  mode: IDBTransactionMode;\n  chromeTransactionDurability: ChromeTransactionDurability;\n  idbtrans: IDBTransaction;\n  storeNames: string[];\n  explicit?: boolean;\n  on: any;\n  parent?: Transaction;\n  schema: DbSchema;\n  _memoizedTables: {[tableName: string]: Table};\n\n  _reculock: number;\n  _blockedFuncs: { 0: () => any, 1: any }[];\n  _resolve: () => void;\n  _reject: (Error) => void;\n  _waitingFor: DexiePromise; // for waitFor()\n  _waitingQueue: Function[]; // for waitFor()\n  _spinCount: number; // Just for debugging waitFor()\n  _completion: DexiePromise;\n\n  //\n  // Transaction internal methods (not required by API users, but needed internally and eventually by dexie extensions)\n  //\n\n  /** Transaction._lock()\n   * \n   * Internal method.\n   */\n  _lock() {\n    assert(!PSD.global); // Locking and unlocking reuires to be within a PSD scope.\n    // Temporary set all requests into a pending queue if they are called before database is ready.\n    ++this._reculock; // Recursive read/write lock pattern using PSD (Promise Specific Data) instead of TLS (Thread Local Storage)\n    if (this._reculock === 1 && !PSD.global) PSD.lockOwnerFor = this;\n    return this;\n  }\n\n  /** Transaction._unlock()\n   * \n   * Internal method.\n   */\n  _unlock() {\n    assert(!PSD.global); // Locking and unlocking reuires to be within a PSD scope.\n    if (--this._reculock === 0) {\n      if (!PSD.global) PSD.lockOwnerFor = null;\n      while (this._blockedFuncs.length > 0 && !this._locked()) {\n        var fnAndPSD = this._blockedFuncs.shift();\n        try { usePSD(fnAndPSD[1], fnAndPSD[0]); } catch (e) { }\n      }\n    }\n    return this;\n  }\n\n  /** Transaction._lock()\n   * \n   * Internal method.\n   */\n  _locked() {\n    // Checks if any write-lock is applied on this transaction.\n    // To simplify the Dexie API for extension implementations, we support recursive locks.\n    // This is accomplished by using \"Promise Specific Data\" (PSD).\n    // PSD data is bound to a Promise and any child Promise emitted through then() or resolve( new Promise() ).\n    // PSD is local to code executing on top of the call stacks of any of any code executed by Promise():\n    //         * callback given to the Promise() constructor  (function (resolve, reject){...})\n    //         * callbacks given to then()/catch()/finally() methods (function (value){...})\n    // If creating a new independant Promise instance from within a Promise call stack, the new Promise will derive the PSD from the call stack of the parent Promise.\n    // Derivation is done so that the inner PSD __proto__ points to the outer PSD.\n    // PSD.lockOwnerFor will point to current transaction object if the currently executing PSD scope owns the lock.\n    return this._reculock && PSD.lockOwnerFor !== this;\n  }\n\n  /** Transaction.create()\n   * \n   * Internal method.\n   * \n   */\n  create(idbtrans?: IDBTransaction & {[prop: string]: any}) {\n    if (!this.mode) return this;\n    const idbdb = this.db.idbdb;\n    const dbOpenError = this.db._state.dbOpenError;\n    assert(!this.idbtrans);\n    if (!idbtrans && !idbdb) {\n      switch (dbOpenError && dbOpenError.name) {\n        case \"DatabaseClosedError\":\n          // Errors where it is no difference whether it was caused by the user operation or an earlier call to db.open()\n          throw new exceptions.DatabaseClosed(dbOpenError);\n        case \"MissingAPIError\":\n          // Errors where it is no difference whether it was caused by the user operation or an earlier call to db.open()\n          throw new exceptions.MissingAPI(dbOpenError.message, dbOpenError);\n        default:\n          // Make it clear that the user operation was not what caused the error - the error had occurred earlier on db.open()!\n          throw new exceptions.OpenFailed(dbOpenError);\n      }\n    }\n    if (!this.active) throw new exceptions.TransactionInactive();\n    assert(this._completion._state === null); // Completion Promise must still be pending.\n\n    idbtrans = this.idbtrans = idbtrans ||\n      (this.db.core \n        ? this.db.core.transaction(this.storeNames, this.mode as 'readwrite' | 'readonly', { durability: this.chromeTransactionDurability })\n        : idbdb.transaction(this.storeNames, this.mode, { durability: this.chromeTransactionDurability })\n      ) as IDBTransaction;\n\n    idbtrans.onerror = wrap(ev => {\n      preventDefault(ev);// Prohibit default bubbling to window.error\n      this._reject(idbtrans.error);\n    });\n    idbtrans.onabort = wrap(ev => {\n      preventDefault(ev);\n      this.active && this._reject(new exceptions.Abort(idbtrans.error));\n      this.active = false;\n      this.on(\"abort\").fire(ev);\n    });\n    idbtrans.oncomplete = wrap(() => {\n      this.active = false;\n      this._resolve();\n      if ('mutatedParts' in idbtrans) {\n        globalEvents.storagemutated.fire(idbtrans[\"mutatedParts\"]);\n      }\n    });\n    return this;\n  }\n\n  /** Transaction._promise()\n   * \n   * Internal method.\n   */\n  _promise(\n    mode: IDBTransactionMode,\n    fn: (resolve, reject, trans: Transaction) => PromiseLike<any> | void,\n    bWriteLock?: string | boolean): DexiePromise\n  {\n    if (mode === 'readwrite' && this.mode !== 'readwrite')\n      return rejection(new exceptions.ReadOnly(\"Transaction is readonly\"));\n\n    if (!this.active)\n      return rejection(new exceptions.TransactionInactive());\n\n    if (this._locked()) {\n      return new DexiePromise((resolve, reject) => {\n        this._blockedFuncs.push([() => {\n          this._promise(mode, fn, bWriteLock).then(resolve, reject);\n        }, PSD]);\n      });\n\n    } else if (bWriteLock) {\n      return newScope(() => {\n        var p = new DexiePromise((resolve, reject) => {\n          this._lock();\n          const rv = fn(resolve, reject, this);\n          if (rv && rv.then) rv.then(resolve, reject);\n        });\n        p.finally(() => this._unlock());\n        p._lib = true;\n        return p;\n      });\n\n    } else {\n      var p = new DexiePromise((resolve, reject) => {\n        var rv = fn(resolve, reject, this);\n        if (rv && rv.then) rv.then(resolve, reject);\n      });\n      p._lib = true;\n      return p;\n    }\n  }\n\n  /** Transaction._root()\n   * \n   * Internal method. Retrieves the root transaction in the tree of sub transactions.\n   */\n  _root() {\n    return this.parent ? this.parent._root() : this;\n  }\n\n  /** Transaction.waitFor()\n   * \n   * Internal method. Can be accessed from the public API through\n   * Dexie.waitFor(): https://dexie.org/docs/Dexie/Dexie.waitFor()\n   * \n   **/\n  waitFor(promiseLike: PromiseLike<any>) {\n    // Always operate on the root transaction (in case this is a sub stransaction)\n    var root = this._root();\n    // For stability reasons, convert parameter to promise no matter what type is passed to waitFor().\n    // (We must be able to call .then() on it.)\n    const promise = DexiePromise.resolve(promiseLike);\n    if (root._waitingFor) {\n      // Already called waitFor(). Wait for both to complete.\n      root._waitingFor = root._waitingFor.then(() => promise);\n    } else {\n      // We're not in waiting state. Start waiting state.\n      root._waitingFor = promise;\n      root._waitingQueue = [];\n      // Start interacting with indexedDB until promise completes:\n      var store = root.idbtrans.objectStore(root.storeNames[0]);\n      (function spin() {\n        ++root._spinCount; // For debugging only\n        while (root._waitingQueue.length) (root._waitingQueue.shift())();\n        if (root._waitingFor) store.get(-Infinity).onsuccess = spin;\n      }());\n    }\n    var currentWaitPromise = root._waitingFor;\n    return new DexiePromise((resolve, reject) => {\n      promise.then(\n        res => root._waitingQueue.push(wrap(resolve.bind(null, res))),\n        err => root._waitingQueue.push(wrap(reject.bind(null, err)))\n      ).finally(() => {\n        if (root._waitingFor === currentWaitPromise) {\n          // No one added a wait after us. Safe to stop the spinning.\n          root._waitingFor = null;\n        }\n      });\n    });\n  }  \n\n  /** Transaction.abort()\n   * \n   * https://dexie.org/docs/Transaction/Transaction.abort()\n   */\n  abort() {\n    if (this.active) {\n      this.active = false;\n      if (this.idbtrans) this.idbtrans.abort();\n      this._reject(new exceptions.Abort());\n    }\n  }\n\n  /** Transaction.table()\n   * \n   * https://dexie.org/docs/Transaction/Transaction.table()\n   */\n  table(tableName: string) {\n    const memoizedTables = (this._memoizedTables || (this._memoizedTables = {}));\n    if (hasOwn(memoizedTables, tableName))\n      return memoizedTables[tableName];\n    const tableSchema = this.schema[tableName];\n    if (!tableSchema) {\n      throw new exceptions.NotFound(\"Table \" + tableName + \" not part of transaction\");        \n    }\n\n    const transactionBoundTable = new this.db.Table(tableName, tableSchema, this);\n    transactionBoundTable.core = this.db.core.table(tableName);\n    memoizedTables[tableName] = transactionBoundTable;\n    return transactionBoundTable;\n  }\n}\n", "import { <PERSON><PERSON> } from '../dexie';\nimport { makeClassConstructor } from '../../functions/make-class-constructor';\nimport { Transaction } from './transaction';\nimport { DbSchema } from '../../public/types/db-schema';\nimport Events from '../../helpers/Events';\nimport Promise, { rejection } from '../../helpers/promise';\n\nexport interface TransactionConstructor<T extends Transaction=Transaction> {\n  new (\n    mode: IDBTransactionMode,\n    storeNames: string[],\n    dbschema: DbSchema,\n    chromeTransactionDurability: ChromeTransactionDurability,\n    parent?: Transaction) : T;\n  prototype: T;\n}\n\n/** Generates a Transaction constructor bound to given Dexie instance.\n * \n * The purpose of having dynamically created constructors, is to allow\n * addons to extend classes for a certain Dexie instance without affecting\n * other db instances.\n */\nexport function createTransactionConstructor(db: Dexie) {\n  return makeClassConstructor<TransactionConstructor<Transaction>>(\n    Transaction.prototype,\n    function Transaction (\n      this: Transaction,\n      mode: IDBTransactionMode,\n      storeNames: string[],\n      dbschema: DbSchema,\n      chromeTransactionDurability: ChromeTransactionDurability,\n      parent?: Transaction)\n    {\n      this.db = db;\n      this.mode = mode;\n      this.storeNames = storeNames;\n      this.schema = dbschema;\n      this.chromeTransactionDurability = chromeTransactionDurability;\n      this.idbtrans = null;\n      this.on = Events(this, \"complete\", \"error\", \"abort\");\n      this.parent = parent || null;\n      this.active = true;\n      this._reculock = 0;\n      this._blockedFuncs = [];\n      this._resolve = null;\n      this._reject = null;\n      this._waitingFor = null;\n      this._waitingQueue = null;\n      this._spinCount = 0; // Just for debugging waitFor()\n      this._completion = new Promise ((resolve, reject) => {\n          this._resolve = resolve;\n          this._reject = reject;\n      });\n      \n      this._completion.then(\n          ()=> {\n              this.active = false;\n              this.on.complete.fire();\n          },\n          e => {\n              var wasActive = this.active;\n              this.active = false;\n              this.on.error.fire(e);\n              this.parent ?\n                  this.parent._reject(e) :\n                  wasActive && this.idbtrans && this.idbtrans.abort();\n              return rejection(e); // Indicate we actually DO NOT catch this error.\n          });\n    \n    });\n}\n", "import { IndexSpec } from '../public/types/index-spec';\n\nexport function createIndexSpec(\n  name: string,\n  keyPath: string | string[],\n  unique: boolean,\n  multi: boolean,\n  auto: boolean,\n  compound: boolean,\n  isPrimKey: boolean\n): IndexSpec {\n  return {\n    name,\n    keyPath,\n    unique,\n    multi,\n    auto,\n    compound,\n    src: (unique && !isPrimKey ? '&' : '') + (multi ? '*' : '') + (auto ? \"++\" : \"\") + nameFromKeyPath(keyPath)\n  }\n}\n\nexport function nameFromKeyPath (keyPath?: string | string[]): string {\n  return typeof keyPath === 'string' ?\n    keyPath :\n    keyPath ? ('[' + [].join.call(keyPath, '+') + ']') : \"\";\n}\n", "import { IndexSpec } from '../public/types/index-spec';\nimport { TableSchema } from '../public/types/table-schema';\nimport { createIndexSpec } from './index-spec';\nimport { arrayToObject } from '../functions/utils';\n\nexport function createTableSchema (\n  name: string,\n  primKey: IndexSpec,\n  indexes: IndexSpec[]\n): TableSchema {\n  return {\n    name,\n    primKey,\n    indexes,\n    mappedClass: null,\n    idxByName: arrayToObject(indexes, index => [index.name, index])\n  };\n}\n", "import { maxString } from '../globals/constants';\n\nexport function safariMultiStoreFix(storeNames: string[]) {\n  return storeNames.length === 1 ? storeNames[0] : storeNames;\n}\n\nexport function getNativeGetDatabaseNamesFn(indexedDB) {\n  var fn = indexedDB && (indexedDB.getDatabaseNames || indexedDB.webkitGetDatabaseNames);\n  return fn && fn.bind(indexedDB);\n}\n\nexport let getMaxKey = (IdbKeyRange: typeof IDBKeyRange) => {\n  try {\n    IdbKeyRange.only([[]]);\n    getMaxKey = () => [[]];\n    return [[]];\n  } catch (e) {\n    getMaxKey = () => maxString;\n    return maxString;\n  }\n}\n", "import { getByKeyPath } from '../functions/utils';\n\nexport function getKeyExtractor (keyPath: null | string | string[]) : (a: any) => any {\n  if (keyPath == null) {\n    return () => undefined;\n  } else if (typeof keyPath === 'string') {\n    return getSinglePathKeyExtractor(keyPath);\n  } else {\n    return obj => getByKeyPath(obj, keyPath);\n  }\n}\n\nexport function getSinglePathKeyExtractor(keyPath: string) {\n  const split = keyPath.split('.');\n  if (split.length === 1) {\n    return obj => obj[keyPath];\n  } else {\n    return obj => getByKeyPath(obj, keyPath);\n  }\n}\n", "import {\n  DBCore,\n  DBCoreCursor,\n  DBCoreOpenCursorRequest,\n  DBCoreQueryRequest,\n  DBCoreIndex,\n  DBCoreKeyRange,\n  DBCoreQueryResponse,\n  DBCoreRangeType,\n  DBCoreSchema,\n  DBCoreTableSchema,\n  DBCoreTable,\n  DBCoreMutateResponse,\n} from \"../public/types/dbcore\";\nimport { isArray } from '../functions/utils';\nimport { eventRejectHandler, preventDefault } from '../functions/event-wrappers';\nimport { wrap } from '../helpers/promise';\nimport { getMaxKey } from '../functions/quirks';\nimport { getKeyExtractor } from './get-key-extractor';\n\nexport function arrayify<T>(arrayLike: {length: number, [index: number]: T}): T[] {\n  return [].slice.call(arrayLike);\n}\nexport function pick<T,Prop extends keyof T>(obj: T, props: Prop[]): Pick<T, Prop> {\n  const result = {} as Pick<T, Prop>;\n  props.forEach(prop => result[prop] = obj[prop]);\n  return result;\n}\n\nlet _id_counter = 0;\n\nexport function getKeyPathAlias(keyPath: null | string | string[]) {\n  return keyPath == null ?\n    \":id\" :\n    typeof keyPath === 'string' ?\n      keyPath :\n      `[${keyPath.join('+')}]`;\n}\n\nexport function createDBCore (\n  db: IDBDatabase,\n  IdbKeyRange: typeof IDBKeyRange,\n  tmpTrans: IDBTransaction) : DBCore\n{\n  function extractSchema(db: IDBDatabase, trans: IDBTransaction) : {schema: DBCoreSchema, hasGetAll: boolean} {\n    const tables = arrayify(db.objectStoreNames);\n    return {\n      schema: {\n        name: db.name,\n        tables: tables.map(table => trans.objectStore(table)).map(store => {\n          const {keyPath, autoIncrement} = store;\n          const compound = isArray(keyPath);\n          const outbound = keyPath == null;\n          const indexByKeyPath: {[keyPathAlias: string]: DBCoreIndex} = {};\n          const result = {\n            name: store.name,\n            primaryKey: {\n              name: null,\n              isPrimaryKey: true,\n              outbound,\n              compound,\n              keyPath,\n              autoIncrement,\n              unique: true,\n              extractKey: getKeyExtractor(keyPath)\n            } as DBCoreIndex,\n            indexes: arrayify(store.indexNames).map(indexName => store.index(indexName))\n              .map(index => {\n                const {name, unique, multiEntry, keyPath} = index;\n                const compound = isArray(keyPath);\n                const result: DBCoreIndex = {\n                  name,\n                  compound,\n                  keyPath,\n                  unique,\n                  multiEntry,\n                  extractKey: getKeyExtractor(keyPath)\n                };\n                indexByKeyPath[getKeyPathAlias(keyPath)] = result;\n                return result;\n              }),\n            getIndexByKeyPath: (keyPath: null | string | string[]) => indexByKeyPath[getKeyPathAlias(keyPath)]\n          };\n          indexByKeyPath[\":id\"] = result.primaryKey;\n          if (keyPath != null) {\n            indexByKeyPath[getKeyPathAlias(keyPath)] = result.primaryKey;\n          }\n          return result;\n        })\n      },\n      hasGetAll: tables.length > 0 && ('getAll' in trans.objectStore(tables[0])) &&\n        !(typeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) &&\n        !/(Chrome\\/|Edge\\/)/.test(navigator.userAgent) &&\n        [].concat(navigator.userAgent.match(/Safari\\/(\\d*)/))[1] < 604) // Bug with getAll() on Safari ver<604. See discussion following PR #579\n    };\n  }\n\n  function makeIDBKeyRange (range: DBCoreKeyRange) : IDBKeyRange | null {\n    if (range.type === DBCoreRangeType.Any) return null;\n    if (range.type === DBCoreRangeType.Never) throw new Error(\"Cannot convert never type to IDBKeyRange\");\n    const {lower, upper, lowerOpen, upperOpen} = range;\n    const idbRange = lower === undefined ?\n      upper === undefined ?\n        null : //IDBKeyRange.lowerBound(-Infinity, false) : // Any range (TODO: Should we return null instead?)\n        IdbKeyRange.upperBound(upper, !!upperOpen) : // below\n      upper === undefined ?\n        IdbKeyRange.lowerBound(lower, !!lowerOpen) : // above\n        IdbKeyRange.bound(lower, upper, !!lowerOpen, !!upperOpen);\n    return idbRange;\n  }\n\n  function createDbCoreTable(tableSchema: DBCoreTableSchema): DBCoreTable {\n    const tableName = tableSchema.name;\n\n    function mutate ({trans, type, keys, values, range}) {\n      return new Promise<DBCoreMutateResponse>((resolve, reject) => {\n        resolve = wrap(resolve);\n        const store = (trans as IDBTransaction).objectStore(tableName);\n        const outbound = store.keyPath == null;\n        const isAddOrPut = type === \"put\" || type === \"add\";\n        if (!isAddOrPut && type !== 'delete' && type !== 'deleteRange')\n          throw new Error (\"Invalid operation type: \" + type);\n\n        const {length} = keys || values || {length: 1}; // keys.length if keys. values.length if values. 1 if range.\n        if (keys && values && keys.length !== values.length) {\n          throw new Error(\"Given keys array must have same length as given values array.\");\n        }\n        if (length === 0)\n          // No items to write. Don't even bother!\n          return resolve({numFailures: 0, failures: {}, results: [], lastResult: undefined});\n\n        let req: IDBRequest;\n        const reqs: IDBRequest[] = [];\n          \n        const failures: {[operationNumber: number]: Error} = [];\n        let numFailures = 0;\n        const errorHandler = \n          event => {\n            ++numFailures;\n            preventDefault(event);\n          };\n  \n        if (type === 'deleteRange') {\n          // Here the argument is the range\n          if (range.type === DBCoreRangeType.Never)\n            return resolve({numFailures, failures, results: [], lastResult: undefined}); // Deleting the Never range shoulnt do anything.\n          if (range.type === DBCoreRangeType.Any)\n            reqs.push(req = store.clear()); // Deleting the Any range is equivalent to store.clear()\n          else\n            reqs.push(req = store.delete(makeIDBKeyRange(range)));\n        } else {\n          // No matter add, put or delete - find out arrays of first and second arguments to it.\n          const [args1, args2] = isAddOrPut ?\n            outbound ?\n              [values, keys] :\n              [values, null] :\n            [keys, null];\n\n          if (isAddOrPut) {\n            for (let i=0; i<length; ++i) {\n              reqs.push(req = (args2 && args2[i] !== undefined ?\n                store[type](args1[i], args2[i]) :\n                store[type](args1[i])) as IDBRequest);\n              req.onerror = errorHandler;\n            }\n          } else {\n            for (let i=0; i<length; ++i) {\n              reqs.push(req = store[type](args1[i]) as IDBRequest);\n              req.onerror = errorHandler;\n            }\n          }\n        }\n        const done = event => {\n          const lastResult = event.target.result;\n          reqs.forEach((req, i) => req.error != null && (failures[i] = req.error));\n          resolve({\n            numFailures,\n            failures,\n            results: type === \"delete\" ? keys : reqs.map(req => req.result),\n            lastResult\n          });\n        };\n  \n        req.onerror = event => { // wrap() not needed. All paths calling outside will wrap!\n          errorHandler(event);\n          done(event);\n        };\n  \n        req.onsuccess = done;\n      });\n    }\n    \n    function openCursor ({trans, values, query, reverse, unique}: DBCoreOpenCursorRequest): Promise<DBCoreCursor>\n    {\n      return new Promise((resolve, reject) => {\n        resolve = wrap(resolve);\n        const {index, range} = query;\n        const store = (trans as IDBTransaction).objectStore(tableName);\n        // source\n        const source = index.isPrimaryKey ?\n          store :\n          store.index(index.name);\n        // direction\n        const direction = reverse ?\n          unique ?\n            \"prevunique\" :\n            \"prev\" :\n          unique ?\n            \"nextunique\" :\n            \"next\";\n        // request\n        const req = values || !('openKeyCursor' in source) ?\n          source.openCursor(makeIDBKeyRange(range), direction) :\n          source.openKeyCursor(makeIDBKeyRange(range), direction);\n          \n        // iteration\n        req.onerror = eventRejectHandler(reject);\n        req.onsuccess = wrap(ev => {\n\n          const cursor = req.result as unknown as DBCoreCursor;\n          if (!cursor) {\n            resolve(null);\n            return;\n          }\n          (cursor as any).___id = ++_id_counter;\n          (cursor as any).done = false;\n          const _cursorContinue = cursor.continue.bind(cursor);\n          let _cursorContinuePrimaryKey = cursor.continuePrimaryKey;\n          if (_cursorContinuePrimaryKey) _cursorContinuePrimaryKey = _cursorContinuePrimaryKey.bind(cursor);\n          const _cursorAdvance = cursor.advance.bind(cursor);\n          const doThrowCursorIsNotStarted = ()=>{throw new Error(\"Cursor not started\");}\n          const doThrowCursorIsStopped = ()=>{throw new Error(\"Cursor not stopped\");}\n          (cursor as any).trans = trans;\n          cursor.stop = cursor.continue = cursor.continuePrimaryKey = cursor.advance = doThrowCursorIsNotStarted;\n          cursor.fail = wrap(reject);\n          cursor.next = function (this: DBCoreCursor) {\n            // next() must work with \"this\" pointer in order to function correctly for ProxyCursors (derived objects)\n            // without having to re-define next() on each child.\n            let gotOne = 1;\n            return this.start(() => gotOne-- ? this.continue() : this.stop()).then(() => this);\n          };\n          cursor.start = (callback) => {\n            //console.log(\"Starting cursor\", (cursor as any).___id);\n            const iterationPromise = new Promise<void>((resolveIteration, rejectIteration) =>{\n              resolveIteration = wrap(resolveIteration);\n              req.onerror = eventRejectHandler(rejectIteration);\n              cursor.fail = rejectIteration;\n              cursor.stop = value => {\n                //console.log(\"Cursor stop\", cursor);\n                cursor.stop = cursor.continue = cursor.continuePrimaryKey = cursor.advance = doThrowCursorIsStopped;\n                resolveIteration(value);\n              };\n            });\n            // Now change req.onsuccess to a callback that doesn't call initCursor but just observer.next()\n            const guardedCallback = () => {\n              if (req.result) {\n                //console.log(\"Next result\", cursor);\n                try {\n                  callback();\n                } catch (err) {\n                  cursor.fail(err);\n                }\n              } else {\n                (cursor as any).done = true;\n                cursor.start = ()=>{throw new Error(\"Cursor behind last entry\");}\n                cursor.stop();\n              }\n            }\n            req.onsuccess = wrap(ev => {\n              //cursor.continue = _cursorContinue;\n              //cursor.continuePrimaryKey = _cursorContinuePrimaryKey;\n              //cursor.advance = _cursorAdvance;\n              req.onsuccess = guardedCallback;\n              guardedCallback();\n            });\n            cursor.continue = _cursorContinue;\n            cursor.continuePrimaryKey = _cursorContinuePrimaryKey;\n            cursor.advance = _cursorAdvance;\n            guardedCallback();\n            return iterationPromise;\n          };\n          resolve(cursor);\n        }, reject); \n      });\n    }\n  \n    function query (hasGetAll: boolean) {\n      return (request: DBCoreQueryRequest) => {\n        return new Promise<DBCoreQueryResponse>((resolve, reject) => {\n          resolve = wrap(resolve);\n          const {trans, values, limit, query} = request;\n          const nonInfinitLimit = limit === Infinity ? undefined : limit;\n          const {index, range} = query;\n          const store = (trans as IDBTransaction).objectStore(tableName);\n          const source = index.isPrimaryKey ? store : store.index(index.name);\n          const idbKeyRange = makeIDBKeyRange(range);\n          if (limit === 0) return resolve({result: []});\n          if (hasGetAll) {\n            const req = values ?\n                (source as any).getAll(idbKeyRange, nonInfinitLimit) :\n                (source as any).getAllKeys(idbKeyRange, nonInfinitLimit);\n            req.onsuccess = event => resolve({result: event.target.result});\n            req.onerror = eventRejectHandler(reject);\n          } else {\n            let count = 0;\n            const req = values || !('openKeyCursor' in source) ?\n              source.openCursor(idbKeyRange) :\n              source.openKeyCursor(idbKeyRange)\n            const result = [];\n            req.onsuccess = event => {\n              const cursor = req.result as IDBCursorWithValue;\n              if (!cursor) return resolve({result});\n              result.push(values ? cursor.value : cursor.primaryKey);\n              if (++count === limit) return resolve({result});\n              cursor.continue();\n            };\n            req.onerror = eventRejectHandler(reject);\n          }\n        });\n      };\n    }\n  \n    return {\n      name: tableName,\n      schema: tableSchema,\n      \n      mutate,\n\n      getMany ({trans, keys}) {\n        return new Promise<any[]>((resolve, reject) => {\n          resolve = wrap(resolve);\n          const store = (trans as IDBTransaction).objectStore(tableName);\n          const length = keys.length;\n          const result = new Array(length);\n          let keyCount = 0;\n          let callbackCount = 0;\n          let valueCount = 0;\n          let req: IDBRequest & {_pos?: number};\n    \n          const successHandler = event => {\n            const req = event.target;\n            if ((result[req._pos] = req.result) != null) ++valueCount;\n            if (++callbackCount === keyCount) resolve(result);\n          };\n          const errorHandler = eventRejectHandler(reject);\n    \n          for (let i=0; i<length; ++i) {\n            const key = keys[i];\n            if (key != null) {\n              req = store.get(keys[i]);\n              req._pos = i;\n              req.onsuccess = successHandler;\n              req.onerror = errorHandler;\n              ++keyCount;\n            }\n          }\n          if (keyCount === 0) resolve(result);\n        });\n      },\n\n      get ({trans, key}) {\n        return new Promise<any>((resolve, reject) => {\n          resolve = wrap (resolve);\n          const store = (trans as IDBTransaction).objectStore(tableName);\n          const req = store.get(key);\n          req.onsuccess = event => resolve((event.target as any).result);\n          req.onerror = eventRejectHandler(reject);\n        });\n      },\n\n      query: query(hasGetAll),\n      \n      openCursor,\n\n      count ({query, trans}) {\n        const {index, range} = query;\n        return new Promise<number>((resolve, reject) => {\n          const store = (trans as IDBTransaction).objectStore(tableName);\n          const source = index.isPrimaryKey ? store : store.index(index.name);\n          const idbKeyRange = makeIDBKeyRange(range);\n          const req = idbKeyRange ? source.count(idbKeyRange) : source.count();\n          req.onsuccess = wrap(ev => resolve((ev.target as IDBRequest).result));\n          req.onerror = eventRejectHandler(reject);\n        });\n      }\n    };\n  }\n\n  const {schema, hasGetAll} = extractSchema(db, tmpTrans);\n  const tables = schema.tables.map(tableSchema => createDbCoreTable(tableSchema));\n  const tableMap: {[name: string]: DBCoreTable} = {};\n  tables.forEach(table => tableMap[table.name] = table);\n  return {\n    stack: \"dbcore\",\n    \n    transaction: db.transaction.bind(db),\n\n    table(name: string) {\n      const result = tableMap[name];\n      if (!result) throw new Error(`Table '${name}' not found`);\n      return tableMap[name];\n    },\n\n    MIN_KEY: -Infinity,\n\n    MAX_KEY: getMaxKey(IdbKeyRange),\n\n    schema\n\n  };\n}\n", "import { <PERSON><PERSON> } from './';\nimport { createDBCore } from '../../dbcore/dbcore-indexeddb';\nimport { DBCore } from '../../public/types/dbcore';\nimport { DexieDOMDependencies } from '../../public/types/dexie-dom-dependencies';\nimport { DexieStacks, Middleware } from '../../public/types/middleware';\nimport { exceptions } from '../../errors';\n\nfunction createMiddlewareStack<TStack extends {stack: string}>(\n  stackImpl: {stack: string},\n  middlewares: Middleware<{stack: string}>[]): TStack {\n  return middlewares.reduce((down, {create}) => ({...down, ...create(down)}), stackImpl) as TStack;\n} \n\nfunction createMiddlewareStacks(\n  middlewares: {[StackName in keyof DexieStacks]?: Middleware<DexieStacks[StackName]>[]},\n  idbdb: IDBDatabase,\n  {IDBKeyRange, indexedDB}: DexieDOMDependencies,\n  tmpTrans: IDBTransaction): {[StackName in keyof DexieStacks]?: DexieStacks[StackName]}\n{\n  const dbcore = createMiddlewareStack<DBCore>(\n    createDBCore(idbdb, IDBKeyRange, tmpTrans),\n    middlewares.dbcore);\n  \n  // TODO: Create other stacks the same way as above. They might be dependant on the result\n  // of creating dbcore stack.\n\n  return {\n    dbcore\n  };\n}\n\nexport function generateMiddlewareStacks(db: Dexie, tmpTrans: IDBTransaction) {\n  const idbdb = tmpTrans.db;\n  const stacks = createMiddlewareStacks(db._middlewares, idbdb, db._deps, tmpTrans);\n  db.core = stacks.dbcore!;\n  db.tables.forEach(table => {\n    const tableName = table.name;\n    if (db.core.schema.tables.some(tbl => tbl.name === tableName)) {\n      table.core = db.core.table(tableName);\n      if (db[tableName] instanceof db.Table) {\n          db[tableName].core = table.core;\n      }\n    }\n  });\n}\n", "import { <PERSON><PERSON> } from '../dexie';\nimport { DbSchema } from '../../public/types/db-schema';\nimport { _global } from \"../../globals/global\";\nimport { setProp, keys, slice, isArray, shallowClone, isAsyncFunction, defineProperty, getPropertyDescriptor } from '../../functions/utils';\nimport { Transaction } from '../transaction';\nimport { Version } from './version';\nimport Promise, { PSD, newScope, NativePromise, decrementExpectedAwaits, incrementExpectedAwaits } from '../../helpers/promise';\nimport { exceptions } from '../../errors';\nimport { TableSchema } from '../../public/types/table-schema';\nimport { IndexSpec } from '../../public/types/index-spec';\nimport { createIndexSpec, nameFromKeyPath } from '../../helpers/index-spec';\nimport { createTableSchema } from '../../helpers/table-schema';\nimport { generateMiddlewareStacks } from '../dexie/generate-middleware-stacks';\nimport { debug } from '../../helpers/debug';\nimport { PromiseExtended } from '../../public/types/promise-extended';\n\nexport function setApiOnPlace(db: Dexie, objs: Object[], tableNames: string[], dbschema: DbSchema) {\n  tableNames.forEach(tableName => {\n    const schema = dbschema[tableName];\n    objs.forEach(obj => {\n      const propDesc = getPropertyDescriptor(obj, tableName);\n      if (!propDesc || (\"value\" in propDesc && propDesc.value === undefined)) {\n        // Either the prop is not declared, or it is initialized to undefined.\n        if (obj === db.Transaction.prototype || obj instanceof db.Transaction) {\n          // obj is a Transaction prototype (or prototype of a subclass to Transaction)\n          // Make the API a getter that returns this.table(tableName)\n          setProp(obj, tableName, {\n            get(this: Transaction) { return this.table(tableName); },\n            set(value: any) {\n              // Issue #1039\n              // Let \"this.schema = dbschema;\" and other props in transaction constructor work even if there's a name collision with the table name.\n              defineProperty(this, tableName, {value, writable: true, configurable: true, enumerable: true});\n            }\n          });\n        } else {\n          // Table will not be bound to a transaction (will use Dexie.currentTransaction)\n          obj[tableName] = new db.Table(tableName, schema);\n        }\n      }\n    });\n  });\n}\n\nexport function removeTablesApi(db: Dexie, objs: Object[]) {\n  objs.forEach(obj => {\n    for (let key in obj) {\n      if (obj[key] instanceof db.Table) delete obj[key];\n    }\n  });\n}\n\nexport function lowerVersionFirst(a: Version, b: Version) {\n  return a._cfg.version - b._cfg.version;\n}\n\nexport function runUpgraders(db: Dexie, oldVersion: number, idbUpgradeTrans: IDBTransaction, reject) {\n  const globalSchema = db._dbSchema;\n  if (idbUpgradeTrans.objectStoreNames.contains('$meta') && !globalSchema.$meta) {\n    globalSchema.$meta = createTableSchema(\"$meta\", parseIndexSyntax(\"\")[0], []);\n    db._storeNames.push('$meta');\n  }\n  const trans = db._createTransaction('readwrite', db._storeNames, globalSchema);\n  trans.create(idbUpgradeTrans);\n  trans._completion.catch(reject);\n  const rejectTransaction = trans._reject.bind(trans);\n  const transless = PSD.transless || PSD;\n  newScope(() => {\n    PSD.trans = trans;\n    PSD.transless = transless;\n    if (oldVersion === 0) {\n      // Create tables:\n      keys(globalSchema).forEach(tableName => {\n        createTable(idbUpgradeTrans, tableName, globalSchema[tableName].primKey, globalSchema[tableName].indexes);\n      });\n      generateMiddlewareStacks(db, idbUpgradeTrans);\n      Promise.follow(() => db.on.populate.fire(trans)).catch(rejectTransaction);\n    } else {\n      generateMiddlewareStacks(db, idbUpgradeTrans);\n      return getExistingVersion(db, trans, oldVersion)\n        .then(oldVersion => updateTablesAndIndexes(db, oldVersion, trans, idbUpgradeTrans))\n        .catch(rejectTransaction);\n    }\n  });\n}\n\nexport type UpgradeQueueItem = (idbtrans: IDBTransaction) => PromiseLike<any> | void;\n\nexport function patchCurrentVersion(db: Dexie, idbUpgradeTrans: IDBTransaction) {\n  createMissingTables(db._dbSchema, idbUpgradeTrans);\n  if (idbUpgradeTrans.db.version % 10 === 0 && !idbUpgradeTrans.objectStoreNames.contains('$meta')) {\n    // Rolled over to the next 10-ies due to many schema upgrades without bumping version.\n    // No problem! We pin the database to its expected version by adding the $meta table so that next\n    // time the programmer bumps the version and attaches, an upgrader, that upgrader will indeed run,\n    // as well any further upgraders coming after that.\n    idbUpgradeTrans.db.createObjectStore('$meta').add(Math.ceil((idbUpgradeTrans.db.version / 10) - 1), 'version');\n  }\n  const globalSchema = buildGlobalSchema(db, db.idbdb, idbUpgradeTrans);\n  adjustToExistingIndexNames(db, db._dbSchema, idbUpgradeTrans);\n  const diff = getSchemaDiff(globalSchema, db._dbSchema);\n  for (const tableChange of diff.change) {\n    if (tableChange.change.length || tableChange.recreate) {\n      console.warn(`Unable to patch indexes of table ${tableChange.name} because it has changes on the type of index or primary key.`);\n      return;\n    }\n    const store = idbUpgradeTrans.objectStore(tableChange.name);\n    tableChange.add.forEach(idx => {\n      if (debug) console.debug(`Dexie upgrade patch: Creating missing index ${tableChange.name}.${idx.src}`);\n      addIndex(store, idx);\n    });\n  }\n}\n\nfunction getExistingVersion(db: Dexie, trans: Transaction, oldVersion: number): PromiseExtended<number> {\n  // In normal case, existing version is the native installed version divided by 10.\n  // However, in case more than 10 schema changes have been made on the same version (such as while\n  // developing an app), the native version may have passed beyond a multiple of 10 within the same version.\n  // When that happens, a table $meta will have been created, containing a single entry with key \"version\"\n  // and the value of the real old version to use when running upgraders going forward.\n  if (trans.storeNames.includes('$meta')) {\n    return trans.table('$meta').get('version').then(metaVersion => {\n      return metaVersion != null ? metaVersion : oldVersion\n    })\n  } else {\n    return Promise.resolve(oldVersion);\n  }\n}\n\nfunction updateTablesAndIndexes(\n  db: Dexie,\n  oldVersion: number,\n  trans: Transaction,\n  idbUpgradeTrans: IDBTransaction)\n{\n  // Upgrade version to version, step-by-step from oldest to newest version.\n  // Each transaction object will contain the table set that was current in that version (but also not-yet-deleted tables from its previous version)\n  const queue: UpgradeQueueItem[] = [];\n  const versions = db._versions;\n  let globalSchema = db._dbSchema = buildGlobalSchema(db, db.idbdb, idbUpgradeTrans);\n  let anyContentUpgraderHasRun = false;\n  \n  const versToRun = versions.filter(v => v._cfg.version >= oldVersion);\n  if (versToRun.length === 0) {\n    // Important not to continue at this point.\n    // Coming here means we've already patched schema in patchCurrentVersion() after having\n    // incremented native version to a value above the declared highest version.\n    // When being in this mode, it means that there might be different versions the db competing\n    // about it with different version of the schema. Therefore, we must avoid deleting tables\n    // or indexes here so that both versions can co-exist until the application has been upgraded to\n    // a version that declares no lower than the native version.\n    // If after that, a downgrade happens again, we'll end up here again, accepting both versions\n    // And we'll stay in this state until app developer releases a new declared version.\n    return Promise.resolve(); \n  }\n  \n  versToRun.forEach(version => {\n    queue.push(() => {\n      const oldSchema = globalSchema;\n      const newSchema = version._cfg.dbschema;\n      adjustToExistingIndexNames(db, oldSchema, idbUpgradeTrans);\n      adjustToExistingIndexNames(db, newSchema, idbUpgradeTrans);\n\n      globalSchema = db._dbSchema = newSchema;\n\n      const diff = getSchemaDiff(oldSchema, newSchema);\n      // Add tables           \n      diff.add.forEach(tuple => {\n        createTable(idbUpgradeTrans, tuple[0], tuple[1].primKey, tuple[1].indexes);\n      });\n      // Change tables\n      diff.change.forEach(change => {\n        if (change.recreate) {\n          throw new exceptions.Upgrade(\"Not yet support for changing primary key\");\n        } else {\n          const store = idbUpgradeTrans.objectStore(change.name);\n          // Add indexes\n          change.add.forEach(idx => addIndex(store, idx));\n          // Update indexes\n          change.change.forEach(idx => {\n            store.deleteIndex(idx.name);\n            addIndex(store, idx);\n          });\n          // Delete indexes\n          change.del.forEach(idxName => store.deleteIndex(idxName));\n        }\n      });\n\n      const contentUpgrade = version._cfg.contentUpgrade;\n\n      if (contentUpgrade && version._cfg.version > oldVersion) {\n        // Update db.core with new tables and indexes:\n        generateMiddlewareStacks(db, idbUpgradeTrans);\n        trans._memoizedTables = {}; // Invalidate memoization as transaction shape may change between versions.\n\n        anyContentUpgraderHasRun = true;\n\n        // Add to-be-deleted tables to contentUpgrade transaction\n        let upgradeSchema = shallowClone(newSchema);\n        diff.del.forEach(table => {\n          upgradeSchema[table] = oldSchema[table];\n        });\n\n        // Safe to affect Transaction.prototype globally in this moment,\n        // because when this code runs, there may not be any other code\n        // that can access any transaction instance, else than this particular\n        // upgrader function.\n        removeTablesApi(db, [db.Transaction.prototype]);\n        setApiOnPlace(db, [db.Transaction.prototype], keys(upgradeSchema), upgradeSchema);\n        trans.schema = upgradeSchema;\n\n        // Support for native async await.\n        const contentUpgradeIsAsync = isAsyncFunction(contentUpgrade);\n        if (contentUpgradeIsAsync) {\n          incrementExpectedAwaits();\n        }\n        \n        let returnValue: any;\n        const promiseFollowed = Promise.follow(() => {\n          // Finally, call the scope function with our table and transaction arguments.\n          returnValue = contentUpgrade(trans);\n          if (returnValue) {\n            if (contentUpgradeIsAsync) {\n              // contentUpgrade is a native async function - we know for sure returnValue is native promise.\n              var decrementor = decrementExpectedAwaits.bind(null, null);\n              returnValue.then(decrementor, decrementor);\n            }\n          }\n        });\n        return (returnValue && typeof returnValue.then === 'function' ?\n          Promise.resolve(returnValue) : promiseFollowed.then(()=>returnValue));\n      }\n    });\n    queue.push(idbtrans => {\n      const newSchema = version._cfg.dbschema;\n      // Delete old tables\n      deleteRemovedTables(newSchema, idbtrans);\n      // Restore the final API\n      removeTablesApi(db, [db.Transaction.prototype]);\n      setApiOnPlace(db, [db.Transaction.prototype], db._storeNames, db._dbSchema);\n      trans.schema = db._dbSchema;\n    });\n    // Maintain the $meta table after this version's tables and indexes has been created and content upgraders have run.\n    queue.push(idbtrans => {\n      if (db.idbdb.objectStoreNames.contains('$meta')) {\n        if (Math.ceil(db.idbdb.version / 10) === version._cfg.version) {\n          // Remove $meta table if it's no more needed - we are in line with the native version\n          db.idbdb.deleteObjectStore('$meta');\n          delete db._dbSchema.$meta;\n          db._storeNames = db._storeNames.filter(name => name !== '$meta');\n        } else {\n          // We're still not in line with the native version. Make sure to update the virtual version\n          // to the successfully run version\n          idbtrans.objectStore('$meta').put(version._cfg.version, 'version');\n        }\n      }\n    }); \n  });\n\n  // Now, create a queue execution engine\n  function runQueue() {\n    return queue.length ? Promise.resolve(queue.shift()(trans.idbtrans)).then(runQueue) :\n      Promise.resolve();\n  }\n\n  return runQueue().then(() => {\n    createMissingTables(globalSchema, idbUpgradeTrans); // At last, make sure to create any missing tables. (Needed by addons that add stores to DB without specifying version)\n  });\n}\n\nexport interface SchemaDiff {\n  del: string[],\n  add: [string, TableSchema][];\n  change: TableSchemaDiff[];\n}\n\nexport interface TableSchemaDiff {\n  name: string,\n  recreate: boolean,\n  del: string[],\n  add: IndexSpec[],\n  change: IndexSpec[]\n}\n\nexport function getSchemaDiff(oldSchema: DbSchema, newSchema: DbSchema): SchemaDiff {\n  const diff: SchemaDiff = {\n    del: [], // Array of table names\n    add: [], // Array of [tableName, newDefinition]\n    change: [] // Array of {name: tableName, recreate: newDefinition, del: delIndexNames, add: newIndexDefs, change: changedIndexDefs}\n  };\n  let table: string;\n  for (table in oldSchema) {\n    if (!newSchema[table]) diff.del.push(table);\n  }\n  for (table in newSchema) {\n    const oldDef = oldSchema[table],\n      newDef = newSchema[table];\n    if (!oldDef) {\n      diff.add.push([table, newDef]);\n    } else {\n      const change = {\n        name: table,\n        def: newDef,\n        recreate: false,\n        del: [],\n        add: [],\n        change: []\n      };\n      if (\n          (\n             // compare keyPaths no matter if string or string[]\n             // compare falsy keypaths same no matter if they are null or empty string.\n            ''+(oldDef.primKey.keyPath||'')\n          ) !== (\n            ''+(newDef.primKey.keyPath||'')\n          ) ||\n            // Compare the autoIncrement flag also\n          (oldDef.primKey.auto !== newDef.primKey.auto))\n      {\n        // Primary key has changed. Remove and re-add table.\n        change.recreate = true;\n        diff.change.push(change);\n      } else {\n        // Same primary key. Just find out what differs:\n        const oldIndexes = oldDef.idxByName;\n        const newIndexes = newDef.idxByName;\n        let idxName: string;\n        for (idxName in oldIndexes) {\n          if (!newIndexes[idxName]) change.del.push(idxName);\n        }\n        for (idxName in newIndexes) {\n          const oldIdx = oldIndexes[idxName],\n            newIdx = newIndexes[idxName];\n          if (!oldIdx) change.add.push(newIdx);\n          else if (oldIdx.src !== newIdx.src) change.change.push(newIdx);\n        }\n        if (change.del.length > 0 || change.add.length > 0 || change.change.length > 0) {\n          diff.change.push(change);\n        }\n      }\n    }\n  }\n  return diff;\n}\n\nexport function createTable(\n  idbtrans: IDBTransaction,\n  tableName: string,\n  primKey: IndexSpec,\n  indexes: IndexSpec[]\n) {\n  const store = idbtrans.db.createObjectStore(\n    tableName,\n    primKey.keyPath ?\n      { keyPath: primKey.keyPath, autoIncrement: primKey.auto } :\n      { autoIncrement: primKey.auto }\n  );\n  indexes.forEach(idx => addIndex(store, idx));\n  return store;\n}\n\nexport function createMissingTables(newSchema: DbSchema, idbtrans: IDBTransaction) {\n  keys(newSchema).forEach(tableName => {\n    if (!idbtrans.db.objectStoreNames.contains(tableName)) {\n      if (debug) console.debug('Dexie: Creating missing table', tableName);\n      createTable(idbtrans, tableName, newSchema[tableName].primKey, newSchema[tableName].indexes);\n    }\n  });\n}\n\nexport function deleteRemovedTables(newSchema: DbSchema, idbtrans: IDBTransaction) {\n  [].slice.call(idbtrans.db.objectStoreNames).forEach(storeName =>\n    newSchema[storeName] == null && idbtrans.db.deleteObjectStore(storeName));\n}\n\nexport function addIndex(store: IDBObjectStore, idx: IndexSpec) {\n  store.createIndex(idx.name, idx.keyPath, { unique: idx.unique, multiEntry: idx.multi });\n}\n\nfunction buildGlobalSchema(\n  db: Dexie,\n  idbdb: IDBDatabase,\n  tmpTrans: IDBTransaction\n) {\n  const globalSchema = {};\n  const dbStoreNames = slice(idbdb.objectStoreNames, 0);\n  dbStoreNames.forEach(storeName => {\n    const store = tmpTrans.objectStore(storeName);\n    let keyPath = store.keyPath;\n    const primKey = createIndexSpec(\n      nameFromKeyPath(keyPath),\n      keyPath || \"\",\n      true,\n      false,\n      !!store.autoIncrement,\n      keyPath && typeof keyPath !== \"string\",\n      true\n    );\n    const indexes: IndexSpec[] = [];\n    for (let j = 0; j < store.indexNames.length; ++j) {\n      const idbindex = store.index(store.indexNames[j]);\n      keyPath = idbindex.keyPath;\n      var index = createIndexSpec(\n        idbindex.name,\n        keyPath,\n        !!idbindex.unique,\n        !!idbindex.multiEntry,\n        false,\n        keyPath && typeof keyPath !== \"string\",\n        false\n      );\n      indexes.push(index);\n    }\n    globalSchema[storeName] = createTableSchema(storeName, primKey, indexes);\n  });\n  return globalSchema;\n}\n\nexport function readGlobalSchema(db: Dexie, idbdb: IDBDatabase, tmpTrans: IDBTransaction) {\n  db.verno = idbdb.version / 10;\n  const globalSchema = db._dbSchema = buildGlobalSchema(db, idbdb, tmpTrans);\n  db._storeNames = slice(idbdb.objectStoreNames, 0);\n  setApiOnPlace(db, [db._allTables], keys(globalSchema), globalSchema);\n}\n\nexport function verifyInstalledSchema(db: Dexie, tmpTrans: IDBTransaction): boolean {\n  const installedSchema = buildGlobalSchema(db, db.idbdb, tmpTrans);\n  const diff = getSchemaDiff(installedSchema, db._dbSchema);\n  return !(diff.add.length || diff.change.some(ch => ch.add.length || ch.change.length));\n}\n\nexport function adjustToExistingIndexNames(db: Dexie, schema: DbSchema, idbtrans: IDBTransaction) {\n  // Issue #30 Problem with existing db - adjust to existing index names when migrating from non-dexie db\n  const storeNames = idbtrans.db.objectStoreNames;\n\n  for (let i = 0; i < storeNames.length; ++i) {\n    const storeName = storeNames[i];\n    const store = idbtrans.objectStore(storeName);\n    db._hasGetAll = 'getAll' in store;\n\n    for (let j = 0; j < store.indexNames.length; ++j) {\n      const indexName = store.indexNames[j];\n      const keyPath = store.index(indexName).keyPath;\n      const dexieName = typeof keyPath === 'string' ? keyPath : \"[\" + slice(keyPath).join('+') + \"]\";\n      if (schema[storeName]) {\n        const indexSpec = schema[storeName].idxByName[dexieName];\n        if (indexSpec) {\n          indexSpec.name = indexName;\n          delete schema[storeName].idxByName[dexieName];\n          schema[storeName].idxByName[indexName] = indexSpec;\n        }\n      }\n    }\n  }\n\n  // Bug with getAll() on Safari ver<604 on Workers only, see discussion following PR #579\n  if (typeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) &&\n    !/(Chrome\\/|Edge\\/)/.test(navigator.userAgent) &&\n    _global.WorkerGlobalScope && _global instanceof _global.WorkerGlobalScope &&\n    [].concat(navigator.userAgent.match(/Safari\\/(\\d*)/))[1] < 604)\n  {\n    db._hasGetAll = false;\n  }\n}\n\nexport function parseIndexSyntax(primKeyAndIndexes: string): IndexSpec[] {\n  return primKeyAndIndexes.split(',').map((index, indexNum) => {\n    index = index.trim();\n    const name = index.replace(/([&*]|\\+\\+)/g, \"\"); // Remove \"&\", \"++\" and \"*\"\n    // Let keyPath of \"[a+b]\" be [\"a\",\"b\"]:\n    const keyPath = /^\\[/.test(name) ? name.match(/^\\[(.*)\\]$/)[1].split('+') : name;\n\n    return createIndexSpec(\n      name,\n      keyPath || null,\n      /\\&/.test(index),\n      /\\*/.test(index),\n      /\\+\\+/.test(index),\n      isArray(keyPath),\n      indexNum === 0\n    );\n  });\n}\n", "import { Version as IVersion } from '../../public/types/version';\nimport { DbSchema } from '../../public/types/db-schema';\nimport { extend, keys } from '../../functions/utils';\nimport { <PERSON>ie } from '../dexie';\nimport { Transaction } from '../transaction';\nimport { removeTables<PERSON><PERSON>, setApiOnPlace, parseIndexSyntax } from './schema-helpers';\nimport { exceptions } from '../../errors';\nimport { createTableSchema } from '../../helpers/table-schema';\nimport { nop, promisableChain } from '../../functions/chaining-functions';\n\n/** class Version\n *\n * https://dexie.org/docs/Version/Version\n */\nexport class Version implements IVersion {\n  db: Dexie;\n  _cfg: {\n    version: number,\n    storesSource: { [tableName: string]: string | null },\n    dbschema: DbSchema,\n    tables: {},\n    contentUpgrade: Function | null\n  }\n\n  _parseStoresSpec(stores: { [tableName: string]: string | null }, outSchema: DbSchema): any {\n    keys(stores).forEach(tableName => {\n      if (stores[tableName] !== null) {\n          var indexes = parseIndexSyntax(stores[tableName]);\n          var primKey = indexes.shift();\n          primKey.unique = true;\n          if (primKey.multi) throw new exceptions.Schema(\"Primary key cannot be multi-valued\");\n          indexes.forEach(idx => {\n              if (idx.auto) throw new exceptions.Schema(\"Only primary key can be marked as autoIncrement (++)\");\n              if (!idx.keyPath) throw new exceptions.Schema(\"Index must have a name and cannot be an empty string\");\n          });\n          outSchema[tableName] = createTableSchema(tableName, primKey, indexes);\n      }\n    });\n  }\n\n  stores(stores: { [key: string]: string | null; }): IVersion {\n    const db = this.db;\n    this._cfg.storesSource = this._cfg.storesSource ?\n      extend(this._cfg.storesSource, stores) :\n      stores;\n    const versions = db._versions;\n\n    // Derive stores from earlier versions if they are not explicitely specified as null or a new syntax.\n    const storesSpec: { [key: string]: string; } = {};\n    let dbschema = {};\n    versions.forEach(version => { // 'versions' is always sorted by lowest version first.\n      extend(storesSpec, version._cfg.storesSource);\n      dbschema = (version._cfg.dbschema = {});\n      version._parseStoresSpec(storesSpec, dbschema);\n    });\n    // Update the latest schema to this version\n    db._dbSchema = dbschema;\n    // Update APIs\n    removeTablesApi(db, [db._allTables, db, db.Transaction.prototype]);\n    setApiOnPlace(db, [db._allTables, db, db.Transaction.prototype, this._cfg.tables], keys(dbschema), dbschema);\n    db._storeNames = keys(dbschema);\n    return this;\n  }\n\n  upgrade(upgradeFunction: (trans: Transaction) => PromiseLike<any> | void): Version {\n    this._cfg.contentUpgrade = promisableChain(this._cfg.contentUpgrade || nop, upgradeFunction);\n    return this;\n  }\n}\n", "import { <PERSON><PERSON> } from '../dexie';\nimport { makeClassConstructor } from '../../functions/make-class-constructor';\nimport { Version } from './version';\n\nexport interface VersionConstructor {\n  new(versionNumber: number): Version;\n  prototype: Version;\n}\n\n/** Generates a Version constructor bound to given <PERSON>ie instance.\n * \n * The purpose of having dynamically created constructors, is to allow\n * addons to extend classes for a certain Dexie instance without affecting\n * other db instances.\n */\nexport function createVersionConstructor(db: Dexie) {\n  return makeClassConstructor<VersionConstructor>(\n    Version.prototype,\n\n    function Version(this: Version, versionNumber: number) {\n      this.db = db;\n      this._cfg = {\n        version: versionNumber,\n        storesSource: null,\n        dbschema: {},\n        tables: {},\n        contentUpgrade: null\n      };\n    });\n\n}\n", "import { <PERSON><PERSON> } from \"../classes/dexie/dexie\";\nimport { Table } from \"../public/types/table\";\nimport { DBNAMES_DB } from \"../globals/constants\";\nimport { DexieDOMDependencies } from \"../public/types/dexie-dom-dependencies\";\nimport { nop } from \"../functions/chaining-functions\";\n\ntype IDBKeyNamesVar = typeof IDBKeyRange;\n\nfunction getDbNamesTable(indexedDB: IDBFactory, IDBKeyRange: IDBKeyNamesVar) {\n  let dbNamesDB = indexedDB[\"_dbNamesDB\"];\n  if (!dbNamesDB) {\n    dbNamesDB = indexedDB[\"_dbNamesDB\"] = new Dexie(DBNAMES_DB, {\n      addons: [],\n      indexedDB,\n      IDBKeyRange,\n    });\n    dbNamesDB.version(1).stores({ dbnames: \"name\" });\n  }\n  return dbNamesDB.table(\"dbnames\") as Table<{ name: string }, string>;\n}\n\nfunction hasDatabasesNative(indexedDB: IDBFactory) {\n  return indexedDB && typeof indexedDB.databases === \"function\";\n}\n\nexport function getDatabaseNames({\n  indexedDB,\n  IDBKeyRange,\n}: DexieDOMDependencies) {\n  return hasDatabasesNative(indexedDB)\n    ? Promise.resolve(indexedDB.databases()).then((infos) =>\n        infos\n          // Select name prop of infos:\n          .map((info) => info.name)\n          // Filter out DBNAMES_DB as previous Dexie or browser version would not have included it in the result.\n          .filter((name) => name !== DBNAMES_DB)\n      )\n    : getDbNamesTable(indexedDB, IDBKeyRange).toCollection().primaryKeys();\n}\n\nexport function _onDatabaseCreated(\n  { indexedDB, IDBKeyRange }: DexieDOMDependencies,\n  name: string\n) {\n  !hasDatabasesNative(indexedDB) &&\n    name !== DBNAMES_DB &&\n    getDbNamesTable(indexedDB, IDBKeyRange).put({name}).catch(nop);\n}\n\nexport function _onDatabaseDeleted(\n  { indexedDB, IDBKeyRange }: DexieDOMDependencies,\n  name: string\n) {\n  !hasDatabasesNative(indexedDB) &&\n    name !== DBNAMES_DB &&\n    getDbNamesTable(indexedDB, IDBKeyRange).delete(name).catch(nop);\n}\n", "import { newScope } from '../../helpers/promise';\nimport { PSD } from '../../helpers/promise';\n\nexport function vip (fn) {\n  // To be used by subscribers to the on('ready') event.\n  // This will let caller through to access DB even when it is blocked while the db.ready() subscribers are firing.\n  // This would have worked automatically if we were certain that the Provider was using Dexie.Promise for all asyncronic operations. The promise PSD\n  // from the provider.connect() call would then be derived all the way to when provider would call localDatabase.applyChanges(). But since\n  // the provider more likely is using non-promise async APIs or other thenable implementations, we cannot assume that.\n  // Note that this method is only useful for on('ready') subscribers that is returning a Promise from the event. If not using vip()\n  // the database could deadlock since it wont open until the returned Promise is resolved, and any non-VIPed operation started by\n  // the caller will not resolve until database is opened.\n  return newScope(function () {\n    PSD.letThrough = true; // Make sure we are let through if still blocking db due to onready is firing.\n    return fn();\n  });\n}\n\n", "/**\n * Work around Safari 14 IndexedDB open bug.\n *\n * Safari has a horrible bug where IDB requests can hang while the browser is starting up. https://bugs.webkit.org/show_bug.cgi?id=226547\n * The only solution is to keep nudging it until it's awake.\n */\nfunction idbReady() {\n    var isSafari = !navigator.userAgentData &&\n        /Safari\\//.test(navigator.userAgent) &&\n        !/Chrom(e|ium)\\//.test(navigator.userAgent);\n    // No point putting other browsers or older versions of Safari through this mess.\n    if (!isSafari || !indexedDB.databases)\n        return Promise.resolve();\n    var intervalId;\n    return new Promise(function (resolve) {\n        var tryIdb = function () { return indexedDB.databases().finally(resolve); };\n        intervalId = setInterval(tryIdb, 100);\n        tryIdb();\n    }).finally(function () { return clearInterval(intervalId); });\n}\n\nexport default idbReady;\n", "import { cmp } from \"../functions/cmp\";\nimport { extend, iteratorSymbol, props } from '../functions/utils';\nimport { IndexableType } from '../public';\nimport {\n  EmptyRange,\n  IntervalTree,\n  IntervalTreeNode,\n  RangeSetConstructor,\n  RangeSetPrototype,\n} from \"../public/types/rangeset\";\n\n/* An interval tree implementation to efficiently detect overlapping ranges of queried indexes.\n *\n * https://en.wikipedia.org/wiki/Interval_tree\n * \n */\n\nfunction isEmptyRange(node: IntervalTree | {from: IndexableType, to: IndexableType}): node is EmptyRange {\n  return !(\"from\" in node);\n}\n\nexport type RangeSet = RangeSetPrototype & IntervalTree;\n\nexport const RangeSet = function(fromOrTree: any, to?: any) {\n  if (this) {\n    // Called with new()\n    extend(this, arguments.length ? {d:1, from: fromOrTree, to: arguments.length > 1 ? to : fromOrTree} : {d:0});\n  } else {\n    // Called without new()\n    const rv = new RangeSet();\n    if (fromOrTree && (\"d\" in fromOrTree)) {\n      extend(rv, fromOrTree);\n    }\n    return rv;\n  }\n} as RangeSetConstructor;\n\nprops(RangeSet.prototype, {\n  add(rangeSet: IntervalTree | {from: IndexableType, to: IndexableType}) {\n    mergeRanges(this, rangeSet);\n    return this;\n  },\n  addKey(key: IndexableType) {\n    addRange(this, key, key);\n    return this;\n  },\n  addKeys(keys: IndexableType[]) {\n    keys.forEach(key => addRange(this, key, key));\n    return this;\n  },\n  hasKey(key: IndexableType) {\n    const node = getRangeSetIterator(this).next(key).value;\n    return node && cmp(node.from, key) <= 0 && cmp(node.to, key) >= 0;\n  },\n\n  [iteratorSymbol](): Iterator<IntervalTreeNode, undefined, IndexableType | undefined> {\n    return getRangeSetIterator(this);\n  }\n});\n\nfunction addRange(target: IntervalTree, from: IndexableType, to: IndexableType) {\n  const diff = cmp(from, to);\n  // cmp() returns NaN if one of the args are IDB-invalid keys.\n  // Avoid storing invalid keys in rangeset:\n  if (isNaN(diff)) return;\n\n  // Caller is trying to add a range where from is greater than to:\n  if (diff > 0) throw RangeError();\n  \n  if (isEmptyRange(target)) return extend(target, { from, to, d: 1 });\n  const left = target.l;\n  const right = target.r;\n  if (cmp(to, target.from) < 0) {\n    left\n      ? addRange(left, from, to)\n      : (target.l = { from, to, d: 1, l: null, r: null });\n    return rebalance(target);\n  }\n  if (cmp(from, target.to) > 0) {\n    right\n      ? addRange(right, from, to)\n      : (target.r = { from, to, d: 1, l: null, r: null });\n    return rebalance(target);\n  }\n  // Now we have some kind of overlap. We will be able to merge the new range into the node or let it be swallowed.\n\n  // Grow left?\n  if (cmp(from, target.from) < 0) {\n    target.from = from;\n    target.l = null; // Cut off for now. Re-add later.\n    target.d = right ? right.d + 1 : 1;\n  }\n  // Grow right?\n  if (cmp(to, target.to) > 0) {\n    target.to = to;\n    target.r = null; // Cut off for now. Re-add later.\n    target.d = target.l ? target.l.d + 1 : 1;\n  }\n  const rightWasCutOff = !target.r;\n  // Re-add left?\n  if (left && !target.l) {\n    //Ranges to the left may be swallowed. Cut it of and re-add all.\n    //Could probably be done more efficiently!\n    mergeRanges(target, left);\n  }\n  // Re-add right?\n  if (right && rightWasCutOff) {\n    //Ranges to the right may be swallowed. Cut it of and re-add all.\n    //Could probably be done more efficiently!\n    mergeRanges(target, right);\n  }\n}\n\nexport function mergeRanges(target: IntervalTree, newSet: IntervalTree | {from: IndexableType, to: IndexableType}) {\n  function _addRangeSet(\n    target: IntervalTree,\n    { from, to, l, r }: IntervalTreeNode | {from: IndexableType, to: IndexableType, l?: undefined, r?: undefined}\n  ) {\n    addRange(target, from, to);\n    if (l) _addRangeSet(target, l);\n    if (r) _addRangeSet(target, r);\n  }\n\n  if(!isEmptyRange(newSet)) _addRangeSet(target, newSet);\n}\n\nexport function rangesOverlap(\n  rangeSet1: IntervalTree,\n  rangeSet2: IntervalTree\n): boolean {\n    // Start iterating other from scratch.\n    const i1 = getRangeSetIterator(rangeSet2);\n    let nextResult1 = i1.next();\n    if (nextResult1.done) return false;\n    let a = nextResult1.value;\n\n    // Start iterating this from start of other\n    const i2 = getRangeSetIterator(rangeSet1);\n    let nextResult2 = i2.next(a.from); // Start from beginning of other range\n    let b = nextResult2.value;\n\n    while (!nextResult1.done && !nextResult2.done) {\n      if (cmp(b!.from, a.to) <= 0 && cmp(b!.to, a.from) >= 0) return true;\n      cmp(a.from, b!.from) < 0\n        ? (a = (nextResult1 = i1.next(b!.from)).value!) // a is behind. forward it to beginning of next b-range\n        : (b = (nextResult2 = i2.next(a.from)).value); // b is behind. forward it to beginning of next a-range\n    }\n  return false;\n}\n\ntype RangeSetIteratorState =\n  | {\n      up?: RangeSetIteratorState;\n      n: IntervalTreeNode;\n      s: 0 | 1 | 2 | 3;\n    }\n  | undefined\n  | null;\nexport function getRangeSetIterator(\n  node: EmptyRange | IntervalTreeNode\n): Generator<IntervalTreeNode, undefined, IndexableType | undefined> {\n  let state: RangeSetIteratorState = isEmptyRange(node) ? null : { s: 0, n: node };\n\n  return {\n    next(key?) {\n      const keyProvided = arguments.length > 0;\n      while (state) {\n        switch (state.s) {\n          case 0:\n            // Initial state for node.\n            // Fast forward to leftmost node.\n            state.s = 1;\n            if (keyProvided) {\n              while (state.n.l && cmp(key, state.n.from) < 0)\n                state = { up: state, n: state.n.l, s: 1 };\n            } else {\n              while (state.n.l) state = { up: state, n: state.n.l, s: 1 };\n            }\n          // intentionally fall into case 1:\n          case 1:\n            // We're on a node where it's left part is already handled or does not exist.\n            state.s = 2;\n            if (!keyProvided || cmp(key, state.n.to) <= 0)\n              return { value: state.n, done: false };\n          case 2:\n            // We've emitted our node and should continue with the right part or let parent take over from it's state 1\n            if (state.n.r) {\n              state.s = 3; // So when child is done, we know we're done.\n              state = { up: state, n: state.n.r, s: 0 };\n              continue; // Will fall in to case 0 with fast forward to left leaf of this subtree.\n            }\n          // intentionally fall into case 3:\n          case 3:\n            state = state.up;\n        }\n      }\n      return { done: true };\n    },\n  } as Generator<IntervalTreeNode, undefined, IndexableType>;\n}\n\nfunction rebalance(target: IntervalTreeNode) {\n  const diff = (target.r?.d || 0) - (target.l?.d || 0);\n  const r = diff > 1 ? \"r\" : diff < -1 ? \"l\" : \"\";\n  if (r) {\n\n    // Rotate (https://en.wikipedia.org/wiki/Tree_rotation)\n    //\n    // \n    //                    [OLDROOT]\n    //       [OLDROOT.L]            [NEWROOT]\n    //                        [NEWROOT.L] [NEWROOT.R]\n    //\n    // Is going to become:\n    //\n    // \n    //                    [NEWROOT]\n    //        [OLDROOT]             [NEWROOT.R]\n    // [OLDROOT.L] [NEWROOT.L]  \n\n    // * clone now has the props of OLDROOT\n    // Plan:\n    // * target must be given the props of NEWROOT\n    // * target[l] must point to a new OLDROOT\n    // * target[r] must point to NEWROOT.R\n    // * OLDROOT[r] must point to NEWROOT.L\n    const l = r === \"r\" ? \"l\" : \"r\"; // Support both left/right rotation\n    const rootClone = { ...target };\n    // We're gonna copy props from target's right node into target so that target will\n    // have same range as old target[r] (instead of changing pointers, we copy values.\n    // that way we do not need to adjust pointers in parents).\n    const oldRootRight = target[r]; \n    target.from = oldRootRight.from;\n    target.to = oldRootRight.to;\n    target[r] = oldRootRight[r];\n    rootClone[r] = oldRootRight[l];\n    target[l] = rootClone;\n    rootClone.d = computeDepth(rootClone);\n  }\n  target.d = computeDepth(target);\n}\n\nfunction computeDepth({ r, l }: Pick<IntervalTreeNode, \"l\" | \"r\">) {\n  return (r ? (l ? Math.max(r.d, l.d) : r.d) : l ? l.d : 0) + 1;\n}\n", "import { cloneSimpleObjectTree, deepClone, keys, objectIsEmpty } from \"../functions/utils\";\nimport { mergeRanges, RangeSet } from \"../helpers/rangeset\";\nimport { ObservabilitySet } from \"../public/types/db-events\";\n\nexport function extendObservabilitySet(\n  target: ObservabilitySet,\n  newSet: ObservabilitySet\n): ObservabilitySet {\n  keys(newSet).forEach(part => {\n    if (target[part]) mergeRanges(target[part], newSet[part]);\n    else target[part] = cloneSimpleObjectTree(newSet[part]); // Somewhat faster\n  });\n  return target;\n}\n", "import { rangesOverlap } from '../helpers/rangeset';\nimport { ObservabilitySet } from '../public/types/db-events';\n\nexport function obsSetsOverlap(os1: ObservabilitySet, os2: ObservabilitySet) {\n  return os1.all || os2.all || Object.keys(os1).some(\n    (key) => os2[key] && rangesOverlap(os2[key], os1[key])\n  );\n}\n", "import { type GlobalQueryCache } from \"../../public/types/cache\";\n\nexport const cache: GlobalQueryCache = {}\n", "import { CacheEntry, TblQueryCache } from '../../public/types/cache';\nimport { ObservabilitySet } from '../../public/types/db-events';\nimport { extendObservabilitySet } from '../extend-observability-set';\nimport { obsSetsOverlap } from '../obs-sets-overlap';\nimport { cache } from './cache';\n\nlet unsignaledParts: ObservabilitySet = {};\nlet isTaskEnqueued = false;\n\nexport function signalSubscribersLazily(part: ObservabilitySet, optimistic = false) {\n  extendObservabilitySet(unsignaledParts, part);\n  if (!isTaskEnqueued) {\n    isTaskEnqueued = true;\n    setTimeout(() => {\n      isTaskEnqueued = false;\n      const parts = unsignaledParts;\n      unsignaledParts = {};\n      signalSubscribersNow(parts, false);\n    }, 0);\n  }\n}\n\nexport function signalSubscribersNow(\n  updatedParts: ObservabilitySet,\n  deleteAffectedCacheEntries = false\n) {\n  const queriesToSignal = new Set<() => void>();\n  if (updatedParts.all) {\n    // Signal all subscribers to requery.\n    for (const tblCache of Object.values(cache)) {\n      collectTableSubscribers(\n        tblCache,\n        updatedParts,\n        queriesToSignal,\n        deleteAffectedCacheEntries\n      );\n    }\n  } else {\n    for (const key in updatedParts) {\n      const parts = /^idb\\:\\/\\/(.*)\\/(.*)\\//.exec(key);\n      if (parts) {\n        const [, dbName, tableName] = parts;\n        const tblCache = cache[`idb://${dbName}/${tableName}`];\n        if (tblCache)\n          collectTableSubscribers(\n            tblCache,\n            updatedParts,\n            queriesToSignal,\n            deleteAffectedCacheEntries\n          );\n      }\n    }\n  }\n  // Now when affected cache entries are removed, signal collected subscribers to requery.\n  queriesToSignal.forEach((requery) => requery());\n}\n\nfunction collectTableSubscribers(\n  tblCache: TblQueryCache,\n  updatedParts: ObservabilitySet,\n  outQueriesToSignal: Set<() => void>,\n  deleteAffectedCacheEntries: boolean\n) {\n  const updatedEntryLists: [string, CacheEntry[]][] = [];\n  for (const [indexName, entries] of Object.entries(tblCache.queries.query)) {\n    const filteredEntries: CacheEntry[] = [];\n    for (const entry of entries) {\n      if (obsSetsOverlap(updatedParts, entry.obsSet)) {\n        // This query is affected by the mutation. Remove it from cache\n        // and signal all subscribers to requery.\n        entry.subscribers.forEach((requery) => outQueriesToSignal.add(requery));\n      } else if (deleteAffectedCacheEntries) {\n        filteredEntries.push(entry);\n      }\n    }\n    // Collect cache entries to be updated\n    if (deleteAffectedCacheEntries)\n      updatedEntryLists.push([indexName, filteredEntries]);\n  }\n  if (deleteAffectedCacheEntries) {\n    for (const [indexName, filteredEntries] of updatedEntryLists) {\n      tblCache.queries.query[indexName] = filteredEntries;\n    }\n  }\n}\n", "import { <PERSON><PERSON> } from './dexie';\nimport * as Debug from '../../helpers/debug';\nimport { rejection } from '../../helpers/promise';\nimport { exceptions } from '../../errors';\nimport { eventRejectHandler, preventDefault } from '../../functions/event-wrappers';\nimport Promise, { wrap } from '../../helpers/promise';\nimport { connections } from '../../globals/constants';\nimport { runUpgraders, readGlobalSchema, adjustToExistingIndexNames, verifyInstalledSchema, patchCurrentVersion } from '../version/schema-helpers';\nimport { safariMultiStoreFix } from '../../functions/quirks';\nimport { _onDatabaseCreated } from '../../helpers/database-enumerator';\nimport { vip } from './vip';\nimport { promisableChain, nop } from '../../functions/chaining-functions';\nimport { generateMiddlewareStacks } from './generate-middleware-stacks';\nimport { slice } from '../../functions/utils';\nimport safari14Workaround from 'safari-14-idb-fix';\nimport { type ObservabilitySet } from '../../public/types/db-events';\nimport { RangeSet } from '../../helpers/rangeset';\nimport { DEXIE_STORAGE_MUTATED_EVENT_NAME, globalEvents } from '../../globals/global-events';\nimport { signalSubscribersNow } from '../../live-query/cache/signalSubscribers';\n\nexport function dexieOpen (db: Dexie) {\n  const state = db._state;\n  const {indexedDB} = db._deps;\n  if (state.isBeingOpened || db.idbdb)\n      return state.dbReadyPromise.then<Dexie>(() => state.dbOpenError ?\n        rejection (state.dbOpenError) :\n        db);\n  state.isBeingOpened = true;\n  state.dbOpenError = null;\n  state.openComplete = false;\n  const openCanceller = state.openCanceller;\n  let nativeVerToOpen = Math.round(db.verno * 10);\n  let schemaPatchMode = false;\n\n  function throwIfCancelled() {\n    // If state.openCanceller object reference is replaced, it means db.close() has been called,\n    // meaning this open flow should be cancelled.\n    if (state.openCanceller !== openCanceller) throw new exceptions.DatabaseClosed('db.open() was cancelled');\n  }\n  \n  // Function pointers to call when the core opening process completes.\n  let resolveDbReady = state.dbReadyResolve,\n      // upgradeTransaction to abort on failure.\n      upgradeTransaction: (IDBTransaction | null) = null,\n      wasCreated = false;\n\n  const tryOpenDB = () => new Promise((resolve, reject) => {\n    throwIfCancelled();\n    // If no API, throw!\n    if (!indexedDB) throw new exceptions.MissingAPI();\n    const dbName = db.name;\n    \n    const req = state.autoSchema || !nativeVerToOpen ?\n      indexedDB.open(dbName) :\n      indexedDB.open(dbName, nativeVerToOpen);\n    if (!req) throw new exceptions.MissingAPI(); // May happen in Safari private mode, see https://github.com/dfahlander/Dexie.js/issues/134\n    req.onerror = eventRejectHandler(reject);\n    req.onblocked = wrap(db._fireOnBlocked);\n    req.onupgradeneeded = wrap (e => {\n        upgradeTransaction = req.transaction;\n        if (state.autoSchema && !db._options.allowEmptyDB) { // Unless an addon has specified db._allowEmptyDB, lets make the call fail.\n            // Caller did not specify a version or schema. Doing that is only acceptable for opening alread existing databases.\n            // If onupgradeneeded is called it means database did not exist. Reject the open() promise and make sure that we\n            // do not create a new database by accident here.\n            req.onerror = preventDefault; // Prohibit onabort error from firing before we're done!\n            upgradeTransaction.abort(); // Abort transaction (would hope that this would make DB disappear but it doesnt.)\n            // Close database and delete it.\n            req.result.close();\n            const delreq = indexedDB.deleteDatabase(dbName); // The upgrade transaction is atomic, and javascript is single threaded - meaning that there is no risk that we delete someone elses database here!\n            delreq.onsuccess = delreq.onerror = wrap(() => {\n                reject (new exceptions.NoSuchDatabase(`Database ${dbName} doesnt exist`));\n            });\n        } else {\n            upgradeTransaction.onerror = eventRejectHandler(reject);\n            const oldVer = e.oldVersion > Math.pow(2, 62) ? 0 : e.oldVersion; // Safari 8 fix.\n            wasCreated = oldVer < 1;\n            db.idbdb = req.result;\n            if (schemaPatchMode) {\n              patchCurrentVersion(db, upgradeTransaction);\n            }\n            runUpgraders(db, oldVer / 10, upgradeTransaction, reject);\n        }\n    }, reject);\n    \n    req.onsuccess = wrap (() => {\n        // Core opening procedure complete. Now let's just record some stuff.\n        upgradeTransaction = null;\n        const idbdb = db.idbdb = req.result;\n\n        const objectStoreNames = slice(idbdb.objectStoreNames);\n        if (objectStoreNames.length > 0) try {\n          const tmpTrans = idbdb.transaction(safariMultiStoreFix(objectStoreNames), 'readonly');\n          if (state.autoSchema) readGlobalSchema(db, idbdb, tmpTrans);\n          else {\n              adjustToExistingIndexNames(db, db._dbSchema, tmpTrans);\n              if (!verifyInstalledSchema(db, tmpTrans) && !schemaPatchMode) {\n                console.warn(`Dexie SchemaDiff: Schema was extended without increasing the number passed to db.version(). Dexie will add missing parts and increment native version number to workaround this.`);\n                idbdb.close();\n                nativeVerToOpen = idbdb.version + 1;\n                schemaPatchMode = true;\n                return resolve (tryOpenDB()); // Try again with new version (nativeVerToOpen\n              }\n          }\n          generateMiddlewareStacks(db, tmpTrans);\n        } catch (e) {\n          // Safari 8 may bail out if > 1 store names. However, this shouldnt be a showstopper. Issue #120.\n          // BUGBUG: It will bail out anyway as of Dexie 3.\n          // Should we support Safari 8 anymore? Believe all\n          // Dexie users use the shim for that platform anyway?!\n          // If removing Safari 8 support, go ahead and remove the safariMultiStoreFix() function\n          // as well as absurd upgrade version quirk for Safari.\n        }\n        \n        connections.push(db); // Used for emulating versionchange event on IE/Edge/Safari.\n        \n        idbdb.onversionchange = wrap(ev => {\n            state.vcFired = true; // detect implementations that not support versionchange (IE/Edge/Safari)\n            db.on(\"versionchange\").fire(ev);\n        });\n        \n        idbdb.onclose = wrap(ev => {\n            db.on(\"close\").fire(ev);\n        });\n\n        if (wasCreated) _onDatabaseCreated(db._deps, dbName);\n\n        resolve();\n\n    }, reject);\n  }).catch(err => {\n    switch (err?.name) {\n      case \"UnknownError\":\n        if (state.PR1398_maxLoop > 0) {\n          // Bug in Chrome after clearing site data\n          // https://github.com/dexie/Dexie.js/issues/543#issuecomment-1795736695\n          state.PR1398_maxLoop--;\n          console.warn('Dexie: Workaround for Chrome UnknownError on open()');\n          return tryOpenDB();\n        }\n        break;\n      case \"VersionError\":\n        if (nativeVerToOpen > 0) {\n          nativeVerToOpen = 0;\n          return tryOpenDB();\n        }\n        break;\n    }\n    return Promise.reject(err);\n  });\n  \n  // safari14Workaround = Workaround by jakearchibald for new nasty bug in safari 14.\n  return Promise.race([\n    openCanceller,\n    (typeof navigator === 'undefined' ? Promise.resolve() : safari14Workaround()).then(tryOpenDB)\n  ]).then(() => {\n      // Before finally resolving the dbReadyPromise and this promise,\n      // call and await all on('ready') subscribers:\n      // Dexie.vip() makes subscribers able to use the database while being opened.\n      // This is a must since these subscribers take part of the opening procedure.\n      throwIfCancelled();\n      state.onReadyBeingFired = [];\n      return Promise.resolve(vip(()=>db.on.ready.fire(db.vip))).then(function fireRemainders() {\n          if (state.onReadyBeingFired.length > 0) {\n              // In case additional subscribers to db.on('ready') were added during the time db.on.ready.fire was executed.\n              let remainders = state.onReadyBeingFired.reduce(promisableChain, nop);\n              state.onReadyBeingFired = [];\n              return Promise.resolve(vip(()=>remainders(db.vip))).then(fireRemainders)\n          }\n      });\n  }).finally(()=>{\n      if (state.openCanceller === openCanceller) {\n        // Only modify state if not cancelled in the mean time.\n        state.onReadyBeingFired = null;\n        state.isBeingOpened = false;\n      }\n  }).catch(err => {\n      state.dbOpenError = err; // Record the error. It will be used to reject further promises of db operations.\n      try {\n        // Did we fail within onupgradeneeded? Make sure to abort the upgrade transaction so it doesnt commit.\n        upgradeTransaction && upgradeTransaction.abort();\n      } catch { }\n      if (openCanceller === state.openCanceller) {\n        // Still in the same open flow - The error reason was not due to external call to db.close().\n        // Make sure to call db.close() to finalize resources.\n        db._close(); // Closes and resets idbdb, removes connections, resets dbReadyPromise and openCanceller so that a later db.open() is fresh.\n      }\n      return rejection (err);\n  }).finally(()=>{\n    state.openComplete = true;\n    resolveDbReady(); // dbReadyPromise is resolved no matter if open() rejects or resolved. It's just to wake up waiters.\n  }).then(()=>{\n    if (wasCreated) {\n      // Propagate full range on primary keys and indexes on all tables now that the DB is ready and opened,\n      // and all upgraders and on('ready') subscribers have run.\n      const everything: ObservabilitySet = {};\n      db.tables.forEach(table => {\n        table.schema.indexes.forEach(idx => {\n          if (idx.name) everything[`idb://${db.name}/${table.name}/${idx.name}`] = new RangeSet(-Infinity, [[[]]]);\n        });\n        everything[`idb://${db.name}/${table.name}/`] = everything[`idb://${db.name}/${table.name}/:dels`] = new RangeSet(-Infinity, [[[]]]);\n      });\n      // Database was created. If another tab had it open when it was deleted and reopened, that tab must be updated now.\n      globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME).fire(everything);\n      // Wipe the cache and trigger optimistic queries:\n      signalSubscribersNow(everything, true);\n    }\n    // Resolve the db.open() with the db instance.\n    return db;\n  });\n}\n", "import { isArray } from '../functions/utils';\n\nexport function awaitIterator (iterator: Iterator<any>) {\n  var callNext = result => iterator.next(result),\n      doThrow = error => iterator.throw(error),\n      onSuccess = step(callNext),\n      onError = step(doThrow);\n\n  function step(getNext: (any)=>any) {\n      return (val?) => {\n          var next = getNext(val),\n              value = next.value;\n\n          return next.done ? value :\n              (!value || typeof value.then !== 'function' ?\n                  isArray(value) ? Promise.all(value).then(onSuccess, onError) : onSuccess(value) :\n                  value.then(onSuccess, onError));\n      };\n  }\n\n  return step(callNext)();\n}\n", "import { TransactionMode } from '../../public/types/transaction-mode';\nimport { errnames, exceptions } from '../../errors';\nimport { flatten, isAsyncFunction } from '../../functions/utils';\nimport { <PERSON>ie } from './dexie';\nimport { Transaction } from '../transaction';\nimport { await<PERSON>terator } from '../../helpers/yield-support';\nimport Promise, {\n  PSD,\n  NativePromise,\n  decrementExpectedAwaits,\n  rejection,\n  incrementExpectedAwaits\n} from '../../helpers/promise';\n\nexport function extractTransactionArgs(mode: TransactionMode, _tableArgs_, scopeFunc) {\n  // Let table arguments be all arguments between mode and last argument.\n  var i = arguments.length;\n  if (i < 2) throw new exceptions.InvalidArgument(\"Too few arguments\");\n  // Prevent optimzation killer (https://github.com/petkaantonov/bluebird/wiki/Optimization-killers#32-leaking-arguments)\n  // and clone arguments except the first one into local var 'args'.\n  var args = new Array(i - 1);\n  while (--i) args[i - 1] = arguments[i];\n  // Let scopeFunc be the last argument and pop it so that args now only contain the table arguments.\n  scopeFunc = args.pop();\n  var tables = flatten(args); // Support using array as middle argument, or a mix of arrays and non-arrays.\n  return [mode, tables, scopeFunc];\n}\n\nexport function enterTransactionScope(\n  db: Dexie,\n  mode: IDBTransactionMode,\n  storeNames: string[],\n  parentTransaction: Transaction | undefined,\n  scopeFunc: ()=>PromiseLike<any> | any\n) {\n  return Promise.resolve().then(() => {\n    // Keep a pointer to last non-transactional PSD to use if someone calls Dexie.ignoreTransaction().\n    const transless = PSD.transless || PSD;\n    // Our transaction.\n    //return new Promise((resolve, reject) => {\n    const trans = db._createTransaction(mode, storeNames, db._dbSchema, parentTransaction);\n    trans.explicit = true;\n    // Let the transaction instance be part of a Promise-specific data (PSD) value.\n    const zoneProps = {\n      trans: trans,\n      transless: transless\n    };\n\n    if (parentTransaction) {\n      // Emulate transaction commit awareness for inner transaction (must 'commit' when the inner transaction has no more operations ongoing)\n      trans.idbtrans = parentTransaction.idbtrans;\n    } else {\n      try {\n        trans.create(); // Create the native transaction so that complete() or error() will trigger even if no operation is made upon it.\n        // @ts-ignore Mark the idbtrans object with \"_explicit\". DBCore middleware won't have access to Dexie trans but will need to have this info.\n        trans.idbtrans._explicit = true;\n        db._state.PR1398_maxLoop = 3;\n      } catch (ex) {\n        if (ex.name === errnames.InvalidState && db.isOpen() && --db._state.PR1398_maxLoop > 0) {\n          console.warn('Dexie: Need to reopen db');\n          db.close({disableAutoOpen: false});\n          return db.open().then(() => enterTransactionScope(\n            db,\n            mode,\n            storeNames,\n            null,\n            scopeFunc\n          ));\n        }\n        return rejection(ex);\n      }\n    }\n\n    // Support for native async await.\n    const scopeFuncIsAsync = isAsyncFunction(scopeFunc);\n    if (scopeFuncIsAsync) {\n      incrementExpectedAwaits();\n    }\n\n    let returnValue;\n    const promiseFollowed = Promise.follow(() => {\n      // Finally, call the scope function with our table and transaction arguments.\n      returnValue = scopeFunc.call(trans, trans);\n      if (returnValue) {\n        if (scopeFuncIsAsync) {\n          // scopeFunc is a native async function - we know for sure returnValue is native promise.\n          var decrementor = decrementExpectedAwaits.bind(null, null);\n          returnValue.then(decrementor, decrementor);\n        } else if (typeof returnValue.next === 'function' && typeof returnValue.throw === 'function') {\n          // scopeFunc returned an iterator with throw-support. Handle yield as await.\n          returnValue = awaitIterator(returnValue);\n        }\n      }\n    }, zoneProps);\n    return (returnValue && typeof returnValue.then === 'function' ?\n      // Promise returned. User uses promise-style transactions.\n      Promise.resolve(returnValue).then(x => trans.active ?\n        x // Transaction still active. Continue.\n        : rejection(new exceptions.PrematureCommit(\n          \"Transaction committed too early. See http://bit.ly/2kdckMn\")))\n      // No promise returned. Wait for all outstanding promises before continuing. \n      : promiseFollowed.then(() => returnValue)\n    ).then(x => {\n      // sub transactions don't react to idbtrans.oncomplete. We must trigger a completion:\n      if (parentTransaction) trans._resolve();\n      // wait for trans._completion\n      // (if root transaction, this means 'complete' event. If sub-transaction, we've just fired it ourselves)\n      return trans._completion.then(() => x);\n    }).catch(e => {\n      trans._reject(e); // Yes, above then-handler were maybe not called because of an unhandled rejection in scopeFunc!\n      return rejection(e);\n    });\n  });\n}\n", "import {\n  DBCore,\n  DBCoreIndex,\n  DBCoreKeyRange,\n  DBCoreQueryRequest,\n  DBCoreRangeType,\n  DBCoreOpenCursorRequest,\n  DBCoreCountRequest,\n  DBCoreCursor,\n  DBCoreTable,\n} from \"../public/types/dbcore\";\nimport { isArray } from '../functions/utils';\nimport { getKeyExtractor } from './get-key-extractor';\nimport { getKeyPathAlias } from './dbcore-indexeddb';\nimport { Middleware } from '../public/types/middleware';\n\ninterface VirtualIndex extends DBCoreIndex {\n  /** True if this index is virtual, i.e. represents a compound index internally,\n   * but makes it act as as having a subset of its keyPaths.\n   */\n  isVirtual: boolean;\n\n  /** Number of keypaths that this index comprises. Can be 0..N.\n   * Note: This is the length of the *virtual index*, not the real index.\n   */\n  keyLength: number;\n\n  /** Number of popped keypaths from the real index.\n   */\n  keyTail: number;\n\n  /** LowLevelIndex represents the actual IndexedDB index behind it */\n  lowLevelIndex: DBCoreIndex;\n}\n\n// Move into some util:\nexport function pad (a: any | any[], value: any, count: number) {\n  const result = isArray(a) ? a.slice() : [a];\n  for (let i=0; i<count; ++i) result.push(value);\n  return result;\n}\n\n\nexport function createVirtualIndexMiddleware (down: DBCore) : DBCore {\n  return {\n    ...down,\n    table(tableName: string) {\n      const table = down.table(tableName);\n      const {schema} = table;\n      const indexLookup: {[indexAlias: string]: VirtualIndex[]} = {};\n      const allVirtualIndexes: VirtualIndex[] = [];\n\n      function addVirtualIndexes (keyPath: null | string | string[], keyTail: number, lowLevelIndex: DBCoreIndex): VirtualIndex {\n        const keyPathAlias = getKeyPathAlias(keyPath);\n        const indexList = (indexLookup[keyPathAlias] = indexLookup[keyPathAlias] || []);\n        const keyLength = keyPath == null ? 0: typeof keyPath === 'string' ? 1 : keyPath.length;\n        const isVirtual = keyTail > 0;\n        const virtualIndex = {\n          ...lowLevelIndex,\n          name: isVirtual\n            ? `${keyPathAlias}(virtual-from:${lowLevelIndex.name})`\n            : lowLevelIndex.name,\n          lowLevelIndex,\n          isVirtual,\n          keyTail,\n          keyLength,\n          extractKey: getKeyExtractor(keyPath),\n          unique: !isVirtual && lowLevelIndex.unique\n        };\n        indexList.push(virtualIndex);\n        if (!virtualIndex.isPrimaryKey) {\n          allVirtualIndexes.push(virtualIndex);\n        }\n        if (keyLength > 1) {\n          const virtualKeyPath = keyLength === 2 ?\n            keyPath[0] : // This is a compound [a, b]. Add a virtual normal index a.\n            keyPath.slice(0, keyLength - 1); // This is compound [a,b,c]. Add virtual compound [a,b].\n          addVirtualIndexes(virtualKeyPath, keyTail + 1, lowLevelIndex);\n        }\n        indexList.sort((a,b) => a.keyTail - b.keyTail); // Shortest keyTail is the best one (represents real index)\n        return virtualIndex;\n      }\n    \n      const primaryKey = addVirtualIndexes(schema.primaryKey.keyPath, 0, schema.primaryKey);\n      indexLookup[\":id\"] = [primaryKey];\n      for (const index of schema.indexes) {\n        addVirtualIndexes(index.keyPath, 0, index);\n      }\n    \n      function findBestIndex(keyPath: null | string | string[]): VirtualIndex {\n        const result = indexLookup[getKeyPathAlias(keyPath)];\n        return result && result[0];\n      }\n    \n      function translateRange (range: DBCoreKeyRange, keyTail: number): DBCoreKeyRange {\n        return {\n          type: range.type === DBCoreRangeType.Equal ?\n            DBCoreRangeType.Range :\n            range.type,\n          lower: pad(range.lower, range.lowerOpen ? down.MAX_KEY : down.MIN_KEY, keyTail),\n          lowerOpen: true, // doesn't matter true or false\n          upper: pad(range.upper, range.upperOpen ? down.MIN_KEY : down.MAX_KEY, keyTail),\n          upperOpen: true // doesn't matter true or false\n        };\n      }\n    \n      function translateRequest (req: DBCoreQueryRequest): DBCoreQueryRequest;\n      function translateRequest (req: DBCoreOpenCursorRequest): DBCoreOpenCursorRequest;\n      function translateRequest (req: DBCoreCountRequest): DBCoreCountRequest {\n        const index = req.query.index as VirtualIndex;\n        return index.isVirtual ? {\n          ...req,\n          query: {\n            index: index.lowLevelIndex,\n            range: translateRange(req.query.range, index.keyTail)\n          }\n        } : req;\n      }\n    \n      const result: DBCoreTable = {\n        ...table,\n        schema: {\n          ...schema,\n          primaryKey,\n          indexes: allVirtualIndexes,\n          getIndexByKeyPath: findBestIndex\n        },\n\n        count(req) {\n          return table.count(translateRequest(req));\n        },    \n    \n        query(req) {\n          return table.query(translateRequest(req));\n        },\n    \n        openCursor(req) {\n          const {keyTail, isVirtual, keyLength} = (req.query.index as VirtualIndex);\n          if (!isVirtual) return table.openCursor(req);\n    \n          function createVirtualCursor(cursor: DBCoreCursor) : DBCoreCursor {\n            function _continue (key?: any) {\n              key != null ?\n                cursor.continue(pad(key, req.reverse ? down.MAX_KEY : down.MIN_KEY, keyTail)) :\n                req.unique ?\n                  cursor.continue(\n                    cursor.key.slice(0, keyLength)\n                      .concat(req.reverse\n                        ? down.MIN_KEY\n                        : down.MAX_KEY, keyTail)\n                  ) :\n                  cursor.continue()\n            }\n            const virtualCursor = Object.create(cursor, {\n              continue: {value: _continue},\n              continuePrimaryKey: {\n                value(key: any, primaryKey: any) {\n                  cursor.continuePrimaryKey(pad(key, down.MAX_KEY, keyTail), primaryKey);\n                }\n              },\n              primaryKey: {\n                get() {\n                  return cursor.primaryKey;\n                }\n              },\n              key: {\n                get() {\n                  const key = cursor.key as any[]; // A virtual cursor always operates on compound key\n                  return keyLength === 1 ?\n                    key[0] : // Cursor.key should not be an array.\n                    key.slice(0, keyLength); // Cursor.key should be first part of array.\n                }\n              },\n              value: {\n                get() {\n                  return cursor.value;\n                }\n              }\n            });\n            return virtualCursor;\n          }\n    \n          return table.openCursor(translateRequest(req))\n            .then(cursor => cursor && createVirtualCursor(cursor));\n        }\n      };\n      return result;\n    }\n  }\n}\n\nexport const virtualIndexMiddleware : Middleware<DBCore> = {\n  stack: \"dbcore\",\n  name: \"VirtualIndexMiddleware\",\n  level: 1,\n  create: createVirtualIndexMiddleware\n};\n\n", "import { keys, hasOwn, toStringTag } from './utils';\n\nexport function getObjectDiff(a: any, b: any, rv?: any, prfx?: string) {\n  // Compares objects a and b and produces a diff object.\n  rv = rv || {};\n  prfx = prfx || '';\n  keys(a).forEach((prop) => {\n    if (!hasOwn(b, prop)) {\n      // Property removed\n      rv[prfx + prop] = undefined;\n    } else {\n      var ap = a[prop],\n        bp = b[prop];\n      if (typeof ap === 'object' && typeof bp === 'object' && ap && bp) {\n        const apTypeName = toStringTag(ap);\n        const bpTypeName = toStringTag(bp);\n\n        if (apTypeName !== bpTypeName) {\n          rv[prfx + prop] = b[prop]; // Property changed to other type\n        } else if (apTypeName === 'Object') {\n          // Pojo objects (not Date, ArrayBuffer, Array etc). Go deep.\n          getObjectDiff(ap, bp, rv, prfx + prop + '.');\n        } else if (ap !== bp) {\n          // Values differ.\n          // Could have checked if Date, arrays or binary types have same\n          // content here but I think that would be a suboptimation.\n          // Prefer simplicity.\n          rv[prfx + prop] = b[prop];\n        }\n      } else if (ap !== bp) rv[prfx + prop] = b[prop]; // Primitive value changed\n    }\n  });\n  keys(b).forEach((prop) => {\n    if (!hasOwn(a, prop)) {\n      rv[prfx + prop] = b[prop]; // Property added\n    }\n  });\n  return rv;\n}\n", "import {\n  DBCoreAddRequest,\n  DBCorePutRequest,\n  DBCoreDeleteRequest,\n  DBCoreIndex,\n  DBCoreTable,\n} from \"../public/types/dbcore\";\n\nexport function getEffectiveKeys (\n  primaryKey: DBCoreIndex,\n  req: (Pick<DBCoreAddRequest | DBCorePutRequest, \"type\" | \"values\"> & {keys?: any[]}) | Pick<DBCoreDeleteRequest, \"keys\" | \"type\">)\n{\n  //const {outbound} = primaryKey;\n  if (req.type === 'delete') return req.keys;\n  return req.keys || req.values.map(primaryKey.extractKey)\n}\n", "import {\n  DBCore,\n  DBCoreTable,\n  DBCoreMutateResponse,\n  DBCoreDeleteRangeRequest,\n  DBCoreAddRequest,\n  DBCorePutRequest,\n  DBCoreDeleteRequest,\n  DBCoreTransaction,\n  DBCoreKeyRange\n} from \"../public/types/dbcore\";\nimport { nop } from '../functions/chaining-functions';\nimport { hasOwn, setByKeyPath } from '../functions/utils';\nimport { getObjectDiff } from \"../functions/get-object-diff\";\nimport { PSD } from '../helpers/promise';\n//import { LockableTableMiddleware } from '../dbcore/lockable-table-middleware';\nimport { getEffectiveKeys } from '../dbcore/get-effective-keys';\nimport { Middleware } from '../public/types/middleware';\nimport { Transaction } from '../classes/transaction';\n\nexport const hooksMiddleware: Middleware<DBCore>  = {\n  stack: \"dbcore\",\n  name: \"HooksMiddleware\",\n  level: 2,\n  create: (downCore: DBCore) => ({\n    ...downCore,\n    table(tableName: string) {\n      const downTable = downCore.table(tableName);\n      const {primaryKey} = downTable.schema;\n  \n      const tableMiddleware: DBCoreTable = {\n        ...downTable,\n        mutate(req):Promise<DBCoreMutateResponse> {\n          const dxTrans = PSD.trans as Transaction;\n          // Hooks can be transaction-bound. Need to grab them from transaction.table and not\n          // db.table!\n          const {deleting, creating, updating} = dxTrans.table(tableName).hook;\n          switch (req.type) {\n            case 'add':\n              if (creating.fire === nop) break;\n              return dxTrans._promise('readwrite', ()=>addPutOrDelete(req), true);\n            case 'put':\n              if (creating.fire === nop && updating.fire === nop) break;\n              return dxTrans._promise('readwrite', ()=>addPutOrDelete(req), true);\n            case 'delete':\n              if (deleting.fire === nop) break;\n              return dxTrans._promise('readwrite', ()=>addPutOrDelete(req), true);\n            case 'deleteRange':\n              if (deleting.fire === nop) break;\n              return dxTrans._promise('readwrite', ()=>deleteRange(req), true);\n          }\n          // Any of the breaks above happened (no hooks) - do the default:\n          return downTable.mutate(req);\n\n\n          function addPutOrDelete(req: DBCoreAddRequest | DBCorePutRequest | DBCoreDeleteRequest): Promise<DBCoreMutateResponse> {\n            const dxTrans = PSD.trans;\n            const keys = req.keys || getEffectiveKeys(primaryKey, req);\n            if (!keys) throw new Error(\"Keys missing\");\n            // Clone Request and set keys arg\n            req = req.type === 'add' || req.type === 'put' ?\n              {...req, keys} :\n              {...req};\n            if (req.type !== 'delete') req.values = [...req.values];\n            if (req.keys) req.keys = [...req.keys];\n  \n            return getExistingValues(downTable, req, keys).then (existingValues => {\n              const contexts = keys.map((key, i) => {\n                const existingValue = existingValues[i];\n                const ctx = { onerror: null, onsuccess: null };\n                if (req.type === 'delete') {\n                  // delete operation\n                  deleting.fire.call(ctx, key, existingValue, dxTrans);\n                } else if (req.type === 'add' || existingValue === undefined) {\n                  // The add() or put() resulted in a create\n                  const generatedPrimaryKey = creating.fire.call(ctx, key, req.values[i], dxTrans);\n                  if (key == null && generatedPrimaryKey != null) {\n                    key = generatedPrimaryKey;\n                    req.keys[i] = key;\n                    if (!primaryKey.outbound) {\n                      setByKeyPath(req.values[i], primaryKey.keyPath, key);\n                    }\n                  }\n                } else {\n                  // The put() operation resulted in an update\n                  const objectDiff = getObjectDiff(existingValue, req.values[i]);\n                  const additionalChanges = updating.fire.call(ctx, objectDiff, key, existingValue, dxTrans);\n                  if (additionalChanges) {\n                    const requestedValue = req.values[i];\n                    Object.keys(additionalChanges).forEach(keyPath => {\n                      if (hasOwn(requestedValue, keyPath)) {\n                        // keyPath is already present as a literal property of the object\n                        requestedValue[keyPath] = additionalChanges[keyPath];\n                      } else {\n                        // keyPath represents a new or existing path into the object\n                        setByKeyPath(requestedValue, keyPath, additionalChanges[keyPath]);\n                      }\n                    });\n                  }\n                }\n                return ctx;\n              });\n              return downTable.mutate(req).then(({failures, results, numFailures, lastResult}) => {\n                for (let i=0; i<keys.length; ++i) {\n                  const primKey = results ? results[i] : keys[i];\n                  const ctx = contexts[i];\n                  if (primKey == null) {\n                    ctx.onerror && ctx.onerror(failures[i]);\n                  } else {\n                    ctx.onsuccess && ctx.onsuccess(\n                      req.type === 'put' && existingValues[i] ? // the put resulted in an update\n                        req.values[i] : // update hooks expects existing value\n                        primKey // create hooks expects primary key\n                    );\n                  }\n                }\n                return {failures, results, numFailures, lastResult};\n              }).catch(error => {\n                contexts.forEach(ctx => ctx.onerror && ctx.onerror(error));\n                return Promise.reject(error);\n              });\n            });\n          }\n  \n          function deleteRange(req: DBCoreDeleteRangeRequest): Promise<DBCoreMutateResponse> {\n            return deleteNextChunk(req.trans, req.range, 10000);\n          }\n  \n          function deleteNextChunk(trans: DBCoreTransaction, range: DBCoreKeyRange, limit: number) {\n            // Query what keys in the DB within the given range\n            return downTable.query({trans, values: false, query: {index: primaryKey, range}, limit})\n            .then(({result}) => {\n              // Given a set of keys, bulk delete those using the same procedure as in addPutOrDelete().\n              // This will make sure that deleting hook is called.\n              return addPutOrDelete({type: 'delete', keys: result, trans}).then(res => {\n                if (res.numFailures > 0) return Promise.reject(res.failures[0]);\n                if (result.length < limit) {\n                  return {failures: [], numFailures: 0, lastResult: undefined} as DBCoreMutateResponse;\n                } else {\n                  return deleteNextChunk(trans, {...range, lower: result[result.length - 1], lowerOpen: true}, limit);\n                }\n              });\n            })\n          }\n        }\n      };\n      //const {lock, lockableMiddleware} = LockableTableMiddleware(tableMiddleware);\n\n      return tableMiddleware;\n    },\n  }) as DBCore\n};\n\nfunction getExistingValues(\n  table: DBCoreTable,\n  req: DBCoreAddRequest | DBCorePutRequest | DBCoreDeleteRequest,\n  effectiveKeys: any[]\n) {\n  return req.type === \"add\"\n    ? Promise.resolve([])\n    : table.getMany({ trans: req.trans, keys: effectiveKeys, cache: \"immutable\" });\n}\n", "import { deepClone } from \"../functions/utils\";\nimport { DBCore } from \"../public/types/dbcore\";\nimport { Middleware } from \"../public/types/middleware\";\nimport Promise from \"../helpers/promise\";\nimport { cmp } from '../functions/cmp';\n\nexport function getFromTransactionCache(\n  keys: readonly any[],\n  cache: { keys: any[]; values: any[] } | undefined | null,\n  clone?: boolean\n) {\n  try {\n    if (!cache) return null;\n    if (cache.keys.length < keys.length) return null;\n    const result: any[] = [];\n    // Compare if the exact same order of keys was retrieved in same transaction:\n    // Allow some cached keys to be omitted from provided set of keys\n    // Use case: 1. getMany(keys) 2. update a subset of those 3. call put with the updated ones ==> middlewares should be able to find old values\n    for (let i = 0, j = 0; i < cache.keys.length && j < keys.length; ++i) {\n      if (cmp(cache.keys[i], keys[j]) !== 0) continue;\n      result.push(clone ? deepClone(cache.values[i]) : cache.values[i]);\n      ++j;\n    }\n    // If got all keys caller was looking for, return result.\n    return result.length === keys.length ? result : null;\n  } catch {\n    return null;\n  }\n}\n\nexport const cacheExistingValuesMiddleware: Middleware<DBCore> = {\n  stack: \"dbcore\",\n  level: -1,\n  create: (core) => {\n    return {\n      table: (tableName) => {\n        const table = core.table(tableName);\n        return {\n          ...table,\n          getMany: (req) => {\n            if (!req.cache) {\n              return table.getMany(req);\n            }\n            const cachedResult = getFromTransactionCache(\n              req.keys,\n              req.trans[\"_cache\"],\n              req.cache === \"clone\"\n            );\n            if (cachedResult) {\n              return Promise.resolve(cachedResult);\n            }\n            return table.getMany(req).then((res) => {\n              req.trans[\"_cache\"] = {\n                keys: req.keys,\n                values: req.cache === \"clone\" ? deepClone(res) : res,\n              };\n              return res;\n            });\n          },\n          mutate: (req) => {\n            // Invalidate cache on any mutate except \"add\" which can't change existing values:\n            if (req.type !== \"add\") req.trans[\"_cache\"] = null;\n            return table.mutate(req);\n          },\n        };\n      },\n    };\n  },\n};\n", "import { DB<PERSON>ore, DBCoreTable } from '../../public/types/dbcore';\nimport { LiveQueryContext } from '../live-query';\n\nexport function isCachableContext(ctx: LiveQueryContext, table: DBCoreTable) {\n  return (\n    ctx.trans.mode === 'readonly' &&\n    !!ctx.subscr &&\n    !ctx.trans.explicit &&\n    ctx.trans.db._options.cache !== 'disabled' &&\n    !table.schema.primaryKey.outbound\n  );\n}\n\n", "import { DBCoreCountRequest, DBCoreGetManyRequest, DBCoreGetRequest, DBCoreOpenCursorRequest, DBCoreQueryRequest } from '../../public/types/dbcore';\n\n\nexport function isCachableRequest(type: string, req: Partial<DBCoreQueryRequest & DBCoreCountRequest & DBCoreGetManyRequest & DBCoreGetRequest & DBCoreOpenCursorRequest>) {\n  switch (type) {\n    case 'query':\n      return req.values && !req.unique;\n    case 'get':\n      return false;\n    case 'getMany':\n      return false;\n    case 'count':\n      return false;\n    case 'openCursor':\n      return false;\n  }\n}\n", "import { LiveQueryContext } from \".\";\nimport { getFromTransactionCache } from \"../dbcore/cache-existing-values-middleware\";\nimport { getEffectiveKeys } from \"../dbcore/get-effective-keys\";\nimport { exceptions } from \"../errors\";\nimport { cmp } from \"../functions/cmp\";\nimport { isArray, keys } from \"../functions/utils\";\nimport { PSD } from \"../helpers/promise\";\nimport { RangeSet } from \"../helpers/rangeset\";\nimport { ObservabilitySet } from \"../public/types/db-events\";\nimport {\n  DBCore,\n  DBCoreCountRequest,\n  DBCoreCursor,\n  DBCoreGetManyRequest,\n  DBCoreGetRequest,\n  DBCoreIndex,\n  DBCoreOpenCursorRequest,\n  DBCoreQueryRequest,\n  DBCoreQueryResponse,\n  DBCoreTable,\n  DBCoreTableSchema,\n  DBCoreTransaction,\n} from \"../public/types/dbcore\";\nimport { Middleware } from \"../public/types/middleware\";\nimport { isCachableContext } from \"./cache/is-cachable-context\";\nimport { isCachableRequest } from \"./cache/is-cachable-request\";\nimport { extendObservabilitySet } from \"./extend-observability-set\";\n\nexport const observabilityMiddleware: Middleware<DBCore> = {\n  stack: \"dbcore\",\n  level: 0,\n  name: \"Observability\",\n  create: (core) => {\n    const dbName = core.schema.name;\n    const FULL_RANGE = new RangeSet(core.MIN_KEY, core.MAX_KEY);\n\n    return {\n      ...core,\n      transaction: (stores, mode, options) => {\n        if (PSD.subscr && mode !== 'readonly') {\n          throw new exceptions.ReadOnly(`Readwrite transaction in liveQuery context. Querier source: ${(PSD as LiveQueryContext).querier}`);\n        }\n        return core.transaction(stores, mode, options);\n      },\n      table: (tableName) => {\n        const table = core.table(tableName);\n        const { schema } = table;\n        const { primaryKey, indexes } = schema;\n        const { extractKey, outbound } = primaryKey;\n        const indexesWithAutoIncPK = primaryKey.autoIncrement && indexes.filter(\n          (index) => index.compound && (index.keyPath as string[]).includes(primaryKey.keyPath as string)\n        );\n        const tableClone: DBCoreTable = {\n          ...table,\n          mutate: (req) => {\n            const trans = req.trans as DBCoreTransaction & {\n              mutatedParts?: ObservabilitySet;\n            };\n            const mutatedParts = req.mutatedParts || (req.mutatedParts = {});\n            const getRangeSet = (indexName: string) => {\n              const part = `idb://${dbName}/${tableName}/${indexName}`;\n              return (mutatedParts[part] ||\n                (mutatedParts[part] = new RangeSet())) as RangeSet;\n            };\n            const pkRangeSet = getRangeSet(\"\");\n            const delsRangeSet = getRangeSet(\":dels\");\n\n            const { type } = req;\n            let [keys, newObjs] =\n              req.type === \"deleteRange\"\n                ? [req.range] // keys will be an DBCoreKeyRange object - transformed later on to a [from,to]-style range.\n                : req.type === \"delete\"\n                ? [req.keys] // keys known already here. newObjs will be undefined.\n                : req.values.length < 50\n                ? [getEffectiveKeys(primaryKey, req).filter(id => id), req.values] // keys except autoIncremented - they will be added later on.\n                : []; // keys and newObjs will both be undefined - changeSpec will become true (changed for entire table)\n\n            const oldCache = req.trans[\"_cache\"];\n\n            // Add the mutated table and optionally keys to the mutatedTables set on the transaction.\n            // Used by subscribers to txcommit event and for Collection.prototype.subscribe().\n            if (isArray(keys)) {\n              // keys is an array - delete, add or put of less than 50 rows.\n              // Individual keys (add put or delete)\n              pkRangeSet.addKeys(keys);\n              // Only get oldObjs if they have been cached recently\n              // (This applies to Collection.modify() only, but also if updating/deleting hooks have subscribers)\n              const oldObjs = type === 'delete' || keys.length === newObjs.length ? getFromTransactionCache(keys, oldCache) : null;\n\n              // Supply detailed values per index for both old and new objects:\n              if (!oldObjs) {\n                // add, delete or put and we don't know old values.\n                // Indicate this in the \":dels\" part, for the sake of count() and primaryKeys() queries only!\n                delsRangeSet.addKeys(keys);\n              }\n              if (oldObjs || newObjs) {\n                // No matter if knowning oldObjs or not, track the indices if it's a put, add or delete.\n                trackAffectedIndexes(getRangeSet, schema, oldObjs, newObjs);\n              }\n            } else if (keys) {\n              // keys is a DBCoreKeyRange object. Transform it to [from,to]-style range.\n              // As we can't know deleted index ranges, mark index-based subscriptions must trigger.\n              // (above/below-style ranges are not supported in RangeSet.ts, so we must replace open ends\n              // with core.MIN_KEY and core.MAX_KEY respectively. This is what solves issue #2067!\n              const range = {\n                from: keys.lower ?? core.MIN_KEY,\n                to: keys.upper ?? core.MAX_KEY\n              };\n              delsRangeSet.add(range);\n              // deleteRange. keys is a DBCoreKeyRange objects. Transform it to [from,to]-style range.\n              pkRangeSet.add(range);\n            } else {\n              // Too many requests to record the details without slowing down write performance.\n              // Let's just record a generic large range on primary key, the virtual :dels index and\n              // all secondary indices:\n              pkRangeSet.add(FULL_RANGE);\n              delsRangeSet.add(FULL_RANGE);\n              schema.indexes.forEach(idx => getRangeSet(idx.name).add(FULL_RANGE));\n            }\n\n            return table.mutate(req).then((res) => {\n              // Merge the mutated parts from the request into the transaction's mutatedParts\n              // now when the request went fine.\n              if (keys && (req.type === 'add' || req.type === 'put')) {\n                // Less than 50 requests (keys truthy) (otherwise we've added full range anyway)\n                // autoincrement means we might not have got all keys until now\n                pkRangeSet.addKeys(res.results);\n                if (indexesWithAutoIncPK) {\n                  // Dexie Issue 1946:\n                  // If an auto-incremented primary key is part of a compound index,\n                  // we need to compute the resulting value of that index after inserting\n                  // the rows.\n                  indexesWithAutoIncPK.forEach(idx => {\n                    // Extract values of this compound index where primary key is not yet set:\n                    const idxVals = req.values.map(v => idx.extractKey(v));\n                    // Find the position of the primary key in the index:\n                    const pkPos = (idx.keyPath as string[]).findIndex(prop => prop === primaryKey.keyPath);\n                    // Update idxVals with the resulting primary keys to complete the index value:\n                    for (let i = 0, len = res.results!.length; i < len; ++i) {\n                      idxVals[i][pkPos] = res.results![i];\n                    }\n                    // Add the updated index to the rangeset:\n                    getRangeSet(idx.name).addKeys(idxVals);\n                  });\n                }\n              }\n              trans.mutatedParts = extendObservabilitySet (\n                trans.mutatedParts || {},\n                mutatedParts\n              );\n              return res;\n            });\n          },\n        };\n\n        const getRange: (req: any) => [DBCoreIndex, RangeSet] = ({\n          query: { index, range },\n        }:\n          | DBCoreQueryRequest\n          | DBCoreCountRequest\n          | DBCoreOpenCursorRequest) => [\n          index,\n          new RangeSet(range.lower ?? core.MIN_KEY, range.upper ?? core.MAX_KEY),\n        ];\n\n        const readSubscribers: {[method in\n          Exclude<keyof DBCoreTable, \"name\" | \"schema\" | \"mutate\">]: \n          (req: any) => [DBCoreIndex, RangeSet]\n        } = {\n          get: (req) => [primaryKey, new RangeSet(req.key)],\n          getMany: (req) => [primaryKey, new RangeSet().addKeys(req.keys)],\n          count: getRange,\n          query: getRange,\n          openCursor: getRange,\n        }\n\n        keys(readSubscribers).forEach((method: 'get' | 'getMany' | 'count' | 'query' | 'openCursor') => {\n          tableClone[method] = function (\n            req:\n              | DBCoreGetRequest\n              | DBCoreGetManyRequest\n              | DBCoreQueryRequest\n              | DBCoreCountRequest\n              | DBCoreOpenCursorRequest\n          ) {\n            const { subscr } = PSD as LiveQueryContext;\n            const isLiveQuery = !!subscr;\n            let cachable = isCachableContext(PSD as LiveQueryContext, table) && isCachableRequest(method, req);\n            const obsSet = cachable\n              ? req.obsSet = {} // Implicit read transaction - track changes for this query only for the request's duration\n              : subscr; // Explicit read transaction - track changes across entire live query\n\n            if (isLiveQuery) {\n              // Current zone want's to track all queries so they can be subscribed to.\n              // (The query is executed within a \"liveQuery\" zone)\n              // Check whether the query applies to a certain set of ranges:\n              // Track what we should be observing:\n              const getRangeSet = (indexName: string) => {\n                const part = `idb://${dbName}/${tableName}/${indexName}`;\n                return (obsSet[part] ||\n                  (obsSet[part] = new RangeSet())) as RangeSet;\n              };\n              const pkRangeSet = getRangeSet(\"\");\n              const delsRangeSet = getRangeSet(\":dels\");\n              const [queriedIndex, queriedRanges] = readSubscribers[method](req);\n              // A generic rule here: queried ranges should always be subscribed to.\n              if (method === 'query' && queriedIndex.isPrimaryKey && !(req as DBCoreQueryRequest).values) {\n                // A pure primay-key based Collection where only .primaryKeys() is requested. Don't wakeup on other changes than added or deleted primary keys within queried range.\n                delsRangeSet.add(queriedRanges);\n              } else {\n                getRangeSet(queriedIndex.name || \"\").add(queriedRanges);\n              }\n              if (!queriedIndex.isPrimaryKey) {\n                // Only count(), query() and openCursor() operates on secondary indices.\n                // Since put(), delete() and deleteRange() mutations may happen without knowing oldObjs,\n                // the mutate() method will be missing what secondary indices that are being deleted from\n                // the subscribed range. We are working around this issue by recording all the resulting\n                // primary keys from the queries. This only works for those kinds of queries where we can\n                // derive the primary key from the result.\n                // In this block we are accomplishing this using various strategies depending on the properties\n                // of the query result.\n\n                if (method === \"count\") {\n                  // We've got a problem! Delete and put mutations happen without known the oldObjs.\n                  // Those mutation could change the count.\n                  // Solution: Dedicated \":dels\" url represends a subscription to all mutations without oldObjs\n                  // (specially triggered in the mutators put(), delete() and deleteRange() when they don't know oldObject)\n                  delsRangeSet.add(FULL_RANGE);\n                } else {\n                  // openCursor() or query()\n\n                  // Prepare a keysPromise in case the we're doing an IDBIndex.getAll() on a store with outbound keys.\n                  const keysPromise =\n                    method === \"query\" &&\n                    outbound &&\n                    (req as DBCoreQueryRequest).values &&\n                    table.query({\n                      ...(req as DBCoreQueryRequest),\n                      values: false,\n                    });\n\n                  return table[method].apply(this, arguments).then((res) => {\n                    if (method === \"query\") {\n                      if (outbound && (req as DBCoreQueryRequest).values) {\n                        // If keys are outbound, we can't use extractKey to map what keys to observe.\n                        // We've queried an index (like 'dateTime') on an outbound table\n                        // and retrieve a list of objects\n                        // from who we cannot know their primary keys.\n                        // \"Luckily\" though, we've prepared the keysPromise to assist us in exact this condition.\n                        return keysPromise.then(\n                          ({ result: resultingKeys }: DBCoreQueryResponse) => {\n                            pkRangeSet.addKeys(resultingKeys);\n                            return res;\n                          }\n                        );\n                      }\n                      // query() inbound values, keys or outbound keys. Secondary indexes only since\n                      // for primary keys we would only add results within the already registered range.\n                      const pKeys = (req as DBCoreQueryRequest).values\n                        ? (res as DBCoreQueryResponse).result.map(extractKey)\n                        : (res as DBCoreQueryResponse).result;\n                      if ((req as DBCoreQueryRequest).values) {\n                        // Subscribe to any mutation made on the returned keys,\n                        // so that we detect both deletions and updated properties.\n                        pkRangeSet.addKeys(pKeys);\n                      } else {\n                        // Subscribe only to mutations on the returned keys\n                        // in case the mutator was unable to know oldObjs.\n                        // If it has oldObj, the mutator won't put anything in \":dels\" because\n                        // it can more fine-grained put the exact removed and added index value in the correct\n                        // index range that we subscribe to in the queried range sets.\n                        // We don't load values so a change on a property outside our index will not\n                        // require us to re-execute the query.\n                        delsRangeSet.addKeys(pKeys);\n                      }\n                    } else if (method === \"openCursor\") {\n                      // Caller requests a cursor.\n                      // For the same reason as when method===\"query\", we only need to observe\n                      // those keys whose values are possibly used or rendered - which could\n                      // only happen on keys where they get the cursor's key, primaryKey or value.\n                      const cursor: DBCoreCursor | null = res;\n                      const wantValues = (req as DBCoreOpenCursorRequest).values;\n                      return (\n                        cursor &&\n                        Object.create(cursor, {\n                          key: {\n                            get() {\n                              delsRangeSet.addKey(cursor.primaryKey);\n                              return cursor.key;\n                            },\n                          },\n                          primaryKey: {\n                            get() {\n                              const pkey = cursor.primaryKey;\n                              delsRangeSet.addKey(pkey);\n                              return pkey;\n                            },\n                          },\n                          value: {\n                            get() {\n                              wantValues && pkRangeSet.addKey(cursor.primaryKey);\n                              return cursor.value;\n                            },\n                          },\n                        })\n                      );\n                    }\n                    return res;\n                  });\n                }\n              }\n            }\n            return table[method].apply(this, arguments);\n          };\n        });\n        return tableClone;\n      },\n    };\n  },\n};\n\nfunction trackAffectedIndexes(\n  getRangeSet: (index: string) => RangeSet,\n  schema: DBCoreTableSchema,\n  oldObjs: readonly any[] | undefined,\n  newObjs: readonly any[] | undefined\n) {\n  function addAffectedIndex(ix: DBCoreIndex) {\n    const rangeSet = getRangeSet(ix.name || \"\");\n    function extractKey(obj: any) {\n      return obj != null ? ix.extractKey(obj) : null;\n    }\n    const addKeyOrKeys = (key: any) => ix.multiEntry && isArray(key)\n      // multiEntry and the old property was an array - add each array entry to the rangeSet:\n      ? key.forEach(key => rangeSet.addKey(key))\n      // Not multiEntry or the old property was not an array - add each array entry to the rangeSet:\n      : rangeSet.addKey(key);\n\n    (oldObjs || newObjs).forEach((_, i) => {\n      const oldKey = oldObjs && extractKey(oldObjs[i]);\n      const newKey = newObjs && extractKey(newObjs[i]);\n      if (cmp(oldKey, newKey) !== 0) {\n        // The index has changed. Add both old and new value of the index.\n        if (oldKey != null) addKeyOrKeys(oldKey); // If oldKey is invalid key, addKey() will be a noop.\n        if (newKey != null) addKeyOrKeys(newKey); // If newKey is invalid key, addKey() will be a noop.\n      }\n    });\n  }\n  schema.indexes.forEach(addAffectedIndex);\n}\n", "import { delArrayItem, isArray } from '../../functions/utils';\nimport { TblQueryCache } from '../../public/types/cache';\nimport {\n  DBCoreMutateRequest,\n  DBCoreMutateResponse,\n} from '../../public/types/dbcore';\n\nexport function adjustOptimisticFromFailures(\n  tblCache: TblQueryCache,\n  req: DBCoreMutateRequest,\n  res: DBCoreMutateResponse\n): DBCoreMutateRequest {\n  if (res.numFailures === 0) return req;\n  if (req.type === 'deleteRange') {\n    // numFailures > 0 means the deleteRange operation failed in its whole.\n    return null;\n  }\n\n  const numBulkOps = req.keys\n    ? req.keys.length\n    : 'values' in req && req.values\n    ? req.values.length\n    : 1;\n  if (res.numFailures === numBulkOps) {\n    // Same number of failures as the number of ops. This means that all ops failed.\n    return null;\n  }\n\n  const clone: DBCoreMutateRequest = { ...req };\n\n  if (isArray(clone.keys)) {\n    clone.keys = clone.keys.filter((_, i) => !(i in res.failures));\n  }\n  if ('values' in clone && isArray(clone.values)) {\n    clone.values = clone.values.filter((_, i) => !(i in res.failures));\n  }\n  return clone;\n}\n", "import { cmp } from '../../functions/cmp';\nimport { IndexableType } from '../../public';\nimport { DBCoreKeyRange } from '../../public/types/dbcore';\n\nexport function isAboveLower(key: IndexableType, range: DBCoreKeyRange) {\n  return range.lower === undefined\n    ? true // lower is less than anything because it is undefined\n    : range.lowerOpen\n    ? cmp(key, range.lower) > 0 // lowerOpen: Exclude lower bound\n    : cmp(key, range.lower) >= 0; // !lowerOpen: Include lower bound\n}\n\nexport function isBelowUpper(key: IndexableType, range: DBCoreKeyRange) {\n  return range.upper === undefined\n    ? true // upper is greater than anything because it is undefined\n    : range.upperOpen\n    ? cmp(key, range.upper) < 0 // upperOpen: Exclude upper bound\n    : cmp(key, range.upper) <= 0; // !upperOpen: Include upper bound\n}\n\nexport function isWithinRange(key: IndexableType, range: DBCoreKeyRange) {\n  return isAboveLower(key, range) && isBelowUpper(key, range);\n}\n", "import { cmp } from '../../functions/cmp';\nimport { isArray } from '../../functions/utils';\nimport { RangeSet } from '../../helpers/rangeset';\nimport { CacheEntry } from '../../public/types/cache';\nimport {\n  DBCoreMutateRequest,\n  DBCoreQueryRequest,\n  DBCoreTable,\n} from '../../public/types/dbcore';\nimport { isWithinRange } from './is-within-range';\n\nexport function applyOptimisticOps(\n  result: any[],\n  req: DBCoreQueryRequest,\n  ops: DBCoreMutateRequest[] | undefined,\n  table: DBCoreTable,\n  cacheEntry: CacheEntry,\n  immutable: boolean\n): any[] {\n  if (!ops || ops.length === 0) return result;\n  const index = req.query.index;\n  const { multiEntry } = index;\n  const queryRange = req.query.range;\n  const primaryKey = table.schema.primaryKey;\n  const extractPrimKey = primaryKey.extractKey!;\n  const extractIndex = index.extractKey!;\n  const extractLowLevelIndex = (index.lowLevelIndex || index).extractKey!;\n\n  let finalResult = ops.reduce((result, op) => {\n    let modifedResult = result;\n    const includedValues: any[] = [];\n    if (op.type === 'add' || op.type === 'put') {\n      const includedPKs = new RangeSet(); // For ignoring duplicates\n      for (let i = op.values.length - 1; i >= 0; --i) {\n        // backwards to prioritize last value of same PK\n        const value = op.values[i];\n        const pk = extractPrimKey(value);\n        if (includedPKs.hasKey(pk)) continue;\n        const key = extractIndex(value);\n        if (\n          multiEntry && isArray(key)\n            ? key.some((k) => isWithinRange(k, queryRange))\n            : isWithinRange(key, queryRange)\n        ) {\n          includedPKs.addKey(pk);\n          includedValues.push(value);\n        }\n      }\n    }\n    switch (op.type) {\n      case 'add': {\n        const existingKeys = new RangeSet().addKeys(\n          req.values ? result.map((v) => extractPrimKey(v)) : result\n        );\n\n        modifedResult = result.concat(\n          req.values\n            ? includedValues.filter((v) => {\n                const key = extractPrimKey(v);\n                if (existingKeys.hasKey(key)) return false;\n                existingKeys.addKey(key);\n                return true;\n              })\n            : includedValues\n                .map((v) => extractPrimKey(v))\n                .filter((k) => {\n                  if (existingKeys.hasKey(k)) return false;\n                  existingKeys.addKey(k);\n                  return true;\n                })\n        );\n        break;\n      }\n      case 'put': {\n        const keySet = new RangeSet().addKeys(\n          op.values.map((v) => extractPrimKey(v))\n        );\n        modifedResult = result\n          .filter(\n            // Remove all items that are being replaced\n            (item) => !keySet.hasKey(req.values ? extractPrimKey(item) : item)\n          )\n          .concat(\n            // Add all items that are being put (sorting will be done later)\n            req.values\n              ? includedValues\n              : includedValues.map((v) => extractPrimKey(v))\n          );\n        break;\n      }\n      case 'delete':\n        const keysToDelete = new RangeSet().addKeys(op.keys);\n        modifedResult = result.filter(\n          (item) =>\n            !keysToDelete.hasKey(req.values ? extractPrimKey(item) : item)\n        );\n\n        break;\n      case 'deleteRange':\n        const range = op.range;\n        modifedResult = result.filter(\n          (item) => !isWithinRange(extractPrimKey(item), range)\n        );\n        break;\n    }\n    return modifedResult;\n  }, result);\n\n  // If no changes were made, we can return the original result.\n  if (finalResult === result) return result;\n\n  // Sort the result on sortIndex:\n  finalResult.sort((a, b) =>\n    cmp(extractLowLevelIndex(a), extractLowLevelIndex(b)) ||\n    cmp(extractPrimKey(a), extractPrimKey(b))\n  );\n\n  // If we have a limit we need to respect it:\n  if (req.limit && req.limit < Infinity) {\n    if (finalResult.length > req.limit) {\n      finalResult.length = req.limit; // Cut of any extras after sorting correctly.\n    } else if (result.length === req.limit && finalResult.length < req.limit) {\n      // We're missing some items because of the limit. We need to add them back.\n      // The easiest way is to mark the cache entry as dirty, which will cause\n      // it to be requeried after the write-transaction successfully completes.\n      cacheEntry.dirty = true;\n    }\n  }\n  return immutable ? Object.freeze(finalResult) as any[] : finalResult;\n}\n", "import { cmp } from '../../functions/cmp';\nimport { DBCoreKeyRange } from '../../public/types/dbcore';\n\nexport function areRangesEqual(r1: DBCoreKeyRange, r2: DBCoreKeyRange) {\n  return (\n    cmp(r1.lower, r2.lower) === 0 &&\n    cmp(r1.upper, r2.upper) === 0 &&\n    !!r1.lowerOpen === !!r2.lowerOpen &&\n    !!r1.upperOpen === !!r2.upperOpen\n  );\n}\n", "import { cmp } from '../../functions/cmp';\nimport { DBCoreKeyRange } from '../../public/types/dbcore';\n\nexport function compareLowers(lower1: any, lower2: any, lowerOpen1: boolean, lowerOpen2: boolean) {\n  if (lower1 === undefined) return lower2 !== undefined ? -1 : 0;\n  if (lower2 === undefined) return 1; // since lower1 !== undefined\n  const c = cmp(lower1, lower2);\n  if (c === 0) {\n    if (lowerOpen1 && lowerOpen2) return 0;\n    if (lowerOpen1) return 1\n    if (lowerOpen2) return -1;\n  }\n  return c;\n}\n\nexport function compareUppers(upper1: any, upper2: any, upperOpen1: boolean, upperOpen2: boolean) {\n  if (upper1 === undefined) return upper2 !== undefined ? 1 : 0;\n  if (upper2 === undefined) return -1; // since upper1 !== undefined\n  const c = cmp(upper1, upper2);\n  if (c === 0) {\n    if (upperOpen1 && upperOpen2) return 0;\n    if (upperOpen1) return -1\n    if (upperOpen2) return 1;\n  }\n  return c;\n}\n\nexport function isSuperRange(r1: DBCoreKeyRange, r2: DBCoreKeyRange) {\n  return (\n    compareLowers(r1.lower, r2.lower, r1.lowerOpen, r2.lowerOpen) <= 0 &&\n    compareUppers(r1.upper, r2.upper, r1.upperOpen, r2.upperOpen) >= 0\n  );\n}", "import { CacheEntry, TblQueryCache } from '../../public/types/cache';\nimport {\n  DBCoreCountRequest,\n  DBCoreQueryRequest,\n} from '../../public/types/dbcore';\nimport { areRangesEqual } from './are-ranges-equal';\nimport { cache } from './cache';\nimport { isSuperRange } from './is-super-range';\n\nexport function findCompatibleQuery(\n  dbName: string,\n  tableName: string,\n  type: 'query',\n  req: DBCoreQueryRequest\n): [] | [CacheEntry, boolean, TblQueryCache, CacheEntry[]];\nexport function findCompatibleQuery(\n  dbName: string,\n  tableName: string,\n  type: 'count',\n  req: DBCoreCountRequest\n): [] | [CacheEntry, boolean, TblQueryCache, CacheEntry[]];\nexport function findCompatibleQuery(\n  dbName: string,\n  tableName: string,\n  type: 'query' | 'count',\n  req: Partial<DBCoreQueryRequest> & Partial<DBCoreCountRequest>\n): [] | [CacheEntry, boolean, TblQueryCache, CacheEntry[]] {\n  const tblCache = cache[`idb://${dbName}/${tableName}`];\n  if (!tblCache) return [];\n  const queries = tblCache.queries[type];\n  if (!queries) return [null, false, tblCache, null];\n  const indexName = req.query ? req.query.index.name : null;\n  const entries = queries[indexName || ''];\n  if (!entries) return [null, false, tblCache, null];\n\n  switch (type) {\n    case 'query':\n      const equalEntry = entries.find(\n        (entry) =>\n          (entry.req as DBCoreQueryRequest).limit === req.limit &&\n          (entry.req as DBCoreQueryRequest).values === req.values &&\n          areRangesEqual(entry.req.query.range, req.query.range)\n      );\n      if (equalEntry)\n        return [\n          equalEntry,\n          true, // exact match\n          tblCache,\n          entries,\n        ];\n      const superEntry = entries.find((entry) => {\n        const limit = 'limit' in entry.req ? entry.req.limit : Infinity;\n        return (\n          limit >= req.limit &&\n          (req.values ? (entry.req as DBCoreQueryRequest).values : true) &&\n          isSuperRange(entry.req.query.range, req.query.range)\n        );\n      });\n      return [superEntry, false, tblCache, entries];\n    case 'count':\n      const countQuery = entries.find((entry) =>\n        areRangesEqual(entry.req.query.range, req.query.range)\n      );\n      return [countQuery, !!countQuery, tblCache, entries];\n  }\n}\n", "import { delArrayItem } from \"../../functions/utils\";\nimport { CacheEntry } from \"../../public/types/cache\";\n\nexport function subscribeToCacheEntry(cacheEntry: CacheEntry, container: CacheEntry[], requery: ()=>void, signal: AbortSignal) {\n  cacheEntry.subscribers.add(requery);\n  signal.addEventListener(\"abort\", () => {\n    cacheEntry.subscribers.delete(requery);\n    if (cacheEntry.subscribers.size === 0) {\n      enqueForDeletion(cacheEntry, container);\n    }\n  });\n}\n\n\nfunction enqueForDeletion(cacheEntry: CacheEntry, container: CacheEntry[]) {\n  setTimeout(() => {\n    if (cacheEntry.subscribers.size === 0) { // Still empty (no new subscribers readded after grace time)\n      delArrayItem(container, cacheEntry);\n    }\n  }, 3000);\n}\n", "import { LiveQueryContext } from '..';\nimport type { Transaction } from '../../classes/transaction';\nimport { getEffectiveKeys } from '../../dbcore/get-effective-keys';\nimport { deepClone, delArrayItem, setByKeyPath } from '../../functions/utils';\nimport DexiePromise, { PSD } from '../../helpers/promise';\nimport { ObservabilitySet } from '../../public/types/db-events';\nimport {\n  DBCore, DBCoreMutateRequest, DBCoreMutateResponse, DBCoreQueryRequest,\n  DBCoreQueryResponse\n} from '../../public/types/dbcore';\nimport { Middleware } from '../../public/types/middleware';\nimport { obsSetsOverlap } from '../obs-sets-overlap';\nimport { adjustOptimisticFromFailures } from './adjust-optimistic-request-from-failures';\nimport { applyOptimisticOps } from './apply-optimistic-ops';\nimport { cache } from './cache';\nimport { findCompatibleQuery } from './find-compatible-query';\nimport { isCachableContext } from './is-cachable-context';\nimport { isCachableRequest } from './is-cachable-request';\nimport { signalSubscribersLazily } from './signalSubscribers';\nimport { subscribeToCacheEntry } from './subscribe-cachentry';\n\nexport const cacheMiddleware: Middleware<DBCore> = {\n  stack: 'dbcore',\n  level: 0,\n  name: 'Cache',\n  create: (core) => {\n    const dbName = core.schema.name;\n    const coreMW: DBCore = {\n      ...core,\n      transaction: (stores, mode, options) => {\n        const idbtrans = core.transaction(\n          stores,\n          mode,\n          options\n        ) as IDBTransaction & {\n          mutatedParts?: ObservabilitySet;\n          _explicit?: boolean;\n        };\n        // Maintain TblQueryCache.ops array when transactions commit or abort\n        if (mode === 'readwrite') {\n          const ac = new AbortController();\n          const { signal } = ac;\n          const endTransaction = (wasCommitted: boolean) => () => {\n            ac.abort();\n            if (mode === 'readwrite') {\n              // Collect which subscribers to notify:\n              const affectedSubscribers = new Set<()=>void>();\n\n              // Go through all tables in transaction and check if they have any optimistic updates\n              for (const storeName of stores) {\n                const tblCache = cache[`idb://${dbName}/${storeName}`];\n                if (tblCache) {\n                  const table = core.table(storeName);\n                  // Pick optimistic ops that are part of this transaction\n                  const ops = tblCache.optimisticOps.filter(\n                    (op) => op.trans === idbtrans\n                  );\n                  // Transaction was marked as _explicit in enterTransactionScope(), transaction-helpers.ts.\n                  if (idbtrans._explicit && wasCommitted && idbtrans.mutatedParts) {\n                    // Invalidate all queries that overlap with the mutated parts and signal their subscribers\n                    for (const entries of Object.values(\n                      tblCache.queries.query\n                    )) {\n                      for (const entry of entries.slice()) {\n                        if (obsSetsOverlap(entry.obsSet, idbtrans.mutatedParts)) {\n                          delArrayItem(entries, entry); // Remove the entry from the cache so it can be refreshed\n                          entry.subscribers.forEach((requery) => affectedSubscribers.add(requery));\n                        }\n                      }\n                    }\n                  } else if (ops.length > 0) {\n                    // Remove them from the optimisticOps array\n                    tblCache.optimisticOps = tblCache.optimisticOps.filter(\n                      (op) => op.trans !== idbtrans\n                    );\n                    // Commit or abort the optimistic updates\n                    for (const entries of Object.values(\n                      tblCache.queries.query\n                    )) {\n                      for (const entry of entries.slice()) {\n                        if (\n                          entry.res != null && // if entry.promise but not entry.res, we're fine. Query will resume now and get the result.\n                          idbtrans.mutatedParts/* &&\n                          obsSetsOverlap(entry.obsSet, idbtrans.mutatedParts)*/\n                        ) {\n                          if (wasCommitted && !entry.dirty) {\n                            const freezeResults = Object.isFrozen(entry.res);\n                            const modRes = applyOptimisticOps(\n                              entry.res as any[],\n                              entry.req,\n                              ops,\n                              table,\n                              entry,\n                              freezeResults\n                            );\n                            if (entry.dirty) {\n                              // Found out at this point that the entry is dirty - not to rely on!\n                              delArrayItem(entries, entry);\n                              entry.subscribers.forEach((requery) => affectedSubscribers.add(requery));\n                            } else if (modRes !== entry.res) {\n                              entry.res = modRes;\n                              // Update promise\n                              entry.promise = DexiePromise.resolve({result: modRes} satisfies DBCoreQueryResponse);\n                              \n                              // No need to notify subscribers. They already have this value.\n                              // We have just updated the value of the cache without having to\n                              // requery the database - because we know the result for this\n                              // query based on computing the operations and applying them\n                              // to the previous result.\n                            }\n                          } else {\n                            if (entry.dirty) {\n                              // If the entry is dirty we need to get rid of it so that\n                              // a new entry will be created when the query is run again.\n                              delArrayItem(entries, entry);\n                            }\n                            // If we're not committing, we need to notify subscribers that the\n                            // optimistic updates are no longer valid.\n                            entry.subscribers.forEach((requery) => affectedSubscribers.add(requery));\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n              affectedSubscribers.forEach((requery) => requery());\n            }\n          };\n          idbtrans.addEventListener('abort', endTransaction(false), {\n            signal,\n          });\n          idbtrans.addEventListener('error', endTransaction(false), {\n            signal,\n          });\n          idbtrans.addEventListener('complete', endTransaction(true), {\n            signal,\n          });\n        }\n        return idbtrans;\n      },\n      table(tableName: string) {\n        const downTable = core.table(tableName);\n        const primKey = downTable.schema.primaryKey;\n        const tableMW = {\n          ...downTable,\n          mutate(req: DBCoreMutateRequest): Promise<DBCoreMutateResponse> {\n            const trans = PSD.trans as Transaction;\n            if (\n              primKey.outbound || // Non-inbound tables are harded to apply optimistic updates on because we can't know primary key of results\n              trans.db._options.cache === 'disabled' || // User has opted-out from caching\n              trans.explicit || // It's an explicit write transaction being made. Don't affect cache until transaction commits.\n              trans.idbtrans.mode !== 'readwrite' // We only handle 'readwrite' in our transaction override. 'versionchange' transactions don't use cache (from populate or upgraders).\n            ) {\n              // Just forward the request to the core.\n              return downTable.mutate(req);\n            }\n            // Find the TblQueryCache for this table:\n            const tblCache = cache[`idb://${dbName}/${tableName}`];\n            if (!tblCache) return downTable.mutate(req);\n\n            const promise = downTable.mutate(req);\n            if ((req.type === 'add' || req.type === 'put') && (req.values.length >= 50 || getEffectiveKeys(primKey, req).some(key => key == null))) {\n              // There are some autoIncremented keys not set yet. Need to wait for completion before we can reliably enqueue the operation.\n              // (or there are too many objects so we lazy out to avoid performance bottleneck for large bulk inserts)\n              promise.then((res) => { // We need to extract result keys and generate cloned values with the keys set (so that applyOptimisticOps can work)\n                // But we have a problem! The req.mutatedParts is still not complete so we have to actively add the keys to the unsignaledParts set manually.\n                const reqWithResolvedKeys = {\n                  ...req,\n                  values: req.values.map((value, i) => {\n                    if (res.failures[i]) return value; // No need to rewrite a failing value\n                    const valueWithKey = primKey.keyPath?.includes('.')\n                      ? deepClone(value)\n                      : {\n                        ...value,\n                      };\n                    setByKeyPath(valueWithKey, primKey.keyPath, res.results![i]);\n                    return valueWithKey;\n                  })\n                };\n                const adjustedReq = adjustOptimisticFromFailures(tblCache, reqWithResolvedKeys, res);\n                tblCache.optimisticOps.push(adjustedReq);\n                // Signal subscribers after the observability middleware has complemented req.mutatedParts with the new keys.\n                // We must queue the task so that we get the req.mutatedParts updated by observability middleware first.\n                // If we refactor the dependency between observability middleware and this middleware we might not need to queue the task.\n                queueMicrotask(()=>req.mutatedParts && signalSubscribersLazily(req.mutatedParts)); // Reason for double laziness: in user awaits put and then does another put, signal once.\n              });\n            } else {\n              // Enque the operation immediately\n              tblCache.optimisticOps.push(req);\n              // Signal subscribers that there are mutated parts\n              req.mutatedParts && signalSubscribersLazily(req.mutatedParts);\n              promise.then((res) => {\n                if (res.numFailures > 0) {\n                  // In case the operation failed, we need to remove it from the optimisticOps array.\n                  delArrayItem(tblCache.optimisticOps, req);\n                  const adjustedReq = adjustOptimisticFromFailures(tblCache, req, res);\n                  if (adjustedReq) {\n                    tblCache.optimisticOps.push(adjustedReq);\n                  }\n                  req.mutatedParts && signalSubscribersLazily(req.mutatedParts); // Signal the rolling back of the operation.\n                }\n              });\n              promise.catch(()=> {\n                // In case the operation failed, we need to remove it from the optimisticOps array.\n                delArrayItem(tblCache.optimisticOps, req);\n                req.mutatedParts && signalSubscribersLazily(req.mutatedParts); // Signal the rolling back of the operation.\n              });\n            }\n            return promise;\n          },\n          query(req: DBCoreQueryRequest): Promise<DBCoreQueryResponse> {\n            if (!isCachableContext(PSD, downTable) || !isCachableRequest(\"query\", req)) return downTable.query(req);\n            const freezeResults =\n              (PSD as LiveQueryContext).trans?.db._options.cache === 'immutable';\n            const { requery, signal } = PSD as LiveQueryContext;\n            let [cacheEntry, exactMatch, tblCache, container] =\n              findCompatibleQuery(dbName, tableName, 'query', req);\n            if (cacheEntry && exactMatch) {\n              cacheEntry.obsSet = req.obsSet!; // So that optimistic result is monitored.\n              // How? - because observability-middleware will track result where optimistic\n              // mutations are applied and record it in the cacheEntry.\n              // TODO: CHANGE THIS! The difference is resultKeys only.\n              // Wanted behavior:\n              //  * cacheEntry obsSet should represent the obsSet without optimistic updates (so it can be checked when merging ops in tx commit)\n              //  * cacheEntry optimisticObsSet should represent the obsSet with current optimistic updates. It should be updated when adding an op\n              //    by adding the primary keys of the put/add/delete operation to the set.\n              //  * observability-middleware should stop recording req.obsSet when a cache entry exact match is found because it won't be used anyway.\n              // I'm thinking of merging observability-middleware with cache-middleware into one single middleware because the dependencies are too\n              // tight between them.\n            } else {\n              // --> TODO here: If not exact match, check if we have a superset to extract\n              // the data from.\n\n              // No cached result found. We need to query the database and cache the result.\n              const promise = downTable.query(req).then((res) => {\n                // Freeze or clone results\n                const result = res.result;\n                if (cacheEntry) cacheEntry.res = result;\n                if (freezeResults) {\n                  // For performance reasons don't deep freeze.\n                  // Only freeze the top-level array and its items.\n                  // This is good enough to teach users that the result must be treated as immutable\n                  // without enforcing it recursively on the entire result (which is not even possible\n                  // for things like Date objects and typed arrays)\n                  for (let i = 0, l = result.length; i < l; ++i) {\n                    Object.freeze(result[i]);\n                  }\n                  Object.freeze(result);\n                } else {\n                  // If not frozen, we need to clone the result to avoid user mutating the cache\n                  // When we do this, user's must feel conformable with the fact that the result\n                  // can be mutated deeply - user is not expected to have any respect for immutability.\n                  res.result = deepClone(result);\n                }\n                return res;\n              }).catch(error => {\n                // In case the query operation failed, we need to remove it from the cache\n                // so that subsequent calls does not get the same error but re-evaluate\n                // the query.\n                if (container && cacheEntry) delArrayItem(container, cacheEntry);\n                return Promise.reject(error);\n              });\n              cacheEntry = {\n                obsSet: req.obsSet!,\n                promise,\n                subscribers: new Set(),\n                type: 'query',\n                req,\n                dirty: false,\n              };\n              if (container) {\n                container.push(cacheEntry);\n              } else {\n                container = [cacheEntry];\n                if (!tblCache) {\n                  tblCache = cache[`idb://${dbName}/${tableName}`] = {\n                    queries: {\n                      query: {},\n                      count: {},\n                    },\n                    objs: new Map(),\n                    optimisticOps: [],\n                    unsignaledParts: {}\n                  };\n                }\n                tblCache.queries.query[req.query.index.name || ''] = container;\n              }\n            }\n            subscribeToCacheEntry(cacheEntry, container!, requery, signal);\n            return cacheEntry.promise.then((res: DBCoreQueryResponse) => {\n              return {\n                result: applyOptimisticOps(\n                  res.result,\n                  req,\n                  tblCache?.optimisticOps,\n                  downTable,\n                  cacheEntry!,\n                  freezeResults\n                ) as any[], // readonly any[]\n              };\n            });\n          },\n        };\n        return tableMW;\n      },\n    };\n    return coreMW;\n  },\n};\n\n\n", "import { type <PERSON><PERSON> } from \"../classes/dexie\";\nimport { type Table } from \"../classes/table\";\nimport { type Transaction } from \"../classes/transaction\";\n\nexport function vipify<T extends Table | Transaction>(\n  target: T,\n  vipDb: <PERSON>ie\n): T {\n  return new Proxy(target, {\n    get (target, prop, receiver) {\n      // The \"db\" prop of the table or transaction is the only one we need to\n      // override. The rest of the props can be accessed from the original\n      // object.\n      if (prop === 'db') return vipDb;\n      return Reflect.get(target, prop, receiver);\n    }\n  });\n}\n", "// Import types from the public API\nimport { <PERSON><PERSON> as IDexie } from \"../../public/types/dexie\";\nimport { <PERSON>ieOptions, DexieConstructor } from \"../../public/types/dexie-constructor\";\nimport { DbEvents } from \"../../public/types/db-events\";\n//import { PromiseExtended, PromiseExtendedConstructor } from '../../public/types/promise-extended';\nimport { Table as ITable } from '../../public/types/table';\nimport { TableSchema } from \"../../public/types/table-schema\";\nimport { DbSchema } from '../../public/types/db-schema';\n\n// Internal imports\nimport { Table, TableConstructor, createTableConstructor } from \"../table\";\nimport { Collection, CollectionConstructor, createCollectionConstructor } from '../collection';\nimport { WhereClause } from '../where-clause/where-clause';\nimport { WhereClauseConstructor, createWhereClauseConstructor } from '../where-clause/where-clause-constructor';\nimport { Transaction } from '../transaction';\nimport { TransactionConstructor, createTransactionConstructor } from '../transaction/transaction-constructor';\nimport { Version } from \"../version/version\";\nimport { VersionConstructor, createVersionConstructor } from '../version/version-constructor';\n\n// Other imports...\nimport { DexieEventSet } from '../../public/types/dexie-event-set';\nimport { DexieExceptionClasses } from '../../public/types/errors';\nimport { DexieDOMDependencies } from '../../public/types/dexie-dom-dependencies';\nimport { nop, promisableChain } from '../../functions/chaining-functions';\nimport Promise, { PSD, globalPSD } from '../../helpers/promise';\nimport { extend, override, keys, hasOwn } from '../../functions/utils';\nimport Events from '../../helpers/Events';\nimport { maxString, connections, READONLY, READWRITE } from '../../globals/constants';\nimport { getMaxKey } from '../../functions/quirks';\nimport { exceptions } from '../../errors';\nimport { lowerVersionFirst } from '../version/schema-helpers';\nimport { dexieOpen } from './dexie-open';\nimport { wrap } from '../../helpers/promise';\nimport { _onDatabaseDeleted } from '../../helpers/database-enumerator';\nimport { eventRejectHandler } from '../../functions/event-wrappers';\nimport { extractTransactionArgs, enterTransactionScope } from './transaction-helpers';\nimport { TransactionMode } from '../../public/types/transaction-mode';\nimport { rejection } from '../../helpers/promise';\nimport { usePSD } from '../../helpers/promise';\nimport { DBCore } from '../../public/types/dbcore';\nimport { Middleware, DexieStacks } from '../../public/types/middleware';\nimport { virtualIndexMiddleware } from '../../dbcore/virtual-index-middleware';\nimport { hooksMiddleware } from '../../hooks/hooks-middleware';\nimport { IndexableType } from '../../public';\nimport { observabilityMiddleware } from '../../live-query/observability-middleware';\nimport { cacheExistingValuesMiddleware } from '../../dbcore/cache-existing-values-middleware';\nimport { cacheMiddleware } from \"../../live-query/cache/cache-middleware\";\nimport { vipify } from \"../../helpers/vipify\";\n\nexport interface DbReadyState {\n  dbOpenError: any;\n  isBeingOpened: boolean;\n  onReadyBeingFired: undefined | Function[];\n  openComplete: boolean;\n  dbReadyResolve: () => void;\n  dbReadyPromise: Promise<any>;\n  cancelOpen: (reason?: Error) => void;\n  openCanceller: Promise<any> & { _stackHolder?: Error };\n  autoSchema: boolean;\n  vcFired?: boolean;\n  PR1398_maxLoop?: number;\n  autoOpen?: boolean;\n}\n\nexport class Dexie implements IDexie {\n  _options: DexieOptions;\n  _state: DbReadyState;\n  _versions: Version[];\n  _storeNames: string[];\n  _deps: DexieDOMDependencies;\n  _allTables: { [name: string]: Table; };\n  _createTransaction: (this: Dexie, mode: IDBTransactionMode, storeNames: ArrayLike<string>, dbschema: { [tableName: string]: TableSchema; }, parentTransaction?: Transaction) => Transaction;\n  _dbSchema: { [tableName: string]: TableSchema; };\n  _hasGetAll?: boolean;\n  _maxKey: IndexableType;\n  _fireOnBlocked: (ev: Event) => void;\n  _middlewares: {[StackName in keyof DexieStacks]?: Middleware<DexieStacks[StackName]>[]} = {};\n  _vip?: boolean;\n  _novip: Dexie;// db._novip is to escape to orig db from db.vip.\n  core: DBCore;\n\n  name: string;\n  verno: number = 0;\n  idbdb: IDBDatabase | null;\n  vip: Dexie;\n  on: DbEvents;\n\n  Table: TableConstructor;\n  WhereClause: WhereClauseConstructor;\n  Collection: CollectionConstructor;\n  Version: VersionConstructor;\n  Transaction: TransactionConstructor;\n  static disableBfCache?: boolean;\n\n  constructor(name: string, options?: DexieOptions) {\n    const deps = (Dexie as any as DexieConstructor).dependencies;\n    this._options = options = {\n      // Default Options\n      addons: (Dexie as any as DexieConstructor).addons, // Pick statically registered addons by default\n      autoOpen: true,                 // Don't require db.open() explicitely.\n      // Default DOM dependency implementations from static prop.\n      indexedDB: deps.indexedDB,      // Backend IndexedDB api. Default to browser env.\n      IDBKeyRange: deps.IDBKeyRange,  // Backend IDBKeyRange api. Default to browser env.\n      cache: 'cloned', // Default to cloned for backward compatibility. For best performance and least memory consumption use 'immutable'.\n      ...options\n    };  \n    this._deps = {\n      indexedDB: options.indexedDB as IDBFactory,\n      IDBKeyRange: options.IDBKeyRange as typeof IDBKeyRange\n    };\n    const {\n      addons,\n    } = options;\n    this._dbSchema = {};\n    this._versions = [];\n    this._storeNames = [];\n    this._allTables = {};\n    this.idbdb = null;\n    this._novip = this;\n    const state: DbReadyState = {\n      dbOpenError: null,\n      isBeingOpened: false,\n      onReadyBeingFired: null,\n      openComplete: false,\n      dbReadyResolve: nop,\n      dbReadyPromise: null as Promise,\n      cancelOpen: nop,\n      openCanceller: null as Promise,\n      autoSchema: true,\n      PR1398_maxLoop: 3,\n      autoOpen: options.autoOpen,\n    };\n    state.dbReadyPromise = new Promise(resolve => {\n      state.dbReadyResolve = resolve;\n    });\n    state.openCanceller = new Promise((_, reject) => {\n      state.cancelOpen = reject;\n    });\n    this._state = state;\n    this.name = name;\n    this.on = Events(this, \"populate\", \"blocked\", \"versionchange\", \"close\", { ready: [promisableChain, nop] }) as DbEvents;\n    this.on.ready.subscribe = override(this.on.ready.subscribe, subscribe => {\n      return (subscriber, bSticky) => {\n        (Dexie as any as DexieConstructor).vip(() => {\n          const state = this._state;\n          if (state.openComplete) {\n            // Database already open. Call subscriber asap.\n            if (!state.dbOpenError) Promise.resolve().then(subscriber);\n            // bSticky: Also subscribe to future open sucesses (after close / reopen) \n            if (bSticky) subscribe(subscriber);\n          } else if (state.onReadyBeingFired) {\n            // db.on('ready') subscribers are currently being executed and have not yet resolved or rejected\n            state.onReadyBeingFired.push(subscriber);\n            if (bSticky) subscribe(subscriber);\n          } else {\n            // Database not yet open. Subscribe to it.\n            subscribe(subscriber);\n            // If bSticky is falsy, make sure to unsubscribe subscriber when fired once.\n            const db = this;\n            if (!bSticky) subscribe(function unsubscribe() {\n              db.on.ready.unsubscribe(subscriber);\n              db.on.ready.unsubscribe(unsubscribe);\n            });\n          }\n        });\n      }\n    });\n\n    // Create derived classes bound to this instance of Dexie:\n    this.Collection = createCollectionConstructor(this);\n    this.Table = createTableConstructor(this);\n    this.Transaction = createTransactionConstructor(this);\n    this.Version = createVersionConstructor(this);\n    this.WhereClause = createWhereClauseConstructor(this);\n\n    // Default subscribers to \"versionchange\" and \"blocked\".\n    // Can be overridden by custom handlers. If custom handlers return false, these default\n    // behaviours will be prevented.\n    this.on(\"versionchange\", ev => {\n      // Default behavior for versionchange event is to close database connection.\n      // Caller can override this behavior by doing db.on(\"versionchange\", function(){ return false; });\n      // Let's not block the other window from making it's delete() or open() call.\n      // NOTE! This event is never fired in IE,Edge or Safari.\n      if (ev.newVersion > 0)\n        console.warn(`Another connection wants to upgrade database '${this.name}'. Closing db now to resume the upgrade.`);\n      else\n        console.warn(`Another connection wants to delete database '${this.name}'. Closing db now to resume the delete request.`);\n      this.close({disableAutoOpen: false});\n      // In many web applications, it would be recommended to force window.reload()\n      // when this event occurs. To do that, subscribe to the versionchange event\n      // and call window.location.reload(true) if ev.newVersion > 0 (not a deletion)\n      // The reason for this is that your current web app obviously has old schema code that needs\n      // to be updated. Another window got a newer version of the app and needs to upgrade DB but\n      // your window is blocking it unless we close it here.\n    });\n    this.on(\"blocked\", ev => {\n      if (!ev.newVersion || ev.newVersion < ev.oldVersion)\n        console.warn(`Dexie.delete('${this.name}') was blocked`);\n      else\n        console.warn(`Upgrade '${this.name}' blocked by other connection holding version ${ev.oldVersion / 10}`);\n    });\n\n    this._maxKey = getMaxKey(options.IDBKeyRange as typeof IDBKeyRange);\n\n    this._createTransaction = (\n      mode: IDBTransactionMode,\n      storeNames: string[],\n      dbschema: DbSchema,\n      parentTransaction?: Transaction) => new this.Transaction(mode, storeNames, dbschema, this._options.chromeTransactionDurability, parentTransaction);\n\n    this._fireOnBlocked = ev => {\n      this.on(\"blocked\").fire(ev);\n      // Workaround (not fully*) for missing \"versionchange\" event in IE,Edge and Safari:\n      connections\n        .filter(c => c.name === this.name && c !== this && !c._state.vcFired)\n        .map(c => c.on(\"versionchange\").fire(ev));\n    }\n\n    // Default middlewares:\n    this.use(cacheExistingValuesMiddleware);\n    this.use(cacheMiddleware);\n    this.use(observabilityMiddleware);\n    this.use(virtualIndexMiddleware);\n    this.use(hooksMiddleware);\n\n    const vipDB = new Proxy(this, {\n      get: (_, prop, receiver) => {\n        if (prop === '_vip') return true;\n        if (prop === 'table') return (tableName: string) => vipify(this.table(tableName), vipDB);\n        const rv = Reflect.get(_, prop, receiver);\n        if (rv instanceof Table) return vipify(rv, vipDB);\n        if (prop === 'tables') return (rv as Table[]).map(t => vipify(t, vipDB));\n        if (prop === '_createTransaction') return function() {\n          const tx: Transaction = (rv as typeof this._createTransaction).apply(this, arguments);\n          return vipify(tx, vipDB);\n        }\n        return rv;\n      }\n    });\n    this.vip = vipDB;\n\n    // Call each addon:\n    addons.forEach(addon => addon(this));\n  }\n\n  version(versionNumber: number): Version {\n    if (isNaN(versionNumber) || versionNumber < 0.1) throw new exceptions.Type(`Given version is not a positive number`);\n    versionNumber = Math.round(versionNumber * 10) / 10;\n    if (this.idbdb || this._state.isBeingOpened)\n      throw new exceptions.Schema(\"Cannot add version when database is open\");\n    this.verno = Math.max(this.verno, versionNumber);\n    const versions = this._versions;\n    var versionInstance = versions.filter(\n      v => v._cfg.version === versionNumber)[0];\n    if (versionInstance) return versionInstance;\n    versionInstance = new this.Version(versionNumber);\n    versions.push(versionInstance);\n    versions.sort(lowerVersionFirst);\n    versionInstance.stores({}); // Derive earlier schemas by default.\n    // Disable autoschema mode, as at least one version is specified.\n    this._state.autoSchema = false;\n    return versionInstance;\n  }\n\n  _whenReady<T>(fn: () => Promise<T>): Promise<T> {\n    return (this.idbdb && (this._state.openComplete || PSD.letThrough || this._vip)) ? fn() : new Promise<T>((resolve, reject) => {\n      if (this._state.openComplete) {\n        // idbdb is falsy but openComplete is true. Must have been an exception durin open.\n        // Don't wait for openComplete as it would lead to infinite loop.\n        return reject(new exceptions.DatabaseClosed(this._state.dbOpenError));\n      }\n      if (!this._state.isBeingOpened) {\n        if (!this._state.autoOpen) {\n          reject(new exceptions.DatabaseClosed());\n          return;\n        }\n        this.open().catch(nop); // Open in background. If if fails, it will be catched by the final promise anyway.\n      }\n      this._state.dbReadyPromise.then(resolve, reject);\n    }).then(fn);\n  }\n\n  use({stack, create, level, name}: Middleware<DBCore>): this {\n    if (name) this.unuse({stack, name}); // Be able to replace existing middleware.\n    const middlewares = this._middlewares[stack] || (this._middlewares[stack] = []);\n    middlewares.push({stack, create, level: level == null ? 10 : level, name});\n    middlewares.sort((a, b) => a.level - b.level);\n    // Todo update db.core and db.tables...core ? Or should be expect this to have effect\n    // only after next open()?\n    return this;\n  }\n\n  unuse({stack, create}: Middleware<{stack: keyof DexieStacks}>): this;\n  unuse({stack, name}: {stack: keyof DexieStacks, name: string}): this;\n  unuse({stack, name, create}: {stack: keyof DexieStacks, name?: string, create?: Function}) {\n    if (stack && this._middlewares[stack]) {\n      this._middlewares[stack] = this._middlewares[stack].filter(mw =>\n        create ? mw.create !== create : // Given middleware has a create method. Match that exactly.\n        name ? mw.name !== name : // Given middleware spec \n        false);\n    }\n    return this;\n  }\n\n  open() {\n    return usePSD(\n      globalPSD, // Enforce global scope here since db.open() can be part of a live query or transaction scope\n      () => dexieOpen(this)\n    );\n  }\n\n  _close(): void {\n    const state = this._state;\n    const idx = connections.indexOf(this);\n    if (idx >= 0) connections.splice(idx, 1);\n    if (this.idbdb) {\n      try { this.idbdb.close(); } catch (e) { }\n      this.idbdb = null;\n    }    \n    // Reset dbReadyPromise promise:\n    if (!state.isBeingOpened) {\n      // Only if not being opened, reset these promises.\n      // Otherwise, keep them so existing promise consumers will resolve when db\n      // db is reopened later on, in case closing for purpose reopening, using {disableAutoOpen: false}.\n      state.dbReadyPromise = new Promise(resolve => {\n        state.dbReadyResolve = resolve;\n      });\n      state.openCanceller = new Promise((_, reject) => {\n        state.cancelOpen = reject;\n      });\n    }\n  }\n\n  close({disableAutoOpen} = {disableAutoOpen: true}): void {\n    const state = this._state;\n    if (disableAutoOpen) {\n      if (state.isBeingOpened) {\n        // cancel before the call to this._close() because this._close() will recreate dbReadyPromise and openCanceller.\n        state.cancelOpen(new exceptions.DatabaseClosed());\n      }\n      this._close();\n      state.autoOpen = false;\n      state.dbOpenError = new exceptions.DatabaseClosed();\n    } else {\n      this._close();\n      state.autoOpen = this._options.autoOpen ||\n        state.isBeingOpened; // If an open call is ongoing, that same promise will resolve when db is reopend.\n      state.openComplete = false;\n      state.dbOpenError = null;\n    }\n  }\n\n  delete(closeOptions = {disableAutoOpen: true}): Promise<void> {\n    // Prevent accidentially doing db.delete(1) when intention was to do db.[table].delete(1).\n    const hasInvalidArguments = arguments.length > 0 && typeof arguments[0] !== 'object'; \n    const state = this._state;\n    return new Promise((resolve, reject) => {\n      const doDelete = () => {\n        this.close(closeOptions);\n        var req = this._deps.indexedDB.deleteDatabase(this.name);\n        req.onsuccess = wrap(() => {\n          _onDatabaseDeleted(this._deps, this.name);\n          resolve();\n        });\n        req.onerror = eventRejectHandler(reject);\n        req.onblocked = this._fireOnBlocked;\n      }\n      // Prevent accidentially doing db.delete(1) when intention was to do db.[table].delete(1).\n      if (hasInvalidArguments) throw new exceptions.InvalidArgument(\"Invalid closeOptions argument to db.delete()\");\n      if (state.isBeingOpened) {\n        state.dbReadyPromise.then(doDelete);\n      } else {\n        doDelete();\n      }\n    });\n  }\n\n  backendDB() {\n    return this.idbdb;\n  }\n\n  isOpen() {\n    return this.idbdb !== null;\n  }\n\n  hasBeenClosed() {\n    const dbOpenError = this._state.dbOpenError;\n    return dbOpenError && (dbOpenError.name === 'DatabaseClosed');\n  }\n\n  hasFailed() {\n    return this._state.dbOpenError !== null;\n  }\n\n  dynamicallyOpened() {\n    return this._state.autoSchema;\n  }\n\n  get tables () {\n    return keys(this._allTables).map(name => this._allTables[name]);\n  }\n\n  transaction(): Promise {\n    const args = extractTransactionArgs.apply(this, arguments);\n    return this._transaction.apply(this, args);\n  }\n\n  _transaction(mode: TransactionMode, tables: Array<ITable | string>, scopeFunc: Function) {\n    let parentTransaction = PSD.trans as Transaction | undefined;\n    // Check if parent transactions is bound to this db instance, and if caller wants to reuse it\n    if (!parentTransaction || parentTransaction.db !== this || mode.indexOf('!') !== -1) parentTransaction = null;\n    const onlyIfCompatible = mode.indexOf('?') !== -1;\n    mode = mode.replace('!', '').replace('?', '') as TransactionMode; // Ok. Will change arguments[0] as well but we wont touch arguments henceforth.\n    let idbMode: IDBTransactionMode,\n        storeNames;\n\n    try {\n        //\n        // Get storeNames from arguments. Either through given table instances, or through given table names.\n        //\n        storeNames = tables.map(table => {\n            var storeName = table instanceof this.Table ? table.name : table;\n            if (typeof storeName !== 'string') throw new TypeError(\"Invalid table argument to Dexie.transaction(). Only Table or String are allowed\");\n            return storeName;\n        });\n\n        //\n        // Resolve mode. Allow shortcuts \"r\" and \"rw\".\n        //\n        if (mode == \"r\" || mode === READONLY)\n          idbMode = READONLY;\n        else if (mode == \"rw\" || mode == READWRITE)\n          idbMode = READWRITE;\n        else\n            throw new exceptions.InvalidArgument(\"Invalid transaction mode: \" + mode);\n\n        if (parentTransaction) {\n            // Basic checks\n            if (parentTransaction.mode === READONLY && idbMode === READWRITE) {\n                if (onlyIfCompatible) {\n                    // Spawn new transaction instead.\n                    parentTransaction = null; \n                }\n                else throw new exceptions.SubTransaction(\"Cannot enter a sub-transaction with READWRITE mode when parent transaction is READONLY\");\n            }\n            if (parentTransaction) {\n                storeNames.forEach(storeName => {\n                    if (parentTransaction && parentTransaction.storeNames.indexOf(storeName) === -1) {\n                        if (onlyIfCompatible) {\n                            // Spawn new transaction instead.\n                            parentTransaction = null; \n                        }\n                        else throw new exceptions.SubTransaction(\"Table \" + storeName +\n                            \" not included in parent transaction.\");\n                    }\n                });\n            }\n            if (onlyIfCompatible && parentTransaction && !parentTransaction.active) {\n                // '?' mode should not keep using an inactive transaction.\n                parentTransaction = null;\n            }\n        }\n    } catch (e) {\n        return parentTransaction ?\n            parentTransaction._promise(null, (_, reject) => {reject(e);}) :\n            rejection (e);\n    }\n    // If this is a sub-transaction, lock the parent and then launch the sub-transaction.\n    const enterTransaction = enterTransactionScope.bind(null, this, idbMode, storeNames, parentTransaction, scopeFunc);\n    return (parentTransaction ?\n        parentTransaction._promise(idbMode, enterTransaction, \"lock\") :\n        PSD.trans ?\n            // no parent transaction despite PSD.trans exists. Make sure also\n            // that the zone we create is not a sub-zone of current, because\n            // Promise.follow() should not wait for it if so.\n            usePSD(PSD.transless, ()=>this._whenReady(enterTransaction)) :\n            this._whenReady (enterTransaction));\n  }\n\n  table(tableName: string): Table;\n  table<T, TKey extends IndexableType=IndexableType>(tableName: string): ITable<T, TKey>;\n  table(tableName: string): Table {\n    if (!hasOwn(this._allTables, tableName)) {\n      throw new exceptions.InvalidTable(`Table ${tableName} does not exist`); }\n    return this._allTables[tableName];\n  }\n}\n", "import {\n  Observable as IObservable,\n  Observer,\n  Subscription,\n} from \"../../public/types/observable\";\n\nconst symbolObservable: typeof Symbol.observable =\n  typeof Symbol !== \"undefined\" && \"observable\" in Symbol\n    ? Symbol.observable\n    : \"@@observable\" as any;\n\nexport class Observable<T> implements IObservable<T> {\n  private _subscribe: (observer: Observer<T>) => Subscription;\n  hasValue?: ()=>boolean;\n  getValue?: ()=>T;\n\n  constructor(subscribe: (observer: Observer<T>) => Subscription) {\n    this._subscribe = subscribe;\n  }\n\n  subscribe(\n    onNext?: ((value: T) => void) | null,\n    onError?: ((error: any) => void) | null,\n    onComplete?: (() => void) | null\n  ): Subscription;\n  subscribe(observer?: Observer<T> | null): Subscription;\n  subscribe(x?: any, error?: any, complete?: any): Subscription {\n    return this._subscribe(\n      !x || typeof x === \"function\" ? { next: x, error, complete } : x\n    );\n  }\n\n  [symbolObservable]() {\n    return this;\n  }\n}\n", "import { _global } from '../../globals/global';\nimport { DexieDOMDependencies } from '../../public/types/dexie-dom-dependencies';\n\nexport let domDeps: DexieDOMDependencies\n\ntry {\n  domDeps = {\n    // Required:\n    indexedDB: _global.indexedDB || _global.mozIndexedDB || _global.webkitIndexedDB || _global.msIndexedDB,\n    IDBKeyRange: _global.IDBKeyRange || _global.webkitIDBKeyRange\n  };\n} catch (e) {\n  domDeps = { indexedDB: null, IDBKeyRange: null };\n}\n", "import { _global, isAsyncFunction, keys, objectIsEmpty } from '../functions/utils';\nimport {\n  globalEvents,\n  DEXIE_STORAGE_MUTATED_EVENT_NAME,\n} from '../globals/global-events';\nimport {\n  beginMicroTickScope,\n  decrementExpectedAwaits,\n  endMicroTickScope,\n  execInGlobalContext,\n  incrementExpectedAwaits,\n  NativePromise,\n  newScope,\n  PSD,\n  usePSD,\n} from '../helpers/promise';\nimport { ObservabilitySet } from '../public/types/db-events';\nimport {\n  Observable as IObservable,\n  Subscription,\n} from '../public/types/observable';\nimport { Observable } from '../classes/observable/observable';\nimport { extendObservabilitySet } from './extend-observability-set';\nimport { rangesOverlap } from '../helpers/rangeset';\nimport { domDeps } from '../classes/dexie/dexie-dom-dependencies';\nimport { Transaction } from '../classes/transaction';\nimport { obsSetsOverlap } from './obs-sets-overlap';\n\nexport interface LiveQueryContext {\n  subscr: ObservabilitySet;\n  signal: AbortSignal;\n  requery: () => void;\n  trans: null | Transaction;\n  querier: Function; // For debugging purposes and Error messages\n}\n\nexport function liveQuery<T>(querier: () => T | Promise<T>): IObservable<T> {\n  let hasValue = false;\n  let currentValue: T;\n  const observable = new Observable<T>((observer) => {\n    const scopeFuncIsAsync = isAsyncFunction(querier);\n    function execute(ctx: LiveQueryContext) {\n      const wasRootExec = beginMicroTickScope(); // Performance: Avoid starting a new microtick scope within the async context.\n      try {\n        if (scopeFuncIsAsync) {\n          incrementExpectedAwaits();\n        }\n        let rv = newScope(querier, ctx);\n        if (scopeFuncIsAsync) {\n          // Make sure to set rv = rv.finally in order to wait to after decrementExpectedAwaits() has been called.\n          // This fixes zone leaking issue that the liveQuery zone can leak to observer's next microtask.\n          rv = (rv as Promise<any>).finally(decrementExpectedAwaits);\n        }\n        return rv;\n      } finally {\n        wasRootExec && endMicroTickScope(); // Given that we created the microtick scope, we must also end it.\n      }\n    }\n\n    let closed = false;\n    let abortController: AbortController;\n\n    let accumMuts: ObservabilitySet = {};\n    let currentObs: ObservabilitySet = {};\n\n    const subscription: Subscription = {\n      get closed() {\n        return closed;\n      },\n      unsubscribe: () => {\n        if (closed) return;\n        closed = true;\n        if (abortController) abortController.abort();\n        if (startedListening) globalEvents.storagemutated.unsubscribe(mutationListener);\n      },\n    };\n\n    observer.start && observer.start(subscription); // https://github.com/tc39/proposal-observable\n\n    let startedListening = false;\n\n    const doQuery = () => execInGlobalContext(_doQuery);\n\n    function shouldNotify() {\n      return obsSetsOverlap(currentObs, accumMuts);\n    }\n\n    const mutationListener = (parts: ObservabilitySet) => {\n      extendObservabilitySet(accumMuts, parts);\n      if (shouldNotify()) {\n        doQuery();\n      }\n    };\n\n    const _doQuery = () => {\n      if (\n        closed || // closed - don't run!\n        !domDeps.indexedDB) // SSR in sveltekit, nextjs etc\n      {\n        return;\n      }\n      accumMuts = {};\n      const subscr: ObservabilitySet = {};\n      // Abort signal fill three purposes:\n      // 1. Abort the query if the observable is unsubscribed.\n      // 2. Abort the query if a new query is made before the previous one has completed.\n      // 3. For cached queries to know if they should remain in memory or could be enqued for being freed up.\n      //    (they will remain in memory for a short time and if noone needs them again, they will eventually be freed up)\n      if (abortController) abortController.abort(); // Cancel previous query. Last query will be cancelled on unsubscribe().\n      abortController = new AbortController();\n      \n      const ctx: LiveQueryContext = {\n        subscr,\n        signal: abortController.signal,\n        requery: doQuery,\n        querier,\n        trans: null // Make the scope transactionless (don't reuse transaction from outer scope of the caller of subscribe())\n      }\n      const ret = execute(ctx);\n      Promise.resolve(ret).then(\n        (result) => {\n          hasValue = true;\n          currentValue = result;\n          if (closed || ctx.signal.aborted) {\n            // closed - no subscriber anymore.\n            // signal.aborted - new query was made before this one completed and\n            // the querier might have catched AbortError and return successful result.\n            // If so, we should not rely in that result because we know we have aborted\n            // this run, which means there's another run going on that will handle accumMuts\n            // and we must not base currentObs on the half-baked subscr.\n            return;\n          }\n          accumMuts = {};\n          // Update what we are subscribing for based on this last run:\n          currentObs = subscr;\n          if (!objectIsEmpty(currentObs) && !startedListening) {\n            globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, mutationListener);\n            startedListening = true;\n          }\n          execInGlobalContext(()=>!closed && observer.next && observer.next(result));\n        },\n        (err) => {\n          hasValue = false;\n          if (!['DatabaseClosedError', 'AbortError'].includes(err?.name)) {\n            if (!closed) execInGlobalContext(()=>{\n              if (closed) return;\n              observer.error && observer.error(err);\n            });\n          }\n        }\n      );\n    };\n\n    // Use setTimeot here to guarantee execution in a private macro task before and\n    // after. The helper executeInGlobalContext(_doQuery) is not enough here because\n    // caller of `subscribe()` could be anything, such as a frontend framework that will\n    // continue in the same tick after subscribe() is called and call other\n    // eftects, that could involve dexie operations such as writing to the DB.\n    // If that happens, the private zone echoes from a live query tast started here\n    // could still be ongoing when the other operations start and make them inherit\n    // the async context from a live query.\n    setTimeout(doQuery, 0);\n    return subscription;\n  });\n  observable.hasValue = () => hasValue;\n  observable.getValue = () => currentValue;\n  return observable;\n}\n", "import { <PERSON><PERSON> as _<PERSON>ie } from './dexie';\nimport { _global } from '../../globals/global';\nimport { props, derive, extend, override, getBy<PERSON>eyPath, setByKeyPath, delByKeyPath, shallowClone, deepClone, asap } from '../../functions/utils';\nimport { getObjectDiff } from \"../../functions/get-object-diff\";\nimport { fullNameExceptions } from '../../errors';\nimport { DexieConstructor } from '../../public/types/dexie-constructor';\nimport { getDatabaseNames } from '../../helpers/database-enumerator';\nimport { PSD } from '../../helpers/promise';\nimport { usePSD } from '../../helpers/promise';\nimport { newScope } from '../../helpers/promise';\nimport { rejection } from '../../helpers/promise';\nimport { awaitIterator } from '../../helpers/yield-support';\nimport Promise from '../../helpers/promise';\nimport * as Debug from '../../helpers/debug';\nimport { dexieStackFrameFilter, minKey, connections, DEXIE_VERSION } from '../../globals/constants';\nimport Events from '../../helpers/Events';\nimport { exceptions } from '../../errors';\nimport { errnames } from '../../errors';\nimport { getMaxKey } from '../../functions/quirks';\nimport { vip } from './vip';\nimport { globalEvents } from '../../globals/global-events';\nimport { liveQuery } from '../../live-query/live-query';\nimport { extendObservabilitySet } from '../../live-query/extend-observability-set';\nimport { domDeps } from './dexie-dom-dependencies';\nimport { cmp } from '../../functions/cmp';\nimport { cache } from '../../live-query/cache/cache';\n\n/* (Dexie) is an instance of DexieConstructor, as defined in public/types/dexie-constructor.d.ts\n*  (new Dexie()) is an instance of Dexie, as defined in public/types/dexie.d.ts\n* \n* Why we're doing this?\n\n* Because we've choosen to define the public Dexie API using a DexieConstructor interface\n* rather than declaring a class. On that interface, all static props are defined.\n* In practice, class Dexie's constructor implements DexieConstructor and all member props\n* are defined in interface Dexie. We could say, it's a typescript limitation of not being\n* able to define a static interface that forces us to do the cast below.\n*/\nconst Dexie = _Dexie as any as DexieConstructor;\n\n//\n// Set all static methods and properties onto Dexie:\n// \nprops(Dexie, {\n\n  // Dexie.BulkError = class BulkError {...};\n  // Dexie.XXXError = class XXXError {...};\n  ...fullNameExceptions,\n\n  //\n  // Static delete() method.\n  //\n  delete(databaseName: string) {\n    const db = new Dexie(databaseName, {addons: []});\n    return db.delete();\n  },\n\n  //\n  // Static exists() method.\n  //\n  exists(name: string) {\n    return new Dexie(name, { addons: [] }).open().then(db => {\n      db.close();\n      return true;\n    }).catch('NoSuchDatabaseError', () => false);\n  },\n\n  //\n  // Static method for retrieving a list of all existing databases at current host.\n  //\n  getDatabaseNames(cb) {\n    try {\n      return getDatabaseNames(Dexie.dependencies).then(cb);\n    } catch {\n      return rejection(new exceptions.MissingAPI());\n    }\n  },\n\n  /** @deprecated */\n  defineClass() {\n    function Class(content) {\n      extend(this, content);\n    }\n    return Class;\n  },\n\n  ignoreTransaction(scopeFunc) {\n    // In case caller is within a transaction but needs to create a separate transaction.\n    // Example of usage:\n    //\n    // Let's say we have a logger function in our app. Other application-logic should be unaware of the\n    // logger function and not need to include the 'logentries' table in all transaction it performs.\n    // The logging should always be done in a separate transaction and not be dependant on the current\n    // running transaction context. Then you could use Dexie.ignoreTransaction() to run code that starts a new transaction.\n    //\n    //     Dexie.ignoreTransaction(function() {\n    //         db.logentries.add(newLogEntry);\n    //     });\n    //\n    // Unless using Dexie.ignoreTransaction(), the above example would try to reuse the current transaction\n    // in current Promise-scope.\n    //\n    // An alternative to Dexie.ignoreTransaction() would be setImmediate() or setTimeout(). The reason we still provide an\n    // API for this because\n    //  1) The intention of writing the statement could be unclear if using setImmediate() or setTimeout().\n    //  2) setTimeout() would wait unnescessary until firing. This is however not the case with setImmediate().\n    //  3) setImmediate() is not supported in the ES standard.\n    //  4) You might want to keep other PSD state that was set in a parent PSD, such as PSD.letThrough.\n    return PSD.trans ?\n      usePSD(PSD.transless, scopeFunc) : // Use the closest parent that was non-transactional.\n      scopeFunc(); // No need to change scope because there is no ongoing transaction.\n  },\n\n  vip,\n\n  async: function (generatorFn: Function) {\n    return function () {\n      try {\n        var rv = awaitIterator(generatorFn.apply(this, arguments));\n        if (!rv || typeof rv.then !== 'function')\n          return Promise.resolve(rv);\n        return rv;\n      } catch (e) {\n        return rejection(e);\n      }\n    };\n  },\n\n  spawn: function (generatorFn, args, thiz) {\n    try {\n      var rv = awaitIterator(generatorFn.apply(thiz, args || []));\n      if (!rv || typeof rv.then !== 'function')\n        return Promise.resolve(rv);\n      return rv;\n    } catch (e) {\n      return rejection(e);\n    }\n  },\n\n  // Dexie.currentTransaction property\n  currentTransaction: {\n    get: () => PSD.trans || null\n  },\n\n  waitFor: function (promiseOrFunction, optionalTimeout) {\n    // If a function is provided, invoke it and pass the returning value to Transaction.waitFor()\n    const promise = Promise.resolve(\n      typeof promiseOrFunction === 'function' ?\n        Dexie.ignoreTransaction(promiseOrFunction) :\n        promiseOrFunction)\n      .timeout(optionalTimeout || 60000); // Default the timeout to one minute. Caller may specify Infinity if required.       \n\n    // Run given promise on current transaction. If no current transaction, just return a Dexie promise based\n    // on given value.\n    return PSD.trans ?\n      PSD.trans.waitFor(promise) :\n      promise;\n  },\n\n  // Export our Promise implementation since it can be handy as a standalone Promise implementation\n  Promise: Promise,\n\n  // Dexie.debug proptery:\n  // Dexie.debug = false\n  // Dexie.debug = true\n  // Dexie.debug = \"dexie\" - don't hide dexie's stack frames.\n  debug: {\n    get: () => Debug.debug,\n    set: value => {\n      Debug.setDebug(value, value === 'dexie' ? () => true : dexieStackFrameFilter);\n    }\n  },\n\n  // Export our derive/extend/override methodology\n  derive: derive, // Deprecate?\n  extend: extend, // Deprecate?\n  props: props,\n  override: override, // Deprecate?\n  // Export our Events() function - can be handy as a toolkit\n  Events: Events,\n  on: globalEvents,\n  liveQuery,\n  extendObservabilitySet,\n  // Utilities\n  getByKeyPath: getByKeyPath,\n  setByKeyPath: setByKeyPath,\n  delByKeyPath: delByKeyPath,\n  shallowClone: shallowClone,\n  deepClone: deepClone,\n  getObjectDiff: getObjectDiff,\n  cmp,\n  asap: asap,\n  //maxKey: new Dexie('',{addons:[]})._maxKey,\n  minKey: minKey,\n  // Addon registry\n  addons: [],\n  // Global DB connection list\n  connections: connections,\n\n  //MultiModifyError: exceptions.Modify, // Obsolete!\n  errnames: errnames,\n\n  // Export other static classes\n  //IndexSpec: IndexSpec, Obsolete!\n  //TableSchema: TableSchema, Obsolete!\n\n  //\n  // Dependencies\n  //\n  // These will automatically work in browsers with indexedDB support, or where an indexedDB polyfill has been included.\n  //\n  // In node.js, however, these properties must be set \"manually\" before instansiating a new Dexie().\n  // For node.js, you need to require indexeddb-js or similar and then set these deps.\n  //\n  dependencies: domDeps,\n  cache,\n\n  // API Version Number: Type Number, make sure to always set a version number that can be comparable correctly. Example: 0.9, 0.91, 0.92, 1.0, 1.01, 1.1, 1.2, 1.21, etc.\n  semVer: DEXIE_VERSION,\n  version: DEXIE_VERSION.split('.')\n    .map(n => parseInt(n))\n    .reduce((p, c, i) => p + (c / Math.pow(10, i * 2))),\n\n  // https://github.com/dfahlander/Dexie.js/issues/186\n  // typescript compiler tsc in mode ts-->es5 & commonJS, will expect require() to return\n  // x.default. Workaround: Set Dexie.default = Dexie.\n  // default: Dexie, // Commented because solved in index-umd.ts instead.\n  // Make it possible to import {Dexie} (non-default import)\n  // Reason 1: May switch to that in future.\n  // Reason 2: We declare it both default and named exported in d.ts to make it possible\n  // to let addons extend the Dexie interface with Typescript 2.1 (works only when explicitely\n  // exporting the symbol, not just default exporting)\n  // Dexie: Dexie // Commented because solved in index-umd.ts instead.\n});\n\nDexie.maxKey = getMaxKey(Dexie.dependencies.IDBKeyRange);\n", "import { globalEvents, DEXIE_STORAGE_MUTATED_EVENT_NAME, STORAGE_MUTATED_DOM_EVENT_NAME } from '../globals/global-events';\nimport { ObservabilitySet } from \"../public/types/db-events\";\nimport { signalSubscribersNow } from './cache/signalSubscribers';\n\nif (typeof dispatchEvent !== 'undefined' && typeof addEventListener !== 'undefined') {\n  globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, updatedParts => {\n    if (!propagatingLocally) {\n      let event: CustomEvent<ObservabilitySet>;\n      event = new CustomEvent(STORAGE_MUTATED_DOM_EVENT_NAME, {\n        detail: updatedParts\n      });\n      propagatingLocally = true;\n      dispatchEvent(event);\n      propagatingLocally = false;\n    }\n  });\n  addEventListener(STORAGE_MUTATED_DOM_EVENT_NAME, ({detail}: CustomEvent<ObservabilitySet>) => {\n    if (!propagatingLocally) {\n      propagateLocally(detail);\n    }\n  });\n}\n\n/** Called from listeners to BroadcastChannel and DOM event to\n * propagate the event locally into dexie's storagemutated event\n * and invalidate cached queries.\n * \n * This function is only called when the event is not originating\n * from this same Dexie module - either from another redundant dexie import\n * or from a foreign tab or worker. That's why we need to invalidate\n * the cache when this happens.\n */\nexport function propagateLocally(updateParts: ObservabilitySet) {\n  let wasMe = propagatingLocally;\n  try {\n    propagatingLocally = true;\n    // Fire the \"storagemutated\" event.\n    globalEvents.storagemutated.fire(updateParts);\n    // Invalidate cached queries and signal subscribers to requery.\n    signalSubscribersNow(updateParts, true);\n  } finally {\n    propagatingLocally = wasMe;\n  }\n}\n\nexport let propagatingLocally = false;\n", "import {\n  globalEvents,\n  STORAGE_MUTATED_DOM_EVENT_NAME,\n  DEXIE_STORAGE_MUTATED_EVENT_NAME,\n} from '../globals/global-events';\nimport { propagateLocally, propagatingLocally } from './propagate-locally';\n\nexport let bc: BroadcastChannel;\n\nexport let createBC = ()=>{};\n\nif (typeof BroadcastChannel !== 'undefined') {\n  createBC = () => {\n    bc = new BroadcastChannel(STORAGE_MUTATED_DOM_EVENT_NAME);\n    bc.onmessage = ev => ev.data && propagateLocally(ev.data);\n  }\n  createBC();\n\n  /**\n   * The Node.js BroadcastChannel will prevent the node process from exiting\n   * if the BroadcastChannel is not closed.\n   * Therefore we have to call unref() which allows the process to finish\n   * properly even when the BroadcastChannel is never closed.\n   * @link https://nodejs.org/api/worker_threads.html#broadcastchannelunref\n   * @link https://github.com/dexie/Dexie.js/pull/1576\n   */\n  if (typeof (bc as any).unref === 'function') {\n    (bc as any).unref();\n  }\n  \n  //\n  // Propagate local changes to remote tabs, windows and workers via BroadcastChannel\n  //\n  globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, (changedParts) => {\n    if (!propagatingLocally) {\n      bc.postMessage(changedParts);\n    }\n  });\n}\n", "import { <PERSON><PERSON> } from \"./classes/dexie\";\nimport { connections } from \"./globals/constants\";\nimport { debug } from \"./helpers/debug\";\nimport { RangeSet } from \"./helpers/rangeset\";\nimport { bc, createBC } from \"./live-query/enable-broadcast\";\nimport { propagateLocally } from \"./live-query/propagate-locally\";\n\n\nif (typeof addEventListener !== 'undefined') {\n  addEventListener('pagehide', (event) => {\n    if (!Dexie.disableBfCache && event.persisted) {\n      if (debug) console.debug('Dexie: handling persisted pagehide');\n      bc?.close();\n      for (const db of connections) {\n        db.close({disableAutoOpen: false});\n      }\n    }\n  });\n  addEventListener('pageshow', (event) => {\n    if (!Dexie.disableBfCache && event.persisted) {\n      if (debug) console.debug('Dexie: handling persisted pageshow');\n      createBC();\n      propagateLocally({all: new RangeSet(-Infinity, [[]])}); // Trigger all queries to requery\n    }\n  });\n}\n", "import { PropModification } from \"../../helpers/prop-modification\";\n\nexport function add(value: number | bigint | Array<string | number>) {\n  return new PropModification({add: value});\n}\n", "import { PropModification } from \"../../helpers/prop-modification\";\n\nexport function remove(value: number | bigint | Array<string | number>) {\n  return new PropModification({remove: value});\n}\n", "import { PropModification } from \"../../helpers/prop-modification\";\n\nexport function replacePrefix(a: string, b:string) {\n  return new PropModification({replacePrefix: [a, b]});\n}\n", "import { <PERSON><PERSON> } from './classes/dexie';\nimport { DexieConstructor } from './public/types/dexie-constructor';\nimport { DexiePromise } from './helpers/promise';\nimport { mapError } from './errors';\nimport * as Debug from './helpers/debug';\nimport { dexieStackFrameFilter } from './globals/constants';\n\n// Generate all static properties such as Dexie.maxKey etc\n// (implement interface DexieConstructor):\nimport './classes/dexie/dexie-static-props';\nimport './live-query/enable-broadcast';\nimport './support-bfcache';\nimport { liveQuery } from './live-query/live-query';\nimport { Entity } from './classes/entity/Entity';\nimport { cmp } from './functions/cmp';\nimport { PropModification } from './helpers/prop-modification';\nimport { replacePrefix, add, remove } from './functions/propmods';\n\n\n// Set rejectionMapper of DexiePromise so that it generally tries to map\n// DOMErrors and DOMExceptions to a DexieError instance with same name but with\n// async stack support and with a prototypal inheritance from DexieError and Error.\n// of Map DOMErrors and DOMExceptions to corresponding Dexie errors.\nDexiePromise.rejectionMapper = mapError;\n\n// Let the async stack filter focus on app code and filter away frames from dexie.min.js:\nDebug.setDebug(Debug.debug, dexieStackFrameFilter);\n\nexport { RangeSet, mergeRanges, rangesOverlap } from \"./helpers/rangeset\";\nexport { Dexie, liveQuery }; // Comply with public/index.d.ts.\nexport { Entity };\nexport { cmp };\nexport { PropModification, replacePrefix, add, remove };\nexport default Dexie;\n", "// Issue #1127. Need another index.ts for the UMD module with only a default export\n// like it was before.\n// In practice though, the UMD export will also export the named export in \n// https://github.com/dfahlander/Dexie.js/blob/c9187ae60c0d7a424f85bab3af179fbbc9901c8e/src/classes/dexie/dexie-static-props.ts#L223-L228\nimport Dexie from \"./index\";\nimport * as namedExports from \"./index\";\nimport { __assign } from 'tslib';\n__assign(Dexie, namedExports, {default: Dexie});\nexport default Dexie;\n", "// Making the module version consumable via require - to prohibit\n// multiple occurrancies of the same module in the same app\n// (dual package hazard, https://nodejs.org/api/packages.html#dual-package-hazard)\nimport _<PERSON>ie from \"./dist/dexie.js\";\nconst DexieSymbol = Symbol.for(\"Dexie\");\nconst Dexie = globalThis[DexieSymbol] || (globalThis[DexieSymbol] = _Dexie);\nif (_Dexie.semVer !== Dexie.semVer) {\n    throw new Error(`Two different versions of Dexie loaded in the same app: ${_Dexie.semVer} and ${Dexie.semVer}`);\n}\nconst { liveQuery, mergeRanges, rangesOverlap, RangeSet, cmp, Entity,\n    PropModification, replacePrefix, add, remove } = Dexie;\nexport { liveQuery, mergeRanges, rangesOverlap, RangeSet, cmp, Dexie, Entity,\n    PropModification, replacePrefix, add, remove };\nexport default Dexie;\n"], "mappings": ";;;;;;;;;;;;AAgBA,UAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,wBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAA,EAAE,aAAc,SAAS,SAAUA,IAAGC,IAAG;AAAE,UAAAD,GAAE,YAAYC;QAAE,KACzE,SAAUD,IAAGC,IAAG;AAAE,mBAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;QAAE;AACnG,eAAO,cAAc,GAAG,CAAC;MAC7B;AAEO,eAAS,UAAU,GAAG,GAAG;AAC5B,YAAI,OAAO,MAAM,cAAc,MAAM;AACjC,gBAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,sBAAc,GAAG,CAAC;AAClB,iBAAS,KAAK;AAAE,eAAK,cAAc;QAAE;AACrC,UAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAE;MACrF;AAEO,UAAI,WAAW,WAAW;AAC7B,mBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,mBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,gBAAI,UAAU,CAAC;AACf,qBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;UACvF;AACQ,iBAAO;QACf;AACI,eAAO,SAAS,MAAM,MAAM,SAAS;MACzC;AA4HO,eAAS,cAAc,IAAI,MAAM,MAAM;AAC1C,YAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,cAAI,MAAM,EAAE,KAAK,OAAO;AACpB,gBAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,eAAG,CAAC,IAAI,KAAK,CAAC;UAC1B;QACA;AACI,eAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;MAC3D;AC3KO,UAAM,UACT,OAAO,eAAe,cAAc,aACpC,OAAO,SAAS,cAAc,OAC9B,OAAO,WAAW,cAAc,SAChC;ACJG,UAAM,OAAO,OAAO;AACpB,UAAM,UAAU,MAAM;AAC7B,UAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,SAAQ;AAInD,gBAAQ,UAAU;MACtB;eAGgB,OAA0C,KAAQ,WAAY;AAC1E,YAAI,OAAO,cAAc;AAAU,iBAAO;AAC1C,aAAK,SAAS,EAAE,QAAQ,SAAU,KAAG;AACjC,cAAI,GAAG,IAAI,UAAU,GAAG;SAC3B;AACD,eAAO;MACX;AAEO,UAAM,WAAW,OAAO;AACxB,UAAM,UAAU,CAAA,EAAG;eACV,OAAO,KAAK,MAAI;AAC5B,eAAO,QAAQ,KAAK,KAAK,IAAI;MACjC;eAEgB,MAAO,OAAO,WAAS;AACnC,YAAI,OAAO,cAAc;AAAY,sBAAY,UAAU,SAAS,KAAK,CAAC;AAC1E,SAAC,OAAO,YAAY,cAAc,OAAO,QAAQ,SAAS,SAAS,EAAE,QAAQ,SAAA,KAAG;AAC5E,kBAAQ,OAAO,KAAK,UAAU,GAAG,CAAC;SACrC;MACL;AAEO,UAAM,iBAAiB,OAAO;eAErB,QAAQ,KAAK,MAAM,kBAAkB,SAAQ;AACzD,uBAAe,KAAK,MAAM,OAAO,oBAAoB,OAAO,kBAAkB,KAAK,KAAK,OAAO,iBAAiB,QAAQ,aACpH,EAAC,KAAK,iBAAiB,KAAK,KAAK,iBAAiB,KAAK,cAAc,KAAI,IACzE,EAAC,OAAO,kBAAkB,cAAc,MAAM,UAAU,KAAI,GAAG,OAAO,CAAC;MAC/E;eAEgB,OAAO,OAAK;AACxB,eAAO;UACH,MAAM,SAAU,QAAM;AAClB,kBAAM,YAAY,OAAO,OAAO,OAAO,SAAS;AAChD,oBAAQ,MAAM,WAAW,eAAe,KAAK;AAC7C,mBAAO;cACH,QAAQ,MAAM,KAAK,MAAM,MAAM,SAAS;;;;MAIxD;AAEO,UAAM,2BAA2B,OAAO;eAE/B,sBAAsB,KAAK,MAAI;AAC3C,YAAM,KAAK,yBAAyB,KAAK,IAAI;AAC7C,YAAI;AACJ,eAAO,OAAO,QAAQ,SAAS,GAAG,MAAM,sBAAuB,OAAO,IAAI;MAC9E;AAEA,UAAM,SAAS,CAAA,EAAG;eACF,MAAM,MAAM,OAAQ,KAAI;AACpC,eAAO,OAAO,KAAK,MAAM,OAAO,GAAG;MACvC;eAEgB,SAAS,UAAU,kBAAgB;AAC/C,eAAO,iBAAiB,QAAQ;MACpC;eAEgB,OAAQ,GAAC;AACrB,YAAI,CAAC;AAAG,gBAAM,IAAI,MAAM,kBAAkB;MAC9C;eAEgBC,OAAK,IAAE;AAEnB,YAAI,QAAQ;AAAc,uBAAa,EAAE;;AAAQ,qBAAW,IAAI,CAAC;MACrE;eAWgB,cAAoB,OAAY,WAA0C;AACtF,eAAO,MAAM,OAAO,SAAC,QAAQ,MAAM,GAAC;AAChC,cAAI,eAAe,UAAU,MAAM,CAAC;AACpC,cAAI;AAAc,mBAAO,aAAa,CAAC,CAAC,IAAI,aAAa,CAAC;AAC1D,iBAAO;WACR,CAAA,CAAE;MACT;eAoBgB,aAAa,KAAK,SAAO;AAErC,YAAI,OAAO,YAAY,YAAY,OAAO,KAAK,OAAO;AAAG,iBAAO,IAAI,OAAO;AAC3E,YAAI,CAAC;AAAS,iBAAO;AACrB,YAAI,OAAO,YAAY,UAAU;AAC7B,cAAI,KAAK,CAAA;AACT,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5C,gBAAI,MAAM,aAAa,KAAK,QAAQ,CAAC,CAAC;AACtC,eAAG,KAAK,GAAG;;AAEf,iBAAO;;AAEX,YAAI,SAAS,QAAQ,QAAQ,GAAG;AAChC,YAAI,WAAW,IAAI;AACf,cAAI,WAAW,IAAI,QAAQ,OAAO,GAAG,MAAM,CAAC;AAC5C,iBAAO,YAAY,OAAO,SAAY,aAAa,UAAU,QAAQ,OAAO,SAAS,CAAC,CAAC;;AAE3F,eAAO;MACX;eAEgB,aAAa,KAAK,SAAS,OAAK;AAC5C,YAAI,CAAC,OAAO,YAAY;AAAW;AACnC,YAAI,cAAc,UAAU,OAAO,SAAS,GAAG;AAAG;AAClD,YAAI,OAAO,YAAY,YAAY,YAAY,SAAS;AACpD,iBAAO,OAAO,UAAU,YAAY,YAAY,KAAK;AACrD,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5C,yBAAa,KAAK,QAAQ,CAAC,GAAG,MAAM,CAAC,CAAC;;eAEvC;AACH,cAAI,SAAS,QAAQ,QAAQ,GAAG;AAChC,cAAI,WAAW,IAAI;AACf,gBAAI,iBAAiB,QAAQ,OAAO,GAAG,MAAM;AAC7C,gBAAI,mBAAmB,QAAQ,OAAO,SAAS,CAAC;AAChD,gBAAI,qBAAqB;AACrB,kBAAI,UAAU,QAAW;AACrB,oBAAI,QAAQ,GAAG,KAAK,CAAC,MAAM,SAAS,cAAc,CAAC;AAAG,sBAAI,OAAO,gBAAgB,CAAC;;AAC7E,yBAAO,IAAI,cAAc;;AAC3B,oBAAI,cAAc,IAAI;iBAC5B;AACD,kBAAI,WAAW,IAAI,cAAc;AACjC,kBAAI,CAAC,YAAY,CAAC,OAAO,KAAK,cAAc;AAAG,2BAAY,IAAI,cAAc,IAAI,CAAA;AACjF,2BAAa,UAAU,kBAAkB,KAAK;;iBAE/C;AACH,gBAAI,UAAU,QAAW;AACrB,kBAAI,QAAQ,GAAG,KAAK,CAAC,MAAM,SAAS,OAAO,CAAC;AAAG,oBAAI,OAAO,SAAS,CAAC;;AAC/D,uBAAO,IAAI,OAAO;;AACpB,kBAAI,OAAO,IAAI;;;MAGlC;eAEgB,aAAa,KAAK,SAAO;AACrC,YAAI,OAAO,YAAY;AACnB,uBAAa,KAAK,SAAS,MAAS;iBAC/B,YAAY;AACjB,WAAA,EAAG,IAAI,KAAK,SAAS,SAAS,IAAE;AAC5B,yBAAa,KAAK,IAAI,MAAS;WAClC;MACT;eAEgB,aAAa,KAAG;AAC5B,YAAI,KAAK,CAAA;AACT,iBAAS,KAAK,KAAK;AACf,cAAI,OAAO,KAAK,CAAC;AAAG,eAAG,CAAC,IAAI,IAAI,CAAC;;AAErC,eAAO;MACX;AAEA,UAAM,SAAS,CAAA,EAAG;eACF,QAAY,GAAc;AACtC,eAAO,OAAO,MAAM,CAAA,GAAI,CAAC;MAC7B;AAGA,UAAM,qBACF,iNACC,MAAM,GAAG,EAAE,OACR,QAAQ,CAAC,GAAE,IAAG,IAAG,EAAE,EAAE,IAAI,SAAA,KAAG;AAAE,eAAA,CAAC,OAAM,QAAO,OAAO,EAAE,IAAI,SAAA,GAAC;AAAE,iBAAA,IAAE,MAAI;QAAO,CAAA;MAAC,CAAA,CAAC,CAAC,EAC9E,OAAO,SAAA,GAAC;AAAE,eAAA,QAAQ,CAAC;MAAC,CAAA;AAC1B,UAAM,iBAAiB,IAAI,IAAI,mBAAmB,IAAI,SAAA,GAAC;AAAE,eAAA,QAAQ,CAAC;MAAC,CAAA,CAAC;eAgBpD,sBAAwC,GAAI;AACxD,YAAM,KAAK,CAAA;AACX,iBAAW,KAAK;AAAG,cAAI,OAAO,GAAG,CAAC,GAAG;AACjC,gBAAM,IAAI,EAAE,CAAC;AACb,eAAG,CAAC,IAAI,CAAC,KAAK,OAAO,MAAM,YAAY,eAAe,IAAI,EAAE,WAAW,IAAI,IAAI,sBAAsB,CAAC;;AAE1G,eAAO;MACX;eAEgB,cAAc,GAAS;AACnC,iBAAW,KAAK;AAAG,cAAI,OAAO,GAAG,CAAC;AAAG,mBAAO;AAC5C,eAAO;MACX;AAEA,UAAI,eAAwC;eAQ5B,UAAa,KAAM;AAC/B,uBAAe,oBAAI,QAAO;AAC1B,YAAM,KAAK,eAAe,GAAG;AAC7B,uBAAe;AACf,eAAO;MACX;AAEA,eAAS,eAAkB,GAAI;AAC3B,YAAI,CAAC,KAAK,OAAO,MAAM;AAAU,iBAAO;AACxC,YAAI,KAAK,aAAa,IAAI,CAAC;AAC3B,YAAI;AAAI,iBAAO;AACf,YAAI,QAAQ,CAAC,GAAG;AACZ,eAAK,CAAA;AACL,uBAAa,IAAI,GAAG,EAAE;AACtB,mBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE,GAAG;AACtC,eAAG,KAAK,eAAe,EAAE,CAAC,CAAC,CAAC;;mBAEzB,eAAe,IAAI,EAAE,WAAW,GAAG;AAI1C,eAAK;eACF;AAGH,cAAM,QAAQ,SAAS,CAAC;AACxB,eAAK,UAAU,OAAO,YAAY,CAAA,IAAK,OAAO,OAAO,KAAK;AAC1D,uBAAa,IAAI,GAAG,EAAE;AACtB,mBAAS,QAAQ,GAAG;AAChB,gBAAI,OAAO,GAAG,IAAI,GAAG;AACjB,iBAAG,IAAI,IAAI,eAAe,EAAE,IAAI,CAAC;;;;AAI7C,eAAO;MACX;AAEO,UAAA,WAAY,CAAA,EAAE;eACL,YAAY,GAAS;AACjC,eAAO,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;MACvC;AAGO,UAAM,iBAAiB,OAAO,WAAW,cAC5C,OAAO,WACP;AACG,UAAM,gBAAgB,OAAO,mBAAmB,WAAW,SAAS,GAAC;AACxE,YAAI;AACJ,eAAO,KAAK,SAAS,IAAI,EAAE,cAAc,MAAM,EAAE,MAAM,CAAC;MAC5D,IAAI,WAAA;AAAc,eAAO;MAAK;eAKd,aAAa,GAAU,GAAM;AACzC,YAAM,IAAI,EAAE,QAAQ,CAAC;AACrB,YAAI,KAAK;AAAG,YAAE,OAAO,GAAG,CAAC;AACzB,eAAO,KAAK;MAChB;AAEO,UAAM,gBAAgB,CAAA;eASb,WAAY,WAAS;AACjC,YAAI,GAAG,GAAG,GAAG;AACb,YAAI,UAAU,WAAW,GAAG;AACxB,cAAI,QAAQ,SAAS;AAAG,mBAAO,UAAU,MAAK;AAC9C,cAAI,SAAS,iBAAiB,OAAO,cAAc;AAAU,mBAAO,CAAC,SAAS;AAC9E,cAAK,KAAK,cAAc,SAAS,GAAI;AACjC,gBAAI,CAAA;AACJ,mBAAQ,IAAI,GAAG,KAAI,GAAK,CAAC,EAAE;AAAM,gBAAE,KAAK,EAAE,KAAK;AAC/C,mBAAO;;AAEX,cAAI,aAAa;AAAM,mBAAO,CAAC,SAAS;AACxC,cAAI,UAAU;AACd,cAAI,OAAO,MAAM,UAAU;AACvB,gBAAI,IAAI,MAAM,CAAC;AACf,mBAAO;AAAK,gBAAE,CAAC,IAAI,UAAU,CAAC;AAC9B,mBAAO;;AAEX,iBAAO,CAAC,SAAS;;AAErB,YAAI,UAAU;AACd,YAAI,IAAI,MAAM,CAAC;AACf,eAAO;AAAK,YAAE,CAAC,IAAI,UAAU,CAAC;AAC9B,eAAO;MACX;AACO,UAAM,kBAAkB,OAAO,WAAW,cAC3C,SAAC,IAAY;AAAK,eAAA,GAAG,OAAO,WAAW,MAAM;MAAe,IAC5D,WAAA;AAAI,eAAA;MAAK;ACpUf,UAAI,kBAAkB;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;AAGJ,UAAI,mBAAmB;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;AAGJ,UAAI,YAAY,gBAAgB,OAAO,gBAAgB;AAEvD,UAAI,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,qBAAqB;QACrB,YAAY;;eAMA,WAAY,MAAM,KAAG;AAMjC,aAAK,OAAO;AACZ,aAAK,UAAU;MACnB;AAEA,aAAO,UAAU,EAAE,KAAK,KAAK,EAAE,OAAO;QAClC,UAAU,WAAA;AAAY,iBAAO,KAAK,OAAO,OAAO,KAAK;QAAQ;OAChE;AAED,eAAS,qBAAsB,KAAK,UAAQ;AACxC,eAAO,MAAM,eAAe,OAAO,KAAK,QAAQ,EAC3C,IAAI,SAAA,KAAG;AAAE,iBAAA,SAAS,GAAG,EAAE,SAAQ;QAAE,CAAA,EACjC,OAAO,SAAC,GAAE,GAAE,GAAC;AAAG,iBAAA,EAAE,QAAQ,CAAC,MAAM;QAAC,CAAA,EAClC,KAAK,IAAI;MAClB;eAMgB,YAAa,KAAK,UAAU,cAAc,YAAU;AAChE,aAAK,WAAW;AAChB,aAAK,aAAa;AAClB,aAAK,eAAe;AACpB,aAAK,UAAU,qBAAqB,KAAK,QAAQ;MACrD;AACA,aAAO,WAAW,EAAE,KAAK,UAAU;eAEnB,UAAW,KAAK,UAAQ;AACpC,aAAK,OAAO;AACZ,aAAK,WAAW,OAAO,KAAK,QAAQ,EAAE,IAAI,SAAA,KAAG;AAAI,iBAAA,SAAS,GAAG;QAAC,CAAA;AAC9D,aAAK,gBAAgB;AACrB,aAAK,UAAU,qBAAqB,KAAK,KAAK,QAAQ;MAC1D;AACA,aAAO,SAAS,EAAE,KAAK,UAAU;AAU1B,UAAI,WAAW,UAAU,OAAO,SAAC,KAAI,MAAI;AAAG,eAAC,IAAI,IAAI,IAAE,OAAK,SAAQ;MAAG,GAAE,CAAA,CAAE;AAGlF,UAAM,gBAAgB;AAEf,UAAI,aAAa,UAAU,OAAO,SAAC,KAAI,MAAI;AAO9C,YAAI,WAAW,OAAO;AACtB,iBAASC,YAAY,YAAY,OAAK;AAClC,eAAK,OAAO;AACZ,cAAI,CAAC,YAAY;AACb,iBAAK,UAAU,aAAa,IAAI,KAAK;AACrC,iBAAK,QAAQ;qBACN,OAAO,eAAe,UAAU;AACvC,iBAAK,UAAU,GAAA,OAAG,UAAU,EAAA,OAAG,CAAC,QAAQ,KAAK,QAAQ,KAAK;AAC1D,iBAAK,QAAQ,SAAS;qBACf,OAAO,eAAe,UAAU;AACvC,iBAAK,UAAU,GAAA,OAAG,WAAW,MAAI,GAAA,EAAA,OAAI,WAAW,OAAO;AACvD,iBAAK,QAAQ;;;AAGrB,eAAOA,WAAU,EAAE,KAAK,aAAa;AACrC,YAAI,IAAI,IAAEA;AACV,eAAO;MACX,GAAE,CAAA,CAAE;AAGJ,iBAAW,SAAS;AACpB,iBAAW,OAAO;AAClB,iBAAW,QAAQ;AAEZ,UAAI,eAAe,iBAAiB,OAAO,SAAC,KAAK,MAAI;AACxD,YAAI,OAAO,OAAO,IAAI,WAAW,IAAI;AACrC,eAAO;MACX,GAAG,CAAA,CAAE;eAEW,SAAU,UAAU,SAAO;AACvC,YAAI,CAAC,YAAY,oBAAoB,cAAc,oBAAoB,aAAa,oBAAoB,eAAe,CAAC,SAAS,QAAQ,CAAC,aAAa,SAAS,IAAI;AAChK,iBAAO;AACX,YAAI,KAAK,IAAI,aAAa,SAAS,IAAI,EAAE,WAAW,SAAS,SAAS,QAAQ;AAC9E,YAAI,WAAW,UAAU;AAErB,kBAAQ,IAAI,SAAS,EAAC,KAAK,WAAA;AACvB,mBAAO,KAAK,MAAM;YACrB,CAAC;;AAEN,eAAO;MACX;AAEO,UAAI,qBAAqB,UAAU,OAAO,SAAC,KAAK,MAAI;AACvD,YAAI,CAAC,UAAS,QAAO,OAAO,EAAE,QAAQ,IAAI,MAAM;AAC5C,cAAI,OAAO,OAAO,IAAI,WAAW,IAAI;AACzC,eAAO;MACX,GAAG,CAAA,CAAE;AAEL,yBAAmB,cAAc;AACjC,yBAAmB,aAAa;AAChC,yBAAmB,YAAY;eChKf,MAAG;MAAA;eACH,OAAO,KAAG;AAAI,eAAO;MAAI;eACzB,kBAAkB,IAAI,IAAE;AAGpC,YAAI,MAAM,QAAQ,OAAO;AAAQ,iBAAO;AACxC,eAAO,SAAU,KAAG;AAChB,iBAAO,GAAG,GAAG,GAAG,CAAC;;MAEzB;eAEgB,SAAS,KAAK,KAAG;AAC7B,eAAO,WAAA;AACH,cAAI,MAAM,MAAM,SAAS;AACzB,cAAI,MAAM,MAAM,SAAS;;MAEjC;eAEgB,kBAAkB,IAAI,IAAE;AAGpC,YAAI,OAAO;AAAK,iBAAO;AACvB,eAAO,WAAA;AACH,cAAI,MAAM,GAAG,MAAM,MAAM,SAAS;AAClC,cAAI,QAAQ;AAAW,sBAAU,CAAC,IAAI;AACtC,cAAI,YAAY,KAAK,WACjB,UAAU,KAAK;AACnB,eAAK,YAAY;AACjB,eAAK,UAAU;AACf,cAAI,OAAO,GAAG,MAAM,MAAM,SAAS;AACnC,cAAI;AAAW,iBAAK,YAAY,KAAK,YAAY,SAAS,WAAW,KAAK,SAAS,IAAI;AACvF,cAAI;AAAS,iBAAK,UAAU,KAAK,UAAU,SAAS,SAAS,KAAK,OAAO,IAAI;AAC7E,iBAAO,SAAS,SAAY,OAAO;;MAE3C;eAEgB,kBAAkB,IAAI,IAAE;AACpC,YAAI,OAAO;AAAK,iBAAO;AACvB,eAAO,WAAA;AACH,aAAG,MAAM,MAAM,SAAS;AACxB,cAAI,YAAY,KAAK,WACjB,UAAU,KAAK;AACnB,eAAK,YAAY,KAAK,UAAU;AAChC,aAAG,MAAM,MAAM,SAAS;AACxB,cAAI;AAAW,iBAAK,YAAY,KAAK,YAAY,SAAS,WAAW,KAAK,SAAS,IAAI;AACvF,cAAI;AAAS,iBAAK,UAAU,KAAK,UAAU,SAAS,SAAS,KAAK,OAAO,IAAI;;MAErF;eAEgB,kBAAkB,IAAI,IAAE;AACpC,YAAI,OAAO;AAAK,iBAAO;AACvB,eAAO,SAAU,eAAa;AAC1B,cAAI,MAAM,GAAG,MAAM,MAAM,SAAS;AAClC,iBAAO,eAAe,GAAG;AACzB,cAAI,YAAY,KAAK,WACjB,UAAU,KAAK;AACnB,eAAK,YAAY;AACjB,eAAK,UAAU;AACf,cAAI,OAAO,GAAG,MAAM,MAAM,SAAS;AACnC,cAAI;AAAW,iBAAK,YAAY,KAAK,YAAY,SAAS,WAAW,KAAK,SAAS,IAAI;AACvF,cAAI;AAAS,iBAAK,UAAU,KAAK,UAAU,SAAS,SAAS,KAAK,OAAO,IAAI;AAC7E,iBAAO,QAAQ,SACV,SAAS,SAAY,SAAY,OACjC,OAAO,KAAK,IAAI;;MAE7B;eAEgB,2BAA2B,IAAI,IAAE;AAC7C,YAAI,OAAO;AAAK,iBAAO;AACvB,eAAO,WAAA;AACH,cAAI,GAAG,MAAM,MAAM,SAAS,MAAM;AAAO,mBAAO;AAChD,iBAAO,GAAG,MAAM,MAAM,SAAS;;MAEvC;eAUgB,gBAAgB,IAAI,IAAE;AAClC,YAAI,OAAO;AAAK,iBAAO;AACvB,eAAO,WAAA;AACH,cAAI,MAAM,GAAG,MAAM,MAAM,SAAS;AAClC,cAAI,OAAO,OAAO,IAAI,SAAS,YAAY;AACvC,gBAAI,OAAO,MACP,IAAI,UAAU,QACd,OAAO,IAAI,MAAM,CAAC;AACtB,mBAAO;AAAK,mBAAK,CAAC,IAAI,UAAU,CAAC;AACjC,mBAAO,IAAI,KAAK,WAAA;AACZ,qBAAO,GAAG,MAAM,MAAM,IAAI;aAC7B;;AAEL,iBAAO,GAAG,MAAM,MAAM,SAAS;;MAEvC;AClGO,UAAI,QAAQ,OAAO,aAAa,eAE/B,6CAA6C,KAAK,SAAS,IAAI;eAEvD,SAAS,OAAO,QAAM;AAClC,gBAAQ;MACZ;AC2BA,UAAI,WAAW,CAAA;AAEf,UACI,kBAAkB,KAClBC,OAAqE,OAAO,YAAY,cACpF,CAAA,IACC,WAAA;AACG,YAAI,UAAU,QAAQ,QAAO;AAC7B,YAAI,OAAO,WAAW,eAAe,CAAC,OAAO;AACzC,iBAAO,CAAC,SAAS,SAAS,OAAO,GAAG,OAAO;AAE/C,YAAM,UAAU,OAAO,OAAO,OAAO,WAAW,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AACnE,eAAO;UACH;UACA,SAAS,OAAO;UAChB;;QAEP,GAbJ,wBAAqB,KAAA,CAAA,GAAE,qBAAkB,KAAA,CAAA,GAAE,wBAAqB,KAAA,CAAA,GAcjE,oBAAoB,sBAAsB,mBAAmB;AAE1D,UAAM,gBAAgB,yBAAyB,sBAAsB;AAC5E,UAAM,qBAAqB,CAAC,CAAC;AAS7B,eAAS,uBAAoB;AACzB,uBAAe,YAAY;MAC/B;AAOA,UAAI,OAAO,SAAU,UAAU,MAAI;AAC/B,uBAAe,KAAK,CAAC,UAAU,IAAI,CAAC;AACpC,YAAI,sBAAsB;AACtB,+BAAoB;AACpB,iCAAuB;;MAE/B;AAEA,UAAI,qBAAqB,MACrB,uBAAuB,MACvB,kBAAkB,CAAA,GAClB,kBAAkB,CAAA,GAClB,kBAAkB;AAEf,UAAI,YAAY;QACnB,IAAI;QACJ,QAAQ;QACR,KAAK;QACL,YAAY,CAAA;QACZ,aAAa;QACb,KAAK;QACL,KAAK,CAAA;QACL,UAAU;;AAGP,UAAI,MAAM;AAEV,UAAI,iBAAiB,CAAA;AACrB,UAAI,oBAAoB;AACxB,UAAI,iBAAiB,CAAA;eAEJ,aAAa,IAAE;AACnC,YAAI,OAAO,SAAS;AAAU,gBAAM,IAAI,UAAU,sCAAsC;AACxF,aAAK,aAAa,CAAA;AAQlB,aAAK,OAAO;AAEZ,YAAI,MAAO,KAAK,OAAO;AAEvB,YAAI,OAAO,OAAO,YAAY;AAC1B,cAAI,OAAO;AAAU,kBAAM,IAAI,UAAU,gBAAgB;AAGzD,eAAK,SAAS,UAAU,CAAC;AACzB,eAAK,SAAS,UAAU,CAAC;AACzB,cAAI,KAAK,WAAW;AAChB,4BAAgB,MAAM,KAAK,MAAM;AACrC;;AAGJ,aAAK,SAAS;AACd,aAAK,SAAS;AACd,UAAE,IAAI;AACN,2BAAmB,MAAM,EAAE;MAC/B;AAGA,UAAM,WAAW;QACb,KAAK,WAAA;AACD,cAAI,MAAM,KAAK,cAAc;AAE7B,mBAAS,KAAM,aAAa,YAAU;AAAtC,gBAAA,QAAA;AACI,gBAAI,gBAAgB,CAAC,IAAI,WAAW,QAAQ,OAAO,gBAAgB;AACnE,gBAAM,UAAU,iBAAiB,CAAC,wBAAuB;AACzD,gBAAI,KAAK,IAAI,aAAa,SAAC,SAAS,QAAM;AACtC,kCAAoB,OAAM,IAAI,SAC1B,0BAA0B,aAAa,KAAK,eAAe,OAAO,GAClE,0BAA0B,YAAY,KAAK,eAAe,OAAO,GACjE,SACA,QACA,GAAG,CAAC;aACX;AACD,gBAAI,KAAK;AAAc,iBAAG,eAAe,KAAK;AAC9C,mBAAO;;AAGX,eAAK,YAAY;AAEjB,iBAAO;;QAIX,KAAK,SAAU,OAAK;AAChB,kBAAS,MAAM,QAAQ,SAAS,MAAM,cAAc,WAChD,WACA;YACI,KAAK,WAAA;AACD,qBAAO;;YAEX,KAAK,SAAS;WACjB;;;AAKb,YAAM,aAAa,WAAW;QAC1B,MAAM;QACN,OAAO,SAAU,aAAa,YAAU;AAEpC,8BAAoB,MAAM,IAAI,SAAS,MAAM,MAAM,aAAa,YAAY,GAAG,CAAC;;QAGpF,OAAO,SAAU,YAAU;AACvB,cAAI,UAAU,WAAW;AAAG,mBAAO,KAAK,KAAK,MAAM,UAAU;AAE7D,cAAIC,QAAO,UAAU,CAAC,GAClB,UAAU,UAAU,CAAC;AACzB,iBAAO,OAAOA,UAAS,aAAa,KAAK,KAAK,MAAM,SAAA,KAAG;AAGnD,mBAAA,eAAeA,QAAO,QAAQ,GAAG,IAAI,cAAc,GAAG;WAAC,IACzD,KAAK,KAAK,MAAM,SAAA,KAAG;AAIjB,mBAAA,OAAO,IAAI,SAASA,QAAO,QAAQ,GAAG,IAAI,cAAc,GAAG;WAAC;;QAGpE,SAAS,SAAU,WAAS;AACxB,iBAAO,KAAK,KAAK,SAAA,OAAK;AAClB,mBAAO,aAAa,QAAQ,UAAS,CAAE,EAAE,KAAK,WAAA;AAAI,qBAAA;YAAK,CAAA;aACxD,SAAA,KAAG;AACF,mBAAO,aAAa,QAAQ,UAAS,CAAE,EAAE,KAAK,WAAA;AAAI,qBAAA,cAAc,GAAG;YAAC,CAAA;WACvE;;QAGL,SAAS,SAAU,IAAI,KAAG;AAAjB,cAAA,QAAA;AACL,iBAAO,KAAK,WACR,IAAI,aAAa,SAAC,SAAS,QAAM;AAC7B,gBAAI,SAAS,WAAW,WAAA;AAAM,qBAAA,OAAO,IAAI,WAAW,QAAQ,GAAG,CAAC;YAAC,GAAE,EAAE;AACrE,kBAAK,KAAK,SAAS,MAAM,EAAE,QAAQ,aAAa,KAAK,MAAM,MAAM,CAAC;WACrE,IAAI;;OAEhB;AAED,UAAI,OAAO,WAAW,eAAe,OAAO;AACxC,gBAAQ,aAAa,WAAW,OAAO,aAAa,eAAe;AAIvE,gBAAU,MAAM,SAAQ;AAExB,eAAS,SAAS,aAAa,YAAY,SAAS,QAAQ,MAAI;AAC5D,aAAK,cAAc,OAAO,gBAAgB,aAAa,cAAc;AACrE,aAAK,aAAa,OAAO,eAAe,aAAa,aAAa;AAClE,aAAK,UAAU;AACf,aAAK,SAAS;AACd,aAAK,MAAM;MACf;AAGA,YAAO,cAAc;QACjB,KAAK,WAAA;AACD,cAAI,SAAS,WAAW,MAAM,MAAM,SAAS,EACxC,IAAI,wBAAwB;AACjC,iBAAO,IAAI,aAAa,SAAU,SAAS,QAAM;AAC7C,gBAAI,OAAO,WAAW;AAAG,sBAAQ,CAAA,CAAE;AACnC,gBAAI,YAAY,OAAO;AACvB,mBAAO,QAAQ,SAAC,GAAE,GAAC;AAAK,qBAAA,aAAa,QAAQ,CAAC,EAAE,KAAK,SAAA,GAAC;AAClD,uBAAO,CAAC,IAAI;AACZ,oBAAI,CAAC,EAAE;AAAW,0BAAQ,MAAM;iBACjC,MAAM;YAAC,CAAA;WACb;;QAGL,SAAS,SAAA,OAAK;AACV,cAAI,iBAAiB;AAAc,mBAAO;AAC1C,cAAI,SAAS,OAAO,MAAM,SAAS;AAAY,mBAAO,IAAI,aAAa,SAAC,SAAS,QAAM;AACnF,oBAAM,KAAK,SAAS,MAAM;aAC7B;AACD,cAAI,KAAK,IAAI,aAAa,UAAU,MAAM,KAAK;AAC/C,iBAAO;;QAGX,QAAQ;QAER,MAAM,WAAA;AACF,cAAI,SAAS,WAAW,MAAM,MAAM,SAAS,EAAE,IAAI,wBAAwB;AAC3E,iBAAO,IAAI,aAAa,SAAC,SAAS,QAAM;AACpC,mBAAO,IAAI,SAAA,OAAK;AAAI,qBAAA,aAAa,QAAQ,KAAK,EAAE,KAAK,SAAS,MAAM;YAAC,CAAA;WACxE;;QAGL,KAAK;UACD,KAAK,WAAA;AAAI,mBAAA;UAAG;UACZ,KAAK,SAAA,OAAK;AAAI,mBAAA,MAAM;UAAK;;QAG7B,aAAa,EAAC,KAAK,WAAA;AAAI,iBAAA;QAAW,EAAA;QAIlC,QAAQ;QAER;QAEA,WAAW;UACP,KAAK,WAAA;AAAM,mBAAA;UAAI;UACf,KAAK,SAAA,OAAK;AAAK,mBAAO;UAAK;;QAG/B,iBAAiB;UACb,KAAK,WAAA;AAAM,mBAAA;UAAe;UAC1B,KAAK,SAAA,OAAK;AAAK,8BAAkB;UAAM;;QAG3C,QAAQ,SAAC,IAAI,WAAS;AAClB,iBAAO,IAAI,aAAa,SAAC,SAAS,QAAM;AACpC,mBAAO,SAAS,SAACC,UAASC,SAAM;AAC5B,kBAAI,MAAM;AACV,kBAAI,aAAa,CAAA;AACjB,kBAAI,cAAcA;AAClB,kBAAI,WAAW,SAAS,WAAA;AAAA,oBAAA,QAAA;AAIpB,yDAAyC,WAAA;AACrC,wBAAK,WAAW,WAAW,IAAID,SAAO,IAAKC,QAAO,MAAK,WAAW,CAAC,CAAC;iBACvE;iBACF,IAAI,QAAQ;AACf,iBAAE;eACH,WAAW,SAAS,MAAM;WAChC;;OAER;AAED,UAAI,eAAe;AACf,YAAI,cAAc;AAAY,kBAAS,cAAc,cAAc,WAAA;AAC/D,gBAAM,mBAAmB,WAAW,MAAM,MAAM,SAAS,EAAE,IAAI,wBAAwB;AACvF,mBAAO,IAAI,aAAa,SAAA,SAAO;AAC3B,kBAAI,iBAAiB,WAAW;AAAG,wBAAQ,CAAA,CAAE;AAC7C,kBAAI,YAAY,iBAAiB;AACjC,kBAAM,UAAU,IAAI,MAAM,SAAS;AACnC,+BAAiB,QAAQ,SAAC,GAAG,GAAC;AAAK,uBAAA,aAAa,QAAQ,CAAC,EAAE,KACvD,SAAA,OAAK;AAAI,yBAAA,QAAQ,CAAC,IAAI,EAAC,QAAQ,aAAa,MAAK;gBAAC,GAClD,SAAA,QAAM;AAAI,yBAAA,QAAQ,CAAC,IAAI,EAAC,QAAQ,YAAY,OAAM;gBAAC,CAAA,EAClD,KAAK,WAAA;AAAI,yBAAA,EAAE,aAAa,QAAQ,OAAO;gBAAC,CAAA;cAAC,CAAA;aACjD;WACJ;AACD,YAAI,cAAc,OAAO,OAAO,mBAAmB;AAAa,kBAAQ,cAAc,OAAO,WAAA;AACzF,gBAAM,mBAAmB,WAAW,MAAM,MAAM,SAAS,EAAE,IAAI,wBAAwB;AACvF,mBAAO,IAAI,aAAa,SAAC,SAAS,QAAM;AACpC,kBAAI,iBAAiB,WAAW;AAAG,uBAAO,IAAI,eAAe,CAAA,CAAE,CAAC;AAChE,kBAAI,YAAY,iBAAiB;AACjC,kBAAM,WAAW,IAAI,MAAM,SAAS;AACpC,+BAAiB,QAAQ,SAAC,GAAG,GAAC;AAAK,uBAAA,aAAa,QAAQ,CAAC,EAAE,KACvD,SAAA,OAAK;AAAI,yBAAA,QAAQ,KAAK;gBAAC,GACvB,SAAA,SAAO;AACH,2BAAS,CAAC,IAAI;AACd,sBAAI,CAAC,EAAE;AAAW,2BAAO,IAAI,eAAe,QAAQ,CAAC;iBACxD;cAAC,CAAA;aACT;WACJ;AAED,YAAI,cAAc;AAAe,uBAAa,gBAAgB,cAAc;MAChF;AAQA,eAAS,mBAAoB,SAAS,IAAE;AAGpC,YAAI;AACA,aAAG,SAAA,OAAK;AACJ,gBAAI,QAAQ,WAAW;AAAM;AAC7B,gBAAI,UAAU;AAAS,oBAAM,IAAI,UAAU,2CAA2C;AACtF,gBAAI,oBAAoB,QAAQ,QAAQ,oBAAmB;AAC3D,gBAAI,SAAS,OAAO,MAAM,SAAS,YAAY;AAC3C,iCAAmB,SAAS,SAAC,SAAS,QAAM;AACxC,iCAAiB,eACb,MAAM,MAAM,SAAS,MAAM,IAC3B,MAAM,KAAK,SAAS,MAAM;eACjC;mBACE;AACH,sBAAQ,SAAS;AACjB,sBAAQ,SAAS;AACjB,oCAAsB,OAAO;;AAEjC,gBAAI;AAAmB,gCAAiB;aACzC,gBAAgB,KAAK,MAAM,OAAO,CAAC;iBACjC,IAAI;AACT,0BAAgB,SAAS,EAAE;;MAEnC;AAEA,eAAS,gBAAiB,SAAS,QAAM;AACrC,wBAAgB,KAAK,MAAM;AAC3B,YAAI,QAAQ,WAAW;AAAM;AAC7B,YAAI,oBAAoB,QAAQ,QAAQ,oBAAmB;AAC3D,iBAAS,gBAAgB,MAAM;AAC/B,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AAEjB,kCAA0B,OAAO;AACjC,8BAAsB,OAAO;AAC7B,YAAI;AAAmB,4BAAiB;MAC5C;AAEA,eAAS,sBAAuB,SAAO;AAEnC,YAAI,YAAY,QAAQ;AACxB,gBAAQ,aAAa,CAAA;AACrB,iBAAS,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK,EAAE,GAAG;AAClD,8BAAoB,SAAS,UAAU,CAAC,CAAC;;AAE7C,YAAI,MAAM,QAAQ;AAClB,UAAE,IAAI,OAAO,IAAI,SAAQ;AACzB,YAAI,sBAAsB,GAAG;AAMzB,YAAE;AACF,eAAK,WAAA;AACD,gBAAI,EAAE,sBAAsB;AAAG,mCAAoB;aACpD,CAAA,CAAE;;MAEb;AAEA,eAAS,oBAAoB,SAAS,UAAQ;AAC1C,YAAI,QAAQ,WAAW,MAAM;AACzB,kBAAQ,WAAW,KAAK,QAAQ;AAChC;;AAGJ,YAAI,KAAK,QAAQ,SAAS,SAAS,cAAc,SAAS;AAC1D,YAAI,OAAO,MAAM;AAEb,kBAAQ,QAAQ,SAAS,SAAS,UAAU,SAAS,QAAS,QAAQ,MAAM;;AAEhF,UAAE,SAAS,IAAI;AACf,UAAE;AACF,aAAM,cAAc,CAAC,IAAI,SAAS,QAAQ,CAAC;MAC/C;AAEA,eAAS,aAAc,IAAI,SAAS,UAAQ;AACxC,YAAI;AAEA,cAAI,KAAK,QAAQ,QAAQ;AAEzB,cAAI,CAAC,QAAQ,UAAU,gBAAgB;AAAQ,8BAAkB,CAAA;AAEjE,gBAAM,SAAS,QAAQ,eAAe,QAAQ,aAAa,IAAI,WAAA;AAAI,mBAAA,GAAI,KAAK;UAAC,CAAA,IAAI,GAAI,KAAK;AAC1F,cAAI,CAAC,QAAQ,UAAU,gBAAgB,QAAQ,KAAK,MAAM,IAAI;AAC1D,+BAAmB,OAAO;;AAE9B,mBAAS,QAAQ,GAAG;iBACf,GAAG;AAER,mBAAS,OAAO,CAAC;;AAEjB,cAAI,EAAE,sBAAsB;AAAG,iCAAoB;AACnD,YAAE,SAAS,IAAI,OAAO,SAAS,IAAI,SAAQ;;MAEnD;AAKA,eAAS,eAAY;AACjB,eAAO,WAAW,WAAA;AAGd,8BAAmB,KAAM,kBAAiB;SAC7C;MACL;eAEgB,sBAAmB;AAC/B,YAAI,cAAc;AAClB,6BAAqB;AACrB,+BAAuB;AACvB,eAAO;MACX;eAUgB,oBAAiB;AAC7B,YAAI,WAAW,GAAG;AAClB,WAAG;AACC,iBAAO,eAAe,SAAS,GAAG;AAC9B,wBAAY;AACZ,6BAAiB,CAAA;AACjB,gBAAI,UAAU;AACd,iBAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACpB,kBAAI,OAAO,UAAU,CAAC;AACtB,mBAAK,CAAC,EAAE,MAAM,MAAM,KAAK,CAAC,CAAC;;;iBAG9B,eAAe,SAAS;AACjC,6BAAqB;AACrB,+BAAuB;MAC3B;AAEA,eAAS,uBAAoB;AACzB,YAAI,gBAAgB;AACpB,0BAAkB,CAAA;AAClB,sBAAc,QAAQ,SAAA,GAAC;AACnB,YAAE,KAAK,YAAY,KAAK,MAAM,EAAE,QAAQ,CAAC;SAC5C;AACD,YAAI,aAAa,eAAe,MAAM,CAAC;AACvC,YAAI,IAAI,WAAW;AACnB,eAAO;AAAG,qBAAW,EAAE,CAAC,EAAC;MAC7B;AAEA,eAAS,yCAA0C,IAAE;AACjD,iBAAS,YAAS;AACd,aAAE;AACF,yBAAe,OAAO,eAAe,QAAQ,SAAS,GAAG,CAAC;;AAE9D,uBAAe,KAAK,SAAS;AAC7B,UAAE;AACF,aAAK,WAAA;AACD,cAAI,EAAE,sBAAsB;AAAG,iCAAoB;WACpD,CAAA,CAAE;MACT;AAEA,eAAS,0BAA0B,SAAO;AAItC,YAAI,CAAC,gBAAgB,KAAK,SAAA,GAAC;AAAI,iBAAA,EAAE,WAAW,QAAQ;QAAM,CAAA;AACtD,0BAAgB,KAAK,OAAO;MACpC;AAEA,eAAS,mBAAmB,SAAO;AAI/B,YAAI,IAAI,gBAAgB;AACxB,eAAO;AAAG,cAAI,gBAAgB,EAAE,CAAC,EAAE,WAAW,QAAQ,QAAQ;AAG1D,4BAAgB,OAAO,GAAG,CAAC;AAC3B;;MAER;AAEA,eAAS,cAAe,QAAM;AAC1B,eAAO,IAAI,aAAa,UAAU,OAAO,MAAM;MACnD;eAEgB,KAAM,IAAI,cAAY;AAClC,YAAI,MAAM;AACV,eAAO,WAAA;AACH,cAAI,cAAc,oBAAmB,GACjC,aAAa;AAEjB,cAAI;AACA,yBAAa,KAAK,IAAI;AACtB,mBAAO,GAAG,MAAM,MAAM,SAAS;mBAC1B,GAAG;AACR,4BAAgB,aAAa,CAAC;;AAE9B,yBAAa,YAAY,KAAK;AAC9B,gBAAI;AAAa,gCAAiB;;;MAG9C;AAMA,UAAM,OAAO,EAAE,QAAQ,GAAG,QAAQ,GAAG,IAAI,EAAC;AAC1C,UAAI,cAAc;AAClB,UAAI,YAAY,CAAA;AAChB,UAAI,aAAa;AACjB,UAAI,cAAc;AAGlB,UAAI,kBAAkB;eACN,SAAU,IAAIC,QAAO,IAAI,IAAE;AACvC,YAAI,SAAS,KACT,MAAM,OAAO,OAAO,MAAM;AAC9B,YAAI,SAAS;AACb,YAAI,MAAM;AACV,YAAI,SAAS;AACb,YAAI,KAAK,EAAE;AAEK,kBAAU;AAC1B,YAAI,MAAM,qBAAqB;UAC3B,SAAS;UACT,aAAa,EAAC,OAAO,cAAc,cAAc,MAAM,UAAU,KAAI;UACrE,KAAK,aAAa;UAClB,MAAM,aAAa;UACnB,YAAY,aAAa;UACzB,KAAK,aAAa;UAClB,SAAS,aAAa;UACtB,QAAQ,aAAa;YACrB,CAAA;AACJ,YAAIA;AAAO,iBAAO,KAAKA,MAAK;AAM5B,UAAE,OAAO;AACT,YAAI,WAAW,WAAA;AACX,YAAE,KAAK,OAAO,OAAO,KAAK,OAAO,SAAQ;;AAE7C,YAAI,KAAK,OAAQ,KAAK,IAAI,IAAI,EAAE;AAChC,YAAI,IAAI,QAAQ;AAAG,cAAI,SAAQ;AAC/B,eAAO;MACX;eAIgB,0BAAuB;AACnC,YAAI,CAAC,KAAK;AAAI,eAAK,KAAK,EAAE;AAC1B,UAAE,KAAK;AACP,aAAK,UAAU;AACf,eAAO,KAAK;MAChB;eAKgB,0BAAuB;AACnC,YAAI,CAAC,KAAK;AAAQ,iBAAO;AACzB,YAAI,EAAE,KAAK,WAAW;AAAG,eAAK,KAAK;AACnC,aAAK,SAAS,KAAK,SAAS;AAC5B,eAAO;MACX;AAEA,WAAK,KAAG,mBAAmB,QAAQ,eAAe,MAAM,IAAI;AAGxD,kCAA0B,0BAA0B;MACxD;eAGgB,yBAA0B,iBAAe;AACrD,YAAI,KAAK,UAAU,mBAAmB,gBAAgB,gBAAgB,eAAe;AACjF,kCAAuB;AACvB,iBAAO,gBAAgB,KAAK,SAAA,GAAC;AACzB,oCAAuB;AACvB,mBAAO;aACR,SAAA,GAAC;AACA,oCAAuB;AACvB,mBAAO,UAAU,CAAC;WACrB;;AAEL,eAAO;MACX;AAEA,eAAS,cAAc,YAAU;AAC7B,UAAE;AAGF,YAAI,CAAC,KAAK,UAAU,EAAE,KAAK,WAAW,GAAG;AACrC,eAAK,SAAS,KAAK,SAAS,KAAK,KAAK;;AAG1C,kBAAU,KAAK,GAAG;AAClB,qBAAa,YAAY,IAAI;MACjC;AAEA,eAAS,gBAAa;AAClB,YAAI,OAAO,UAAU,UAAU,SAAO,CAAC;AACvC,kBAAU,IAAG;AACb,qBAAa,MAAM,KAAK;MAC5B;AAEA,eAAS,aAAc,YAAY,eAAa;AAC5C,YAAI,cAAc;AAClB,YAAI,gBAAgB,KAAK,WAAW,CAAC,gBAAgB,eAAe,OAAO,eAAe,CAAC,EAAE,cAAc,eAAe,MAAM;AAG5H,yBAAe,gBAAgB,cAAc,KAAK,MAAM,UAAU,IAAI,aAAa;;AAEvF,YAAI,eAAe;AAAK;AAExB,cAAM;AAGN,YAAI,gBAAgB;AAAW,oBAAU,MAAM,SAAQ;AAEvD,YAAI,oBAAoB;AAEpB,cAAI,gBAAgB,UAAU,IAAI;AAElC,cAAI,YAAY,WAAW;AAE3B,cAAI,YAAY,UAAU,WAAW,QAAQ;AAIzC,mBAAO,eAAe,SAAS,WAAW,UAAU,WAAW;AAI/D,0BAAc,MAAM,UAAU;AAC9B,0BAAc,OAAO,UAAU;AAC/B,0BAAc,UAAU,UAAU;AAClC,0BAAc,SAAS,UAAU;AACjC,gBAAI,UAAU;AAAY,4BAAc,aAAa,UAAU;AAC/D,gBAAI,UAAU;AAAK,4BAAc,MAAM,UAAU;;;MAG7D;AAEA,eAAS,WAAQ;AACb,YAAI,gBAAgB,QAAQ;AAC5B,eAAO,qBAAqB;UACxB,SAAS;UACT,aAAa,OAAO,yBAAyB,SAAS,SAAS;UAC/D,KAAK,cAAc;UACnB,MAAM,cAAc;UACpB,YAAY,cAAc;UAC1B,KAAK,cAAc;UACnB,SAAS,cAAc;UACvB,QAAQ,cAAc;YACtB,CAAA;MACR;eAEgB,OAAQ,KAAK,IAAI,IAAI,IAAI,IAAE;AACvC,YAAI,aAAa;AACjB,YAAI;AACA,uBAAa,KAAK,IAAI;AACtB,iBAAO,GAAG,IAAI,IAAI,EAAE;;AAEpB,uBAAa,YAAY,KAAK;;MAEtC;AAEA,eAAS,0BAA0B,IAAI,MAAM,eAAe,SAAO;AAC/D,eAAO,OAAO,OAAO,aAAa,KAAK,WAAA;AACnC,cAAI,YAAY;AAChB,cAAI;AAAe,oCAAuB;AAC1C,uBAAa,MAAM,IAAI;AACvB,cAAI;AACA,mBAAO,GAAG,MAAM,MAAM,SAAS;;AAE/B,yBAAa,WAAW,KAAK;AAC7B,gBAAI;AAAS,6BAAe,uBAAuB;;;MAG/D;eAGgB,oBAAoB,IAAE;AAClC,YAAI,YAAY,iBAAiB,KAAK,WAAW,GAAG;AAChD,cAAI,eAAe,GAAG;AAClB,eAAE;iBACC;AACH,mCAAuB,EAAE;;eAE1B;AACH,qBAAW,IAAI,CAAC;;MAExB;AAEO,UAAI,YAAY,aAAa;eC9tBpB,gBACd,IACA,MACA,YACA,IAAgD;AAGhD,YAAI,CAAC,GAAG,SAAU,CAAC,GAAG,OAAO,iBAAiB,CAAC,IAAI,cAAc,CAAC,GAAG,OAAQ;AAC3E,cAAI,GAAG,OAAO,cAAc;AAG1B,mBAAO,UAAU,IAAI,WAAW,eAAe,GAAG,OAAO,WAAW,CAAC;;AAEvE,cAAI,CAAC,GAAG,OAAO,eAAe;AAC5B,gBAAI,CAAC,GAAG,OAAO;AACb,qBAAO,UAAU,IAAI,WAAW,eAAc,CAAE;AAClD,eAAG,KAAI,EAAG,MAAM,GAAG;;AAErB,iBAAO,GAAG,OAAO,eAAe,KAAK,WAAA;AAAM,mBAAA,gBAAgB,IAAI,MAAM,YAAY,EAAE;UAAC,CAAA;eAC/E;AACL,cAAI,QAAQ,GAAG,mBAAmB,MAAM,YAAY,GAAG,SAAS;AAChE,cAAI;AACF,kBAAM,OAAM;AACZ,eAAG,OAAO,iBAAiB;mBACpB,IAAI;AACX,gBAAI,GAAG,SAAS,SAAS,gBAAgB,GAAG,OAAM,KAAM,EAAE,GAAG,OAAO,iBAAiB,GAAG;AACtF,sBAAQ,KAAK,0BAA0B;AACvC,iBAAG,MAAM,EAAC,iBAAiB,MAAK,CAAC;AACjC,qBAAO,GAAG,KAAI,EAAG,KAAK,WAAA;AAAI,uBAAA,gBAAgB,IAAI,MAAM,YAAY,EAAE;cAAC,CAAA;;AAErE,mBAAO,UAAU,EAAE;;AAErB,iBAAO,MAAM,SAAS,MAAM,SAAC,SAAS,QAAM;AAC1C,mBAAO,SAAS,WAAA;AACd,kBAAI,QAAQ;AACZ,qBAAO,GAAG,SAAS,QAAQ,KAAK;aACjC;WACF,EAAE,KAAK,SAAA,QAAM;AAWZ,gBAAI,SAAS;AAAa,kBAAI;AAAC,sBAAM,SAAS,OAAM;uBAAKJ,KAAM;cAAA;AAC/D,mBAAO,SAAS,aAAa,SAAS,MAAM,YAAY,KAAK,WAAA;AAAM,qBAAA;YAAM,CAAA;WAC1E;;MAKL;AC9DO,UAAM,gBAAgB;AACtB,UAAM,YAAY,OAAO,aAAa,KAAK;AAC3C,UAAM,SAAS;AACf,UAAM,uBACX;AACK,UAAM,kBAAkB;AACxB,UAAM,cAAuB,CAAA;AAE7B,UAAM,aAAa;AACnB,UAAM,WAAW;AACjB,UAAM,YAAY;eCZT,QAAQ,SAAS,SAAO;AACtC,eAAO,UACH,UACI,WAAA;AAAc,iBAAO,QAAQ,MAAM,MAAM,SAAS,KAAK,QAAQ,MAAM,MAAM,SAAS;QAAE,IACtF,UACJ;MACN;ACJO,UAAM,WAA2B;QACtC,MAAI;QACJ,OAAO;QACP,WAAW;QACX,OAAO,CAAC,CAAA,CAAE;QACV,WAAW;;eCFG,8BAA8B,SAAmC;AAE/E,eAAO,OAAO,YAAY,YAAY,CAAC,KAAK,KAAK,OAAO,IACtD,SAAC,KAAW;AACZ,cAAI,IAAI,OAAO,MAAM,UAAc,WAAW,KAAM;AAIlD,kBAAM,UAAU,GAAG;AACnB,mBAAO,IAAI,OAAO;;AAEpB,iBAAO;YAEP,SAAC,KAAW;AAAK,iBAAA;QAAG;MACxB;eCjBgBK,UAAM;AACpB,cAAM,WAAW,KAAI;MACvB;eCCgBC,KAAI,GAAQ,GAAM;AAChC,YAAI;AACF,cAAM,KAAK,KAAK,CAAC;AACjB,cAAM,KAAK,KAAK,CAAC;AACjB,cAAI,OAAO,IAAI;AACb,gBAAI,OAAO;AAAS,qBAAO;AAC3B,gBAAI,OAAO;AAAS,qBAAO;AAC3B,gBAAI,OAAO;AAAU,qBAAO;AAC5B,gBAAI,OAAO;AAAU,qBAAO;AAC5B,gBAAI,OAAO;AAAU,qBAAO;AAC5B,gBAAI,OAAO;AAAU,qBAAO;AAC5B,gBAAI,OAAO;AAAQ,qBAAO;AAC1B,gBAAI,OAAO;AAAQ,qBAAO;AAC1B,mBAAO;;AAET,kBAAQ,IAAE;YACR,KAAK;YACL,KAAK;YACL,KAAK;AACH,qBAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;YAClC,KAAK,UAAU;AACb,qBAAO,mBAAmB,cAAc,CAAC,GAAG,cAAc,CAAC,CAAC;;YAE9D,KAAK;AACH,qBAAO,cAAc,GAAG,CAAC;;iBAE7BN,KAAM;QAAA;AACR,eAAO;MACT;eAEgB,cAAc,GAAU,GAAQ;AAC9C,YAAM,KAAK,EAAE;AACb,YAAM,KAAK,EAAE;AACb,YAAM,IAAI,KAAK,KAAK,KAAK;AACzB,iBAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,cAAM,MAAMM,KAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1B,cAAI,QAAQ;AAAG,mBAAO;;AAExB,eAAO,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;MACxC;eAEgB,mBACd,GACA,GAAa;AAEb,YAAM,KAAK,EAAE;AACb,YAAM,KAAK,EAAE;AACb,YAAM,IAAI,KAAK,KAAK,KAAK;AACzB,iBAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,cAAI,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG,mBAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK;;AAE/C,eAAO,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;MACxC;AAGA,eAAS,KAAK,GAAM;AAClB,YAAM,IAAI,OAAO;AACjB,YAAI,MAAM;AAAU,iBAAO;AAC3B,YAAI,YAAY,OAAO,CAAC;AAAG,iBAAO;AAClC,YAAM,QAAQ,YAAY,CAAC;AAC3B,eAAO,UAAU,gBAAgB,WAAY;MAC/C;AAkBA,eAAS,cAAc,GAAa;AAClC,YAAI,aAAa;AAAY,iBAAO;AACpC,YAAI,YAAY,OAAO,CAAC;AAEtB,iBAAO,IAAI,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU;AAC5D,eAAO,IAAI,WAAW,CAAC;MACzB;AChEA,UAAA,QAAA,WAAA;AAAA,iBAAAC,SAAA;;AAQE,QAAAA,OAAA,UAAA,SAAA,SACE,MACA,IACA,aAA8B;AAE9B,cAAM,QAAqB,KAAK,OAAO,IAAI;AAC3C,cAAM,YAAY,KAAK;AAEvB,cAAMC,QAAO,SAAS,OAAO,YAAY,eAAe,QAAQ,cAAc,QAAQ,WAAW,UAAA,OAAU,SAAS,aAAa,SAAS,SAAO,GAAA,EAAA,OAAK,KAAK,IAAI,CAAE;AAEjK,mBAAS,wBAAwB,SAAS,QAAQC,QAAkB;AAClE,gBAAI,CAACA,OAAM,OAAO,SAAS;AACzB,oBAAM,IAAI,WAAW,SAAS,WAAW,YAAY,0BAA0B;AACjF,mBAAO,GAAGA,OAAM,UAAUA,MAAK;;AAejC,cAAM,cAAc,oBAAmB;AACvC,cAAI;AACF,gBAAI,IAAI,SAAS,MAAM,GAAG,WAAW,KAAK,GAAG,SAC3C,UAAU,IAAI,QACZ,MAAM,SAAS,MAAM,yBAAyB,WAAW,IACzD,SAAS,WAAA;AAAM,qBAAA,MAAM,SAAS,MAAM,yBAAyB,WAAW;YAAC,GAAE,EAAE,OAAc,WAAW,IAAI,aAAa,IAAG,CAAE,IAC9H,gBAAgB,KAAK,IAAI,MAAM,CAAC,KAAK,IAAI,GAAG,uBAAuB;AACrE,gBAAID,OAAM;AACR,gBAAE,eAAeA;AACjB,kBAAI,EAAE,MAAM,SAAA,KAAG;AACb,wBAAQ,MAAM,GAAG;AACjB,uBAAO,UAAU,GAAG;eACrB;;AAEH,mBAAO;;AAEP,gBAAI;AAAa,gCAAiB;;;AAStC,QAAAD,OAAA,UAAA,MAAA,SAAI,WAAW,IAAG;AAAlB,cAAA,QAAA;AACE,cAAI,aAAa,UAAU,gBAAgB;AACzC,mBAAO,KAAK,MAAM,SAA6C,EAAE,MAAM,EAAE;AAC3E,cAAI,aAAa;AAAM,mBAAO,UAAU,IAAI,WAAW,KAAK,iCAAiC,CAAC;AAE9F,iBAAO,KAAK,OAAO,YAAY,SAAC,OAAK;AACnC,mBAAO,MAAK,KAAK,IAAI,EAAC,OAAO,KAAK,UAAS,CAAC,EACzC,KAAK,SAAA,KAAG;AAAI,qBAAA,MAAK,KAAK,QAAQ,KAAK,GAAG;YAAC,CAAA;WAC3C,EAAE,KAAK,EAAE;;AAQZ,QAAAA,OAAA,UAAA,QAAA,SAAM,aAAiE;AACrE,cAAI,OAAO,gBAAgB;AACzB,mBAAO,IAAI,KAAK,GAAG,YAAY,MAAM,WAAW;AAClD,cAAI,QAAQ,WAAW;AACrB,mBAAO,IAAI,KAAK,GAAG,YAAY,MAAM,IAAA,OAAI,YAAY,KAAK,GAAG,GAAC,GAAA,CAAG;AAEnE,cAAM,WAAW,KAAK,WAAW;AACjC,cAAI,SAAS,WAAW;AAEtB,mBAAO,KACJ,MAAM,SAAS,CAAC,CAAC,EACjB,OAAO,YAAY,SAAS,CAAC,CAAC,CAAC;AAKpC,cAAM,gBAAgB,KAAK,OAAO,QAAQ,OAAO,KAAK,OAAO,OAAO,EAAE,OAAO,SAAA,IAAE;AAC7E,gBACE,GAAG,YACH,SAAS,MAAM,SAAA,SAAO;AAAI,qBAAA,GAAG,QAAQ,QAAQ,OAAO,KAAK;YAAC,CAAA,GAAG;AAC3D,uBAAS,IAAE,GAAG,IAAE,SAAS,QAAQ,EAAE,GAAG;AACpC,oBAAI,SAAS,QAAQ,GAAG,QAAQ,CAAC,CAAC,MAAM;AAAI,yBAAO;;AAErD,qBAAO;;AAET,mBAAO;WACR,EAAE,KAAK,SAAC,GAAE,GAAC;AAAK,mBAAA,EAAE,QAAQ,SAAS,EAAE,QAAQ;UAAM,CAAA,EAAE,CAAC;AAEzD,cAAI,iBAAiB,KAAK,GAAG,YAAY,WAAW;AAGlD,gBAAM,uBAAwB,cAAc,QAAqB,MAAM,GAAG,SAAS,MAAM;AACzF,mBAAO,KACJ,MAAM,oBAAoB,EAC1B,OAAO,qBAAqB,IAAI,SAAA,IAAE;AAAI,qBAAA,YAAY,EAAE;YAAC,CAAA,CAAC;;AAG3D,cAAI,CAAC,iBAAiB;AAAO,oBAAQ,KACnC,aAAA,OAAa,KAAK,UAAU,WAAW,GAAC,MAAA,EAAA,OAAO,KAAK,MAAI,wBAAA,IACxD,mBAAA,OAAmB,SAAS,KAAK,GAAG,GAAC,GAAA,CAAG;AAIlC,cAAA,YAAc,KAAK,OAAM;AAEjC,mBAAS,OAAO,GAAG,GAAC;AAClB,mBAAOD,KAAI,GAAG,CAAC,MAAM;;AAGjB,cAAAN,MAAwB,SAAS,OAAO,SAACA,KAA2B,SAAO;gBAAjC,YAASA,IAAA,CAAA,GAAE,eAAYA,IAAA,CAAA;AACrE,gBAAM,QAAQ,UAAU,OAAO;AAC/B,gBAAM,QAAQ,YAAY,OAAO;AACjC,mBAAO;cACL,aAAa;cACb,aAAa,CAAC,QACZ,QACE,cACA,SAAS,MAAM,QACb,SAAA,GAAC;AACC,oBAAM,OAAO,aAAa,GAAG,OAAO;AACpC,uBAAO,QAAQ,IAAI,KAAK,KAAK,KAAK,SAAA,MAAI;AAAI,yBAAA,OAAO,OAAO,IAAI;gBAAC,CAAA;kBAC3D,SAAA,GAAC;AAAI,uBAAA,OAAO,OAAO,aAAa,GAAG,OAAO,CAAC;cAAC,CAAA,IAClD;;aAEL,CAAC,MAAM,IAAI,CAAC,GAfR,MAAGA,IAAA,CAAA,GAAE,iBAAcA,IAAA,CAAA;AAiB1B,iBAAO,MACL,KAAK,MAAM,IAAI,IAAI,EAAE,OAAO,YAAY,IAAI,OAAO,CAAC,EACjD,OAAO,cAAc,IACxB,gBACE,KAAK,OAAO,cAAc,IAC1B,KAAK,MAAM,QAAQ,EAAE,OAAO,EAAE;;AAQpC,QAAAO,OAAA,UAAA,SAAA,SAAO,gBAAqC;AAC1C,iBAAO,KAAK,aAAY,EAAG,IAAI,cAAc;;AAQ/C,QAAAA,OAAA,UAAA,QAAA,SAAM,cAAkB;AACtB,iBAAO,KAAK,aAAY,EAAG,MAAM,YAAY;;AAQ/C,QAAAA,OAAA,UAAA,SAAA,SAAO,QAAc;AACnB,iBAAO,KAAK,aAAY,EAAG,OAAO,MAAM;;AAQ1C,QAAAA,OAAA,UAAA,QAAA,SAAM,SAAe;AACnB,iBAAO,KAAK,aAAY,EAAG,MAAM,OAAO;;AAQ1C,QAAAA,OAAA,UAAA,OAAA,SAAK,UAAsF;AACzF,iBAAO,KAAK,aAAY,EAAG,KAAK,QAAQ;;AAQ1C,QAAAA,OAAA,UAAA,UAAA,SAAQ,cAAkB;AACxB,iBAAO,KAAK,aAAY,EAAG,QAAQ,YAAY;;AAQjD,QAAAA,OAAA,UAAA,eAAA,WAAA;AACE,iBAAO,IAAI,KAAK,GAAG,WAAW,IAAI,KAAK,GAAG,YAAY,IAAI,CAAC;;AAQ7D,QAAAA,OAAA,UAAA,UAAA,SAAQ,OAAwB;AAC9B,iBAAO,IAAI,KAAK,GAAG,WACjB,IAAI,KAAK,GAAG,YAAY,MAAM,QAAQ,KAAK,IACzC,IAAA,OAAI,MAAM,KAAK,GAAG,GAAC,GAAA,IACnB,KAAK,CAAC;;AAQZ,QAAAA,OAAA,UAAA,UAAA,WAAA;AACE,iBAAO,KAAK,aAAY,EAAG,QAAO;;AAQpC,QAAAA,OAAA,UAAA,aAAA,SAAW,aAAqB;AACxB,cAAAP,MAAwB,MAAvB,KAAEA,IAAA,IAAQ,YAASA,IAAA;AAC1B,eAAK,OAAO,cAAc;AAC1B,cAAI,YAAY,qBAAqBK,SAAQ;AAC3C,0BAAW,SAAA,QAAA;AAAiB,wBAAA,SAAA,MAAA;AAAd,uBAAA,UAAA;;;AACZ,qBAAA,eAAI,QAAA,WAAA,MAAE;qBAAN,WAAA;AAAY,yBAAO;gBAAG;;;;AACtB,sBAAA,UAAA,QAAA,WAAA;AAAU,uBAAO;cAAU;AAC7B,qBAAA;cAH6B,WAAmB;;AASlD,cAAM,iBAAiB,oBAAI,IAAG;AAC9B,mBAAS,QAAQ,YAAY,WAAW,OAAO,QAAQ,SAAS,KAAK,GAAG;AACtE,mBAAO,oBAAoB,KAAK,EAAE,QAAQ,SAAA,UAAQ;AAAI,qBAAA,eAAe,IAAI,QAAQ;YAAC,CAAA;;AAKpF,cAAM,WAAW,SAAC,KAAW;AAC3B,gBAAI,CAAC;AAAK,qBAAO;AAEjB,gBAAM,MAAM,OAAO,OAAO,YAAY,SAAS;AAG/C,qBAAS,KAAK;AAAK,kBAAI,CAAC,eAAe,IAAI,CAAC;AAAG,oBAAI;AAAE,sBAAI,CAAC,IAAI,IAAI,CAAC;yBAAY,GAAG;gBAAA;AAClF,mBAAO;;AAGT,cAAI,KAAK,OAAO,UAAU;AACxB,iBAAK,KAAK,QAAQ,YAAY,KAAK,OAAO,QAAQ;;AAEpD,eAAK,OAAO,WAAW;AACvB,eAAK,KAAK,WAAW,QAAQ;AAC7B,iBAAO;;AAIT,QAAAE,OAAA,UAAA,cAAA,WAAA;AACE,mBAAS,MAAO,SAAO;AACrB,mBAAO,MAAM,OAAO;;AAEtB,iBAAO,KAAK,WAAW,KAAK;;AAQ9B,QAAAA,OAAA,UAAA,MAAA,SAAI,KAAK,KAAmB;AAA5B,cAAA,QAAA;AACQ,cAAAP,MAAkB,KAAK,OAAO,SAA7B,OAAIA,IAAA,MAAE,UAAOA,IAAA;AACpB,cAAI,WAAW;AACf,cAAI,WAAW,MAAM;AACnB,uBAAW,8BAA8B,OAAO,EAAE,GAAG;;AAEvD,iBAAO,KAAK,OAAO,aAAa,SAAA,OAAK;AACnC,mBAAO,MAAK,KAAK,OAAO,EAAC,OAAO,MAAM,OAAO,MAAM,OAAO,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,CAAC,QAAQ,EAAC,CAAC;WACnG,EAAE,KAAK,SAAA,KAAG;AAAI,mBAAA,IAAI,cAAcU,aAAQ,OAAO,IAAI,SAAS,CAAC,CAAC,IAAI,IAAI;UAAU,CAAA,EAChF,KAAK,SAAA,YAAU;AACd,gBAAI,SAAS;AAIX,kBAAG;AAAC,6BAAa,KAAK,SAAS,UAAU;uBAAS,GAAE;cAAA;;AAEtD,mBAAO;WACR;;AAQH,QAAAH,OAAA,UAAA,SAAA,SAAO,aAAa,eAAqH;AACvI,cAAI,OAAO,gBAAgB,YAAY,CAAC,QAAQ,WAAW,GAAG;AAC5D,gBAAM,MAAM,aAAa,aAAa,KAAK,OAAO,QAAQ,OAAO;AACjE,gBAAI,QAAQ;AAAW,qBAAO,UAAU,IAAI,WAAW,gBACrD,+CAA+C,CAAC;AAmBlD,mBAAO,KAAK,MAAM,KAAK,EAAE,OAAO,GAAG,EAAE,OAAO,aAAa;iBACpD;AAEL,mBAAO,KAAK,MAAM,KAAK,EAAE,OAAO,WAAW,EAAE,OAAO,aAAa;;;AASrE,QAAAA,OAAA,UAAA,MAAA,SAAI,KAAK,KAAmB;AAA5B,cAAA,QAAA;AACQ,cAAAP,MAAkB,KAAK,OAAO,SAA7B,OAAIA,IAAA,MAAE,UAAOA,IAAA;AACpB,cAAI,WAAW;AACf,cAAI,WAAW,MAAM;AACnB,uBAAW,8BAA8B,OAAO,EAAE,GAAG;;AAEvD,iBAAO,KAAK,OACV,aACA,SAAA,OAAK;AAAI,mBAAA,MAAK,KAAK,OAAO,EAAC,OAAO,MAAM,OAAO,QAAQ,CAAC,QAAQ,GAAG,MAAM,OAAO,OAAO,CAAC,GAAG,IAAI,KAAI,CAAC;UAAC,CAAA,EACtG,KAAK,SAAA,KAAG;AAAI,mBAAA,IAAI,cAAcU,aAAQ,OAAO,IAAI,SAAS,CAAC,CAAC,IAAI,IAAI;UAAU,CAAA,EAC9E,KAAK,SAAA,YAAU;AACd,gBAAI,SAAS;AAIX,kBAAG;AAAC,6BAAa,KAAK,SAAS,UAAU;uBAAS,GAAE;cAAA;;AAEtD,mBAAO;WACR;;AAQH,QAAAH,OAAA,UAAA,SAAA,SAAO,KAAkB;AAAzB,cAAA,QAAA;AACE,iBAAO,KAAK,OAAO,aACjB,SAAA,OAAK;AAAI,mBAAA,MAAK,KAAK,OAAO,EAAC,OAAO,MAAM,UAAU,MAAM,CAAC,GAAG,EAAC,CAAC;UAAC,CAAA,EAChE,KAAK,SAAA,KAAG;AAAI,mBAAA,IAAI,cAAcG,aAAQ,OAAO,IAAI,SAAS,CAAC,CAAC,IAAI;UAAS,CAAA;;AAQ5E,QAAAH,OAAA,UAAA,QAAA,WAAA;AAAA,cAAA,QAAA;AACE,iBAAO,KAAK,OAAO,aACjB,SAAA,OAAK;AAAI,mBAAA,MAAK,KAAK,OAAO,EAAC,OAAO,MAAM,eAAe,OAAO,SAAQ,CAAC;UAAC,CAAA,EACrE,KAAK,SAAA,KAAG;AAAI,mBAAA,IAAI,cAAcG,aAAQ,OAAO,IAAI,SAAS,CAAC,CAAC,IAAI;UAAS,CAAA;;AAShF,QAAAH,OAAA,UAAA,UAAA,SAAQI,OAAqB;AAA7B,cAAA,QAAA;AACE,iBAAO,KAAK,OAAO,YAAY,SAAA,OAAK;AAClC,mBAAO,MAAK,KAAK,QAAQ;cACvB,MAAIA;cACJ;aACD,EAAE,KAAK,SAAA,QAAM;AAAI,qBAAA,OAAO,IAAI,SAAA,KAAG;AAAI,uBAAA,MAAK,KAAK,QAAQ,KAAK,GAAG;cAAC,CAAA;YAAC,CAAA;WACjE;;AAQH,QAAAJ,OAAA,UAAA,UAAA,SACE,SACA,eACA,SAA+B;AAHjC,cAAA,QAAA;AAKE,cAAMI,QAAO,MAAM,QAAQ,aAAa,IAAI,gBAAgB;AAC5D,oBAAU,YAAYA,QAAO,SAAY;AACzC,cAAM,cAAc,UAAU,QAAQ,UAAU;AAEhD,iBAAO,KAAK,OAAO,aAAa,SAAA,OAAK;AAC7B,gBAAAX,MAAkB,MAAK,OAAO,SAA7B,OAAIA,IAAA,MAAE,UAAOA,IAAA;AACpB,gBAAI,WAAWW;AACb,oBAAM,IAAI,WAAW,gBAAgB,8DAA8D;AACrG,gBAAIA,SAAQA,MAAK,WAAW,QAAQ;AAClC,oBAAM,IAAI,WAAW,gBAAgB,sDAAsD;AAE7F,gBAAM,aAAa,QAAQ;AAC3B,gBAAI,eAAe,WAAW,OAC5B,QAAQ,IAAI,8BAA8B,OAAO,CAAC,IAClD;AACF,mBAAO,MAAK,KAAK,OACf,EAAC,OAAO,MAAM,OAAO,MAAMA,OAAyB,QAAQ,cAAc,YAAW,CAAC,EAErF,KAAK,SAACX,KAA2C;kBAA1C,cAAWA,IAAA,aAAE,UAAOA,IAAA,SAAC,aAAUA,IAAA,YAAE,WAAQA,IAAA;AAC/C,kBAAM,SAAS,cAAc,UAAU;AACvC,kBAAI,gBAAgB;AAAG,uBAAO;AAC9B,oBAAM,IAAI,UACR,GAAA,OAAG,MAAK,MAAI,cAAA,EAAA,OAAe,aAAW,MAAA,EAAA,OAAO,YAAU,oBAAA,GAAsB,QAAQ;aACxF;WACJ;;AAQH,QAAAO,OAAA,UAAA,UAAA,SACE,SACA,eACA,SAA+B;AAHjC,cAAA,QAAA;AAKE,cAAMI,QAAO,MAAM,QAAQ,aAAa,IAAI,gBAAgB;AAC5D,oBAAU,YAAYA,QAAO,SAAY;AACzC,cAAM,cAAc,UAAU,QAAQ,UAAU;AAEhD,iBAAO,KAAK,OAAO,aAAa,SAAA,OAAK;AAC7B,gBAAAX,MAAkB,MAAK,OAAO,SAA7B,OAAIA,IAAA,MAAE,UAAOA,IAAA;AACpB,gBAAI,WAAWW;AACb,oBAAM,IAAI,WAAW,gBAAgB,8DAA8D;AACrG,gBAAIA,SAAQA,MAAK,WAAW,QAAQ;AAClC,oBAAM,IAAI,WAAW,gBAAgB,sDAAsD;AAE7F,gBAAM,aAAa,QAAQ;AAC3B,gBAAI,eAAe,WAAW,OAC5B,QAAQ,IAAI,8BAA8B,OAAO,CAAC,IAClD;AAEF,mBAAO,MAAK,KAAK,OACf,EAAC,OAAO,MAAM,OAAO,MAAMA,OAAyB,QAAQ,cAAc,YAAW,CAAC,EAErF,KAAK,SAACX,KAA4C;kBAA3C,cAAWA,IAAA,aAAE,UAAOA,IAAA,SAAE,aAAUA,IAAA,YAAE,WAAQA,IAAA;AAChD,kBAAM,SAAS,cAAc,UAAU;AACvC,kBAAI,gBAAgB;AAAG,uBAAO;AAC9B,oBAAM,IAAI,UACR,GAAA,OAAG,MAAK,MAAI,cAAA,EAAA,OAAe,aAAW,MAAA,EAAA,OAAO,YAAU,oBAAA,GAAsB,QAAQ;aACxF;WACJ;;AAOF,QAAAO,OAAA,UAAA,aAAA,SACC,gBAAiE;AADlE,cAAA,QAAA;AAGC,cAAM,YAAY,KAAK;AACvB,cAAMI,QAAO,eAAe,IAAI,SAAC,OAAK;AAAK,mBAAA,MAAM;UAAG,CAAA;AACpD,cAAM,cAAc,eAAe,IAAI,SAAC,OAAK;AAAK,mBAAA,MAAM;UAAO,CAAA;AAC/D,cAAM,YAAsB,CAAA;AAC5B,iBAAO,KAAK,OAAO,aAAa,SAAC,OAAK;AACpC,mBAAO,UAAU,QAAQ,EAAE,OAAO,MAAIA,OAAE,OAAO,QAAO,CAAE,EAAE,KAAK,SAAC,MAAI;AAClE,kBAAM,aAAoB,CAAA;AAC1B,kBAAM,aAAoB,CAAA;AAC1B,6BAAe,QAAQ,SAACX,KAAkB,KAAG;oBAAnB,MAAGA,IAAA,KAAE,UAAOA,IAAA;AACpC,oBAAM,MAAM,KAAK,GAAG;AACpB,oBAAI,KAAK;AACP,2BAAsB,KAAA,GAAA,KAAA,OAAO,KAAK,OAAO,GAAnB,KAAA,GAAA,QAAA,MAAsB;AAAvC,wBAAM,UAAO,GAAA,EAAA;AAChB,wBAAM,QAAQ,QAAQ,OAAO;AAC7B,wBAAI,YAAY,MAAK,OAAO,QAAQ,SAAS;AAC3C,0BAAIM,KAAI,OAAO,GAAG,MAAM,GAAG;AACzB,8BAAM,IAAI,WAAW,WACnB,2CAA2C;;2BAG1C;AACL,mCAAa,KAAK,SAAS,KAAK;;;AAGpC,4BAAU,KAAK,GAAG;AAClB,6BAAW,KAAK,GAAG;AACnB,6BAAW,KAAK,GAAG;;eAEtB;AACD,kBAAM,aAAa,WAAW;AAC9B,qBAAO,UACJ,OAAO;gBACN;gBACA,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,SAAS;kBACP,MAAIK;kBACJ;;eAEH,EACA,KAAK,SAACX,KAAyB;oBAAvB,cAAWA,IAAA,aAAE,WAAQA,IAAA;AAC5B,oBAAI,gBAAgB;AAAG,yBAAO;AAG9B,yBAAqB,KAAA,GAAA,KAAA,OAAO,KAAK,QAAQ,GAApB,KAAA,GAAA,QAAA,MAAuB;AAAvC,sBAAM,SAAM,GAAA,EAAA;AACf,sBAAM,eAAe,UAAU,OAAO,MAAM,CAAC;AAC7C,sBAAI,gBAAgB,MAAM;AACxB,wBAAM,UAAU,SAAS,MAAM;AAC/B,2BAAO,SAAS,MAAM;AACtB,6BAAS,YAAY,IAAI;;;AAG7B,sBAAM,IAAI,UACR,GAAA,OAAG,MAAK,MAAI,iBAAA,EAAA,OAAkB,aAAW,MAAA,EAAA,OAAO,YAAU,oBAAA,GAC1D,QAAQ;eAEX;aACJ;WACF;;AAQH,QAAAO,OAAA,UAAA,aAAA,SAAWI,OAAkC;AAA7C,cAAA,QAAA;AACE,cAAM,UAAUA,MAAK;AACrB,iBAAO,KAAK,OAAO,aAAa,SAAA,OAAK;AACnC,mBAAO,MAAK,KAAK,OAAO,EAAC,OAAO,MAAM,UAAU,MAAMA,MAAuB,CAAC;WAC/E,EAAE,KAAK,SAACX,KAAmC;gBAAlC,cAAWA,IAAA,aAAE,aAAUA,IAAA,YAAE,WAAQA,IAAA;AACzC,gBAAI,gBAAgB;AAAG,qBAAO;AAC9B,kBAAM,IAAI,UACR,GAAA,OAAG,MAAK,MAAI,iBAAA,EAAA,OAAkB,aAAW,MAAA,EAAA,OAAO,SAAO,oBAAA,GAAsB,QAAQ;WACxF;;AAEL,eAAAO;MAAA,EAAC;eC7kBuB,OAAO,KAAG;AAC9B,YAAI,MAAM,CAAA;AACV,YAAI,KAAK,SAAU,WAAW,YAAU;AACpC,cAAI,YAAY;AAEZ,gBAAIK,KAAI,UAAU,QAAQ,OAAO,IAAI,MAAMA,KAAI,CAAC;AAChD,mBAAO,EAAEA;AAAG,mBAAKA,KAAI,CAAC,IAAI,UAAUA,EAAC;AACrC,gBAAI,SAAS,EAAE,UAAU,MAAM,MAAM,IAAI;AACzC,mBAAO;qBACA,OAAQ,cAAe,UAAU;AAExC,mBAAO,IAAI,SAAS;;;AAG5B,WAAG,eAAeC;AAElB,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,UAAAA,KAAI,UAAU,CAAC,CAAC;;AAGpB,eAAO;AAEP,iBAASA,KAAI,WAAW,eAAe,iBAAe;AAClD,cAAI,OAAO,cAAc;AAAU,mBAAO,oBAAoB,SAAS;AACvE,cAAI,CAAC;AAAe,4BAAgB;AACpC,cAAI,CAAC;AAAiB,8BAAkB;AAExC,cAAI,UAAU;YACV,aAAa,CAAA;YACb,MAAM;YACN,WAAW,SAAU,IAAE;AACnB,kBAAI,QAAQ,YAAY,QAAQ,EAAE,MAAM,IAAI;AACxC,wBAAQ,YAAY,KAAK,EAAE;AAC3B,wBAAQ,OAAO,cAAc,QAAQ,MAAM,EAAE;;;YAGrD,aAAa,SAAU,IAAE;AACrB,sBAAQ,cAAc,QAAQ,YAAY,OAAO,SAAU,IAAE;AAAI,uBAAO,OAAO;cAAG,CAAE;AACpF,sBAAQ,OAAO,QAAQ,YAAY,OAAO,eAAe,eAAe;;;AAGhF,cAAI,SAAS,IAAI,GAAG,SAAS,IAAI;AACjC,iBAAO;;AAGX,iBAAS,oBAAoB,KAAG;AAE5B,eAAK,GAAG,EAAE,QAAQ,SAAU,WAAS;AACjC,gBAAI,OAAO,IAAI,SAAS;AACxB,gBAAI,QAAQ,IAAI,GAAG;AACf,cAAAA,KAAI,WAAW,IAAI,SAAS,EAAE,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC,CAAC;uBAC5C,SAAS,QAAQ;AAGxB,kBAAI,UAAUA,KAAI,WAAW,QAAQ,SAAS,OAAI;AAE9C,oBAAID,KAAI,UAAU,QAAQE,QAAO,IAAI,MAAMF,EAAC;AAC5C,uBAAOA;AAAK,kBAAAE,MAAKF,EAAC,IAAI,UAAUA,EAAC;AAEjC,wBAAQ,YAAY,QAAQ,SAAU,IAAE;AACpCd,yBAAK,SAAS,YAAS;AACnB,uBAAG,MAAM,MAAMgB,KAAI;mBACtB;iBACJ;eACJ;;AACE,oBAAM,IAAI,WAAW,gBAAgB,sBAAsB;WACrE;;MAET;eCrEgB,qBAAoC,WAAmB,aAAqB;AAiB1F,eAAO,WAAW,EAAE,KAAK,EAAC,UAAS,CAAC;AACpC,eAAO;MACT;eCFgB,uBAAwB,IAAS;AAC/C,eAAO,qBACL,MAAM,WAEN,SAASP,OAAoB,MAAc,aAA0B,OAAmB;AACtF,eAAK,KAAK;AACV,eAAK,MAAM;AACX,eAAK,OAAO;AACZ,eAAK,SAAS;AACd,eAAK,OAAO,GAAG,WAAW,IAAI,IAAI,GAAG,WAAW,IAAI,EAAE,OAAO,OAAO,MAAM;YACxE,YAAY,CAAC,mBAAmB,GAAG;YACnC,WAAW,CAAC,mBAAmB,MAAM;YACrC,YAAY,CAAC,mBAAmB,GAAG;YACnC,YAAY,CAAC,mBAAmB,GAAG;WACpC;SACF;MAGL;eC5BgB,gBAAiB,KAAwB,mBAA2B;AAClF,eAAO,EAAE,IAAI,UAAU,IAAI,aAAa,IAAI,QACvC,oBAAoB,IAAI,YAAY,CAAC,IAAI;MAChD;eAEgB,UAAU,KAAwB,IAAY;AAC5D,YAAI,SAAS,QAAQ,IAAI,QAAQ,EAAE;MACrC;eAEgB,gBAAiB,KAAwB,SAAS,eAAc;AAC9E,YAAI,OAAO,IAAI;AACf,YAAI,eAAe,OAAO,WAAA;AAAI,iBAAA,QAAQ,KAAI,GAAI,QAAO,CAAE;QAAC,IAAG;AAC3D,YAAI,YAAY,iBAAiB,CAAC;MACpC;eAEgB,eAAe,KAAwB,IAAE;AACvD,YAAI,UAAU,QAAQ,IAAI,SAAS,EAAE;MACvC;eAEgB,gBAAgB,KAAwB,YAA6B;AAGnF,YAAI,IAAI;AAAW,iBAAO,WAAW;AACrC,YAAM,QAAQ,WAAW,kBAAkB,IAAI,KAAK;AACpD,YAAI,CAAC;AAAO,gBAAM,IAAI,WAAW,OAAO,aAAa,IAAI,QAAQ,sBAAsB,WAAW,OAAO,iBAAiB;AAC1H,eAAO;MACT;eAEgB,WAAW,KAAwB,WAAwB,OAAwB;AACjG,YAAM,QAAQ,gBAAgB,KAAK,UAAU,MAAM;AACnD,eAAO,UAAU,WAAW;UAC1B;UACA,QAAQ,CAAC,IAAI;UACb,SAAS,IAAI,QAAQ;UACrB,QAAQ,CAAC,CAAC,IAAI;UACd,OAAO;YACL;YACA,OAAO,IAAI;;SAEd;MACH;eAEgB,KACd,KACA,IACA,WACA,WAAsB;AAEtB,YAAM,SAAS,IAAI,eAAe,QAAQ,IAAI,QAAQ,IAAI,aAAY,CAAE,IAAI,IAAI;AAChF,YAAI,CAAC,IAAI,IAAI;AACT,iBAAO,QACL,WAAW,KAAK,WAAW,SAAS,GACpC,QAAQ,IAAI,WAAW,MAAM,GAAG,IAAI,CAAC,IAAI,YAAY,IAAI,WAAW;eACnE;AACH,cAAM,QAAM,CAAA;AAEZ,cAAM,QAAQ,SAAC,MAAW,QAAsB,SAAO;AACnD,gBAAI,CAAC,UAAU,OAAO,QAAQ,SAAS,SAAA,QAAM;AAAE,qBAAA,OAAO,KAAK,MAAM;YAAC,GAAE,SAAA,KAAG;AAAI,qBAAA,OAAO,KAAK,GAAG;YAAC,CAAA,GAAG;AAC1F,kBAAI,aAAa,OAAO;AACxB,kBAAI,MAAM,KAAK;AACf,kBAAI,QAAQ;AAAwB,sBAAM,KAAK,IAAI,WAAW,UAAU;AACxE,kBAAI,CAAC,OAAO,OAAK,GAAG,GAAG;AACnB,sBAAI,GAAG,IAAI;AACX,mBAAG,MAAM,QAAQ,OAAO;;;;AAKpC,iBAAO,QAAQ,IAAI;YACjB,IAAI,GAAG,SAAS,OAAO,SAAS;YAChC,QAAQ,WAAW,KAAK,WAAW,SAAS,GAAG,IAAI,WAAW,OAAO,CAAC,IAAI,YAAY,IAAI,WAAW;WACtG;;MAEP;AAEA,eAAS,QAAQ,eAAsC,QAAQ,IAAI,aAAW;AAG5E,YAAI,WAAW,cAAc,SAAC,GAAE,GAAE,GAAC;AAAK,iBAAA,GAAG,YAAY,CAAC,GAAE,GAAE,CAAC;QAAC,IAAG;AAEjE,YAAI,YAAY,KAAK,QAAQ;AAE7B,eAAO,cAAc,KAAK,SAAA,QAAM;AAC9B,cAAI,QAAQ;AACV,mBAAO,OAAO,MAAM,WAAA;AAClB,kBAAI,IAAI,WAAA;AAAI,uBAAA,OAAO,SAAQ;cAAE;AAC7B,kBAAI,CAAC,UAAU,OAAO,QAAQ,SAAA,UAAQ;AAAI,uBAAA,IAAI;cAAQ,GAAE,SAAA,KAAG;AAAG,uBAAO,KAAK,GAAG;AAAE,oBAAE;cAAG,GAAG,SAAA,GAAC;AAAK,uBAAO,KAAK,CAAC;AAAE,oBAAI;cAAI,CAAC;AACnH,0BAAU,OAAO,OAAO,QAAQ,SAAA,UAAQ;AAAI,yBAAA,IAAI;gBAAQ,CAAA;AAC1D,gBAAC;aACF;;SAEJ;MACH;AC7EA,UAAAQ,oBAAA,WAAA;AAkDE,iBAAAA,kBAAY,MAAiB;AAC3B,eAAK,WAAW,IAAI;;AAjDtB,QAAAA,kBAAA,UAAA,UAAA,SAAQ,OAAU;;AAChB,cAAM,OAAO,KAAK,WAAW;AAE7B,cAAI,KAAK,QAAQ,QAAW;AAC1B,gBAAM,OAAO,KAAK;AAElB,gBAAI,QAAQ,IAAI,GAAG;AACjB,qBAAO,cAAA,cAAA,CAAA,GAAK,QAAQ,KAAK,IAAI,QAAQ,CAAA,GAAE,IAAA,GAAM,MAAI,IAAA,EAAE,KAAI;;AAGzD,gBAAI,OAAO,SAAS;AAAU,sBAAQ,OAAO,KAAK,KAAK,KAAK;AAC5D,gBAAI,OAAO,SAAS,UAAU;AAC5B,kBAAI;AACF,uBAAO,OAAO,KAAK,IAAI;uBACvB,IAAM;AACN,uBAAO,OAAO,CAAC,IAAI;;;AAGvB,kBAAM,IAAI,UAAU,gBAAA,OAAgB,IAAI,CAAE;;AAI5C,cAAI,KAAK,WAAW,QAAW;AAC7B,gBAAM,eAAa,KAAK;AAExB,gBAAI,QAAQ,YAAU,GAAG;AACvB,qBAAO,QAAQ,KAAK,IAAI,MAAM,OAAO,SAAA,MAAI;AAAI,uBAAA,CAAC,aAAW,SAAS,IAAI;cAAC,CAAA,EAAE,KAAI,IAAK,CAAA;;AAGpF,gBAAI,OAAO,iBAAe;AAAU,qBAAO,OAAO,KAAK,IAAI;AAC3D,gBAAI,OAAO,iBAAe,UAAU;AAClC,kBAAI;AACF,uBAAO,OAAO,KAAK,IAAI;uBACvB,IAAM;AACN,uBAAO,OAAO,CAAC,IAAI;;;AAGvB,kBAAM,IAAI,UAAU,sBAAA,OAAsB,YAAU,CAAE;;AAIxD,cAAM,mBAAkBf,MAAA,KAAK,mBAAa,QAAAA,QAAA,SAAA,SAAAA,IAAG,CAAC;AAC9C,cAAI,mBAAmB,OAAO,UAAU,YAAY,MAAM,WAAW,eAAe,GAAG;AACrF,mBAAO,KAAK,cAAc,CAAC,IAAI,MAAM,UAAU,gBAAgB,MAAM;;AAEvE,iBAAO;;AAMX,eAAAe;MAAA,EAAC;ACzDD,UAAA,aAAA,WAAA;AAAA,iBAAAC,cAAA;;AAwBE,QAAAA,YAAA,UAAA,QAAA,SAAS,IAAwE,IAAG;AAClF,cAAI,MAAM,KAAK;AACf,iBAAO,IAAI,QACT,IAAI,MAAM,OAAO,MAAM,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,IACtD,IAAI,MAAM,OAAO,YAAY,EAAE,EAAE,KAAK,EAAE;;AAG5C,QAAAA,YAAA,UAAA,SAAA,SAAU,IAAsE;AAC9E,cAAI,MAAM,KAAK;AACf,iBAAO,IAAI,QACT,IAAI,MAAM,OAAO,MAAM,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,IACtD,IAAI,MAAM,OAAO,aAAa,IAAI,QAAQ;;AAG9C,QAAAA,YAAA,UAAA,gBAAA,SAAc,IAAE;AACd,cAAI,MAAM,KAAK;AACf,cAAI,YAAY,QAAQ,IAAI,WAAW,EAAE;;AAG3C,QAAAA,YAAA,UAAA,WAAA,SACE,IACA,WAA4B;AAE5B,iBAAO,KAAK,KAAK,MAAM,IAAI,WAAW,KAAK,KAAK,MAAM,IAAI;;AAQ5D,QAAAA,YAAA,UAAA,QAAA,SAAMZ,QAAM;AACV,cAAI,KAAK,OAAO,OAAO,KAAK,YAAY,SAAS,GAC/C,MAAM,OAAO,OAAO,KAAK,IAAI;AAC/B,cAAIA;AAAO,mBAAO,KAAKA,MAAK;AAC5B,aAAG,OAAO;AACV,iBAAO;;AAQT,QAAAY,YAAA,UAAA,MAAA,WAAA;AACE,eAAK,KAAK,cAAc;AACxB,iBAAO;;AAQT,QAAAA,YAAA,UAAA,OAAA,SAAK,IAAsC;AACzC,cAAI,MAAM,KAAK;AAEf,iBAAO,KAAK,MAAM,SAAA,OAAK;AAAI,mBAAA,KAAK,KAAK,IAAI,OAAO,IAAI,MAAM,IAAI;UAAC,CAAA;;AAQjE,QAAAA,YAAA,UAAA,QAAA,SAAM,IAAG;AAAT,cAAA,QAAA;AACE,iBAAO,KAAK,MAAM,SAAA,OAAK;AACrB,gBAAM,MAAM,MAAK;AACjB,gBAAM,YAAY,IAAI,MAAM;AAC5B,gBAAI,gBAAgB,KAAK,IAAI,GAAG;AAE9B,qBAAO,UAAU,MAAM;gBACrB;gBACA,OAAO;kBACL,OAAO,gBAAgB,KAAK,UAAU,MAAM;kBAC5C,OAAO,IAAI;;eAEd,EAAE,KAAK,SAAAC,QAAK;AAAI,uBAAA,KAAK,IAAIA,QAAO,IAAI,KAAK;cAAC,CAAA;mBACtC;AAEL,kBAAI,QAAQ;AACZ,qBAAO,KAAK,KAAK,WAAA;AAAQ,kBAAE;AAAO,uBAAO;cAAM,GAAI,OAAO,SAAS,EAClE,KAAK,WAAA;AAAI,uBAAA;cAAK,CAAA;;WAElB,EAAE,KAAK,EAAE;;AAUZ,QAAAD,YAAA,UAAA,SAAA,SAAO,SAAiB,IAA6B;AACnD,cAAM,QAAQ,QAAQ,MAAM,GAAG,EAAE,QAAO,GACtC,WAAW,MAAM,CAAC,GAClB,YAAY,MAAM,SAAS;AAC7B,mBAAS,OAAO,KAAK,GAAC;AACpB,gBAAI;AAAG,qBAAO,OAAO,IAAI,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;AACzC,mBAAO,IAAI,QAAQ;;AAErB,cAAI,QAAQ,KAAK,KAAK,QAAQ,SAAS,IAAI;AAE3C,mBAAS,OAAO,GAAG,GAAC;AAClB,gBAAI,OAAO,OAAO,GAAG,SAAS,GAC5B,OAAO,OAAO,GAAG,SAAS;AAC5B,mBAAOV,KAAI,MAAM,IAAI,IAAI;;AAE3B,iBAAO,KAAK,QAAQ,SAAU,GAAC;AAC7B,mBAAO,EAAE,KAAK,MAAM;WACrB,EAAE,KAAK,EAAE;;AAQZ,QAAAU,YAAA,UAAA,UAAA,SAAQ,IAAG;AAAX,cAAA,QAAA;AACE,iBAAO,KAAK,MAAM,SAAA,OAAK;AACrB,gBAAI,MAAM,MAAK;AACf,gBAAI,IAAI,QAAQ,UAAU,gBAAgB,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG;AAG9D,kBAAA,gBAAe,IAAG;AACzB,kBAAM,QAAQ,gBAAgB,KAAK,IAAI,MAAM,KAAK,MAAM;AACxD,qBAAO,IAAI,MAAM,KAAK,MAAM;gBAC1B;gBACA,OAAO,IAAI;gBACX,QAAQ;gBACR,OAAO;kBACL;kBACA,OAAO,IAAI;;eAEd,EAAE,KAAK,SAAChB,KAAQ;oBAAP,SAAMA,IAAA;AAAM,uBAAA,gBAAc,OAAO,IAAI,aAAW,IAAI;eAAM;mBAC/D;AAEL,kBAAM,MAAI,CAAA;AACV,qBAAO,KAAK,KAAK,SAAA,MAAI;AAAI,uBAAA,IAAE,KAAK,IAAI;cAAC,GAAE,OAAO,IAAI,MAAM,IAAI,EAAE,KAAK,WAAA;AAAI,uBAAA;cAAC,CAAA;;aAEzE,EAAE;;AAQP,QAAAgB,YAAA,UAAA,SAAA,SAAO,QAAc;AACnB,cAAI,MAAM,KAAK;AACf,cAAI,UAAU;AAAG,mBAAO;AACxB,cAAI,UAAU;AACd,cAAI,gBAAgB,GAAG,GAAG;AACxB,4BAAgB,KAAK,WAAA;AACnB,kBAAI,aAAa;AACjB,qBAAO,SAAC,QAAQ,SAAO;AACrB,oBAAI,eAAe;AAAG,yBAAO;AAC7B,oBAAI,eAAe,GAAG;AAAE,oBAAE;AAAY,yBAAO;;AAC7C,wBAAQ,WAAA;AACN,yBAAO,QAAQ,UAAU;AACzB,+BAAa;iBACd;AACD,uBAAO;;aAEV;iBACI;AACL,4BAAgB,KAAK,WAAA;AACnB,kBAAI,aAAa;AACjB,qBAAO,WAAA;AAAM,uBAAC,EAAE,aAAa;cAAC;aAC/B;;AAEH,iBAAO;;AAQT,QAAAA,YAAA,UAAA,QAAA,SAAM,SAAe;AACnB,eAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,KAAK,OAAO,OAAO;AACnD,0BAAgB,KAAK,MAAM,WAAA;AACzB,gBAAI,WAAW;AACf,mBAAO,SAAU,QAAQ,SAAS,SAAO;AACvC,kBAAI,EAAE,YAAY;AAAG,wBAAQ,OAAO;AACpC,qBAAO,YAAY;;aAEpB,IAAI;AACP,iBAAO;;AAQT,QAAAA,YAAA,UAAA,QAAA,SAAM,gBAAgC,mBAAkB;AACtD,oBAAU,KAAK,MAAM,SAAU,QAAQ,SAAS,SAAO;AACrD,gBAAI,eAAe,OAAO,KAAK,GAAG;AAChC,sBAAQ,OAAO;AACf,qBAAO;mBACF;AACL,qBAAO;;WAEV;AACD,iBAAO;;AAQT,QAAAA,YAAA,UAAA,QAAA,SAAM,IAAG;AACP,iBAAO,KAAK,MAAM,CAAC,EAAE,QAAQ,SAAU,GAAC;AAAI,mBAAO,EAAE,CAAC;UAAE,CAAE,EAAE,KAAK,EAAE;;AAQrE,QAAAA,YAAA,UAAA,OAAA,SAAK,IAAG;AACN,iBAAO,KAAK,QAAO,EAAG,MAAM,EAAE;;AAQhC,QAAAA,YAAA,UAAA,SAAA,SAAO,gBAA8B;AAEnC,oBAAU,KAAK,MAAM,SAAU,QAAM;AACnC,mBAAO,eAAe,OAAO,KAAK;WACnC;AAGD,yBAAe,KAAK,MAAM,cAAc;AACxC,iBAAO;;AAQT,QAAAA,YAAA,UAAA,MAAA,SAAI,QAAsB;AACxB,iBAAO,KAAK,OAAO,MAAM;;AAQ3B,QAAAA,YAAA,UAAA,KAAA,SAAG,WAAiB;AAClB,iBAAO,IAAI,KAAK,GAAG,YAAY,KAAK,KAAK,OAAO,WAAW,IAAI;;AAQjE,QAAAA,YAAA,UAAA,UAAA,WAAA;AACE,eAAK,KAAK,MAAO,KAAK,KAAK,QAAQ,SAAS,SAAS;AACrD,cAAI,KAAK;AAAoB,iBAAK,mBAAmB,KAAK,KAAK,GAAG;AAClE,iBAAO;;AAQT,QAAAA,YAAA,UAAA,OAAA,WAAA;AACE,iBAAO,KAAK,QAAO;;AAQrB,QAAAA,YAAA,UAAA,UAAA,SAAQ,IAAG;AACT,cAAI,MAAM,KAAK;AACf,cAAI,WAAW,CAAC,IAAI;AACpB,iBAAO,KAAK,KAAK,SAAU,KAAK,QAAM;AAAI,eAAG,OAAO,KAAK,MAAM;UAAE,CAAE;;AAQrE,QAAAA,YAAA,UAAA,gBAAA,SAAc,IAAG;AACf,eAAK,KAAK,SAAS;AACnB,iBAAO,KAAK,QAAQ,EAAE;;AAQxB,QAAAA,YAAA,UAAA,iBAAA,SAAe,IAAG;AAChB,cAAI,MAAM,KAAK;AACf,cAAI,WAAW,CAAC,IAAI;AACpB,iBAAO,KAAK,KAAK,SAAU,KAAK,QAAM;AAAI,eAAG,OAAO,YAAY,MAAM;UAAE,CAAE;;AAQ5E,QAAAA,YAAA,UAAA,OAAA,SAAK,IAAG;AACN,cAAI,MAAM,KAAK;AACf,cAAI,WAAW,CAAC,IAAI;AACpB,cAAI,IAAI,CAAA;AACR,iBAAO,KAAK,KAAK,SAAU,MAAM,QAAM;AACrC,cAAE,KAAK,OAAO,GAAG;WAClB,EAAE,KAAK,WAAA;AACN,mBAAO;WACR,EAAE,KAAK,EAAE;;AAQZ,QAAAA,YAAA,UAAA,cAAA,SAAY,IAAG;AACb,cAAI,MAAM,KAAK;AACf,cAAI,IAAI,QAAQ,UAAU,gBAAgB,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG;AAGrE,mBAAO,KAAK,MAAM,SAAA,OAAK;AACrB,kBAAI,QAAQ,gBAAgB,KAAK,IAAI,MAAM,KAAK,MAAM;AACtD,qBAAO,IAAI,MAAM,KAAK,MAAM;gBAC1B;gBACA,QAAQ;gBACR,OAAO,IAAI;gBACX,OAAO;kBACL;kBACA,OAAO,IAAI;;eACX;aACL,EAAE,KAAK,SAAChB,KAAQ;kBAAP,SAAMA,IAAA;AAAI,qBAAA;aAAM,EAAE,KAAK,EAAE;;AAErC,cAAI,WAAW,CAAC,IAAI;AACpB,cAAI,IAAI,CAAA;AACR,iBAAO,KAAK,KAAK,SAAU,MAAM,QAAM;AACrC,cAAE,KAAK,OAAO,UAAU;WACzB,EAAE,KAAK,WAAA;AACN,mBAAO;WACR,EAAE,KAAK,EAAE;;AAQZ,QAAAgB,YAAA,UAAA,aAAA,SAAW,IAAG;AACZ,eAAK,KAAK,SAAS;AACnB,iBAAO,KAAK,KAAK,EAAE;;AAQrB,QAAAA,YAAA,UAAA,WAAA,SAAS,IAAG;AACV,iBAAO,KAAK,MAAM,CAAC,EAAE,KAAK,SAAU,GAAC;AAAI,mBAAO,EAAE,CAAC;UAAE,CAAE,EAAE,KAAK,EAAE;;AAQlE,QAAAA,YAAA,UAAA,UAAA,SAAQ,IAAG;AACT,iBAAO,KAAK,QAAO,EAAG,SAAS,EAAE;;AAQnC,QAAAA,YAAA,UAAA,WAAA,WAAA;AACE,cAAI,MAAM,KAAK,MACb,MAAM,IAAI,SAAS,IAAI,MAAM,OAAO,UAAU,IAAI,KAAK;AACzD,cAAI,CAAC,OAAO,CAAC,IAAI;AAAO,mBAAO;AAC/B,cAAI,MAAM,CAAA;AACV,oBAAU,KAAK,MAAM,SAAU,QAAoB;AACjD,gBAAI,SAAS,OAAO,WAAW,SAAQ;AACvC,gBAAI,QAAQ,OAAO,KAAK,MAAM;AAC9B,gBAAI,MAAM,IAAI;AACd,mBAAO,CAAC;WACT;AACD,iBAAO;;AAYT,QAAAA,YAAA,UAAA,SAAA,SAAO,SAAmG;AAA1G,cAAA,QAAA;AACE,cAAI,MAAM,KAAK;AACf,iBAAO,KAAK,OAAO,SAAA,OAAK;AACtB,gBAAI;AACJ,gBAAI,OAAO,YAAY,YAAY;AAEjC,yBAAW;mBACN;AAEL,kBAAI,WAAW,KAAK,OAAO;AAC3B,kBAAI,UAAU,SAAS;AACvB,yBAAW,SAAU,MAAI;AACvB,oBAAI,mBAAmB;AACvB,yBAAS,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAChC,sBAAI,UAAU,SAAS,CAAC;AACxB,sBAAI,MAAM,QAAQ,OAAO;AACzB,sBAAI,UAAU,aAAa,MAAM,OAAO;AAExC,sBAAI,eAAeD,mBAAkB;AACnC,iCAAa,MAAM,SAAS,IAAI,QAAQ,OAAO,CAAC;AAChD,uCAAmB;6BACV,YAAY,KAAK;AAC1B,iCAAa,MAAM,SAAS,GAAG;AAC/B,uCAAmB;;;AAGvB,uBAAO;;;AAIX,gBAAM,YAAY,IAAI,MAAM;AACtB,gBAAAf,MAAyB,UAAU,OAAO,YAAzC,WAAQA,IAAA,UAAE,aAAUA,IAAA;AAC3B,gBAAI,QAAQ;AACZ,gBAAM,kBAAkB,MAAK,GAAG,SAAS;AACzC,gBAAI,iBAAiB;AACnB,kBAAI,OAAO,mBAAmB,UAAU;AACtC,wBAAQ,gBAAgB,UAAU,IAAI,KAAK,gBAAgB,GAAG,KAAK;qBAC9D;AACL,wBAAQ;;;AAGZ,gBAAM,gBAAgB,CAAA;AACtB,gBAAI,eAAe;AACnB,gBAAM,aAA8B,CAAA;AACpC,gBAAM,oBAAoB,SAAC,eAAuB,KAAyB;AAClE,kBAAA,WAAyB,IAAG,UAAlB,cAAe,IAAG;AACnC,8BAAgB,gBAAgB;AAChC,uBAAgB,KAAA,GAAAA,MAAA,KAAK,QAAQ,GAAb,KAAAA,IAAA,QAAA,MAAgB;AAA3B,oBAAI,MAAGA,IAAA,EAAA;AACV,8BAAc,KAAK,SAAS,GAAG,CAAC;;;AAGpC,mBAAO,MAAK,MAAK,EAAG,YAAW,EAAG,KAAK,SAAAW,OAAI;AACzC,kBAAM,WAAW,gBAAgB,GAAG,KAClC,IAAI,UAAU,aACb,OAAO,YAAY,cAAc,YAAY,mBAAmB;gBAC/D,OAAO,IAAI;gBACX,OAAO,IAAI;;AAGf,kBAAM,YAAY,SAAC,QAAc;AAC/B,oBAAM,QAAQ,KAAK,IAAI,OAAOA,MAAK,SAAS,MAAM;AAClD,uBAAO,UAAU,QAAQ;kBACvB;kBACA,MAAMA,MAAK,MAAM,QAAQ,SAAS,KAAK;kBACvC,OAAO;iBAIR,EAAE,KAAK,SAAA,QAAM;AACZ,sBAAM,YAAY,CAAA;AAClB,sBAAM,YAAY,CAAA;AAClB,sBAAM,UAAU,WAAW,CAAA,IAAK;AAChC,sBAAM,aAAa,CAAA;AACnB,2BAAS,IAAE,GAAG,IAAE,OAAO,EAAE,GAAG;AAC1B,wBAAM,YAAY,OAAO,CAAC;AAC1B,wBAAM,QAAM;sBACV,OAAO,UAAU,SAAS;sBAC1B,SAASA,MAAK,SAAO,CAAC;;AAExB,wBAAI,SAAS,KAAK,OAAK,MAAI,OAAO,KAAG,MAAM,OAAO;AAChD,0BAAI,MAAI,SAAS,MAAM;AAErB,mCAAW,KAAKA,MAAK,SAAO,CAAC,CAAC;iCACrB,CAAC,YAAYL,KAAI,WAAW,SAAS,GAAG,WAAW,MAAI,KAAK,CAAC,MAAM,GAAG;AAE/E,mCAAW,KAAKK,MAAK,SAAO,CAAC,CAAC;AAC9B,kCAAU,KAAK,MAAI,KAAK;6BACnB;AAEL,kCAAU,KAAK,MAAI,KAAK;AACxB,4BAAI;AAAU,kCAAQ,KAAKA,MAAK,SAAO,CAAC,CAAC;;;;AAK/C,yBAAO,QAAQ,QAAQ,UAAU,SAAS,KACxC,UAAU,OAAO,EAAC,OAAO,MAAM,OAAO,QAAQ,UAAS,CAAC,EACrD,KAAK,SAAA,KAAG;AACP,6BAAS,OAAO,IAAI,UAAU;AAE5B,iCAAW,OAAO,SAAS,GAAG,GAAG,CAAC;;AAEpC,sCAAkB,UAAU,QAAQ,GAAG;mBACxC,CAAC,EACJ,KAAK,WAAA;AAAI,4BAAC,UAAU,SAAS,KAAM,YAAY,OAAO,YAAY,aAChE,UAAU,OAAO;sBACf;sBACA,MAAM;sBACN,MAAM;sBACN,QAAQ;sBACR;sBACA,YAAY,OAAO,YAAY,cAC1B;sBACL,mBAAmB,SAAS;qBAC7B,EAAE,KAAK,SAAA,KAAG;AAAE,6BAAA,kBAAkB,UAAU,QAAQ,GAAG;oBAAC,CAAA;kBAAC,CAAA,EACxD,KAAK,WAAA;AAAI,4BAAC,WAAW,SAAS,KAAM,YAAY,YAAY,mBAC1D,UAAU,OAAO;sBACf;sBACA,MAAM;sBACN,MAAM;sBACN;sBACA,mBAAmB,SAAS;qBAC7B,EAAE,KAAK,SAAA,KAAG;AAAE,6BAAA,kBAAkB,WAAW,QAAQ,GAAG;oBAAC,CAAA;kBAAC,CAAA,EACzD,KAAK,WAAA;AACL,2BAAOA,MAAK,SAAS,SAAS,SAAS,UAAU,SAAS,KAAK;mBAChE;iBACF;;AAGH,qBAAO,UAAU,CAAC,EAAE,KAAK,WAAA;AACvB,oBAAI,cAAc,SAAS;AACzB,wBAAM,IAAI,YAAY,uCAAuC,eAAe,cAAc,UAAwC;AAEpI,uBAAOA,MAAK;eACb;aACF;WAEF;;AAQH,QAAAK,YAAA,UAAA,SAAA,WAAA;AACE,cAAI,MAAM,KAAK,MACb,QAAQ,IAAI;AAGd,cAAI,gBAAgB,GAAG,MACpB,IAAI,aAAa,MAAM,SAAI,IAC9B;AAKE,mBAAO,KAAK,OAAO,SAAA,OAAK;AAEf,kBAAA,aAAc,IAAI,MAAM,KAAK,OAAM;AAC1C,kBAAM,YAAY;AAClB,qBAAO,IAAI,MAAM,KAAK,MAAM,EAAC,OAAO,OAAO,EAAC,OAAO,YAAY,OAAO,UAAS,EAAC,CAAC,EAAE,KAAK,SAAA,OAAK;AAC3F,uBAAO,IAAI,MAAM,KAAK,OAAO,EAAC,OAAO,MAAM,eAAe,OAAO,UAAS,CAAC,EAC1E,KAAK,SAAChB,KAA4C;sBAA3C,WAAQA,IAAA;AAAA,kBAAAA,IAAA;AAAY,kBAAAA,IAAA;AAAS,sBAAE,cAAWA,IAAA;AAChD,sBAAI;AAAa,0BAAM,IAAI,YAAY,gCACrC,OAAO,KAAK,QAAQ,EAAE,IAAI,SAAA,KAAG;AAAI,6BAAA,SAAS,GAAG;oBAAC,CAAA,GAC9C,QAAQ,WAAW;AACrB,yBAAO,QAAQ;iBAChB;eACF;aACF;;AAGH,iBAAO,KAAK,OAAO,cAAc;;AAErC,eAAAgB;MAAA,EAAC;AAED,UAAM,iBAAiB,SAAC,OAAO,KAAG;AAAK,eAAA,IAAI,QAAQ;MAAI;eC1mBvC,4BAA4B,IAAS;AACnD,eAAO,qBACL,WAAW,WAEX,SAASA,YAEP,aACA,mBAAwC;AAExC,eAAK,KAAK;AACV,cAAI,WAAW,UAAU,QAAQ;AACjC,cAAI;AAAmB,gBAAI;AACzB,yBAAW,kBAAiB;qBACrB,IAAI;AACX,sBAAQ;;AAGV,cAAM,WAAW,YAAY;AAC7B,cAAM,QAAQ,SAAS;AACvB,cAAM,cAAc,MAAM,KAAK,QAAQ;AACvC,eAAK,OAAO;YACV;YACA,OAAO,SAAS;YAChB,WAAY,CAAC,SAAS,SAAU,MAAM,OAAO,QAAQ,WAAW,SAAS,UAAU,MAAM,OAAO,QAAQ;YACxG,OAAO;YACP,UAAU;YACV,KAAK;YACL,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,cAAc;YACd,WAAW;YACX,SAAS;YACT,QAAQ;YACR,OAAO;YACP;YACA,IAAI,SAAS;YACb,aAAa,gBAAgB,SAAS,cAAc;;SAEvD;MAEL;eC3DgB,cAAc,GAAG,GAAC;AAChC,eAAO,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI;MACpC;eAEgB,qBAAqB,GAAG,GAAC;AACvC,eAAO,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI;MACpC;eCDgB,KAAK,yBAAmD,KAAK,GAAE;AAC7E,YAAI,aAAa,mCAAmC,cAChD,IAAI,wBAAwB,WAAY,uBAAuB,IAC/D;AAEJ,mBAAW,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG,IAAI,IAAI,UAAU,GAAG;AAC1D,eAAO;MACT;eAEgB,gBAAgB,aAAwB;AACtD,eAAO,IAAI,YAAY,WAAY,aAAa,WAAA;AAAM,iBAAA,WAAW,EAAE;QAAC,CAAA,EAAE,MAAM,CAAC;MAC/E;eAEgB,aAAa,KAAoB;AAC/C,eAAO,QAAQ,SACb,SAAC,GAAS;AAAK,iBAAA,EAAE,YAAW;QAAE,IAC9B,SAAC,GAAS;AAAK,iBAAA,EAAE,YAAW;QAAE;MAClC;eAEgB,aAAa,KAAoB;AAC/C,eAAO,QAAQ,SACb,SAAC,GAAS;AAAK,iBAAA,EAAE,YAAW;QAAE,IAC9B,SAAC,GAAS;AAAK,iBAAA,EAAE,YAAW;QAAE;MAClC;eAEgB,WAAW,KAAK,UAAU,aAAa,aAAaV,MAAK,KAAG;AAC1E,YAAI,SAAS,KAAK,IAAI,IAAI,QAAQ,YAAY,MAAM;AACpD,YAAI,MAAM;AACV,iBAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC7B,cAAI,aAAa,SAAS,CAAC;AAC3B,cAAI,eAAe,YAAY,CAAC,GAAG;AAC/B,gBAAIA,KAAI,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC,IAAI;AAAG,qBAAO,IAAI,OAAO,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,YAAY,OAAO,IAAI,CAAC;AACxG,gBAAIA,KAAI,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC,IAAI;AAAG,qBAAO,IAAI,OAAO,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,YAAY,OAAO,IAAI,CAAC;AACxG,gBAAI,OAAO;AAAG,qBAAO,IAAI,OAAO,GAAG,GAAG,IAAI,SAAS,GAAG,IAAI,YAAY,OAAO,MAAM,CAAC;AACpF,mBAAO;;AAEX,cAAIA,KAAI,IAAI,CAAC,GAAG,UAAU,IAAI;AAAG,kBAAM;;AAE3C,YAAI,SAAS,YAAY,UAAU,QAAQ;AAAQ,iBAAO,MAAM,YAAY,OAAO,IAAI,MAAM;AAC7F,YAAI,SAAS,IAAI,UAAU,QAAQ;AAAQ,iBAAO,IAAI,OAAO,GAAG,YAAY,MAAM;AAClF,eAAQ,MAAM,IAAI,OAAO,IAAI,OAAO,GAAG,GAAG,IAAI,YAAY,GAAG,IAAI,YAAY,OAAO,MAAM,CAAC;MAC7F;eAEgB,uBAAuB,aAA0B,OAAO,SAAS,QAAM;AAErF,YAAI,OAAO,OAAO,SAAS,cAAc,cAAc,WAAW,eAC9D,aAAa,QAAQ;AACzB,YAAI,CAAC,QAAQ,MAAM,SAAA,GAAC;AAAI,iBAAA,OAAO,MAAM;QAAQ,CAAA,GAAG;AAC5C,iBAAO,KAAK,aAAa,eAAe;;AAE5C,iBAAS,cAAc,KAAG;AACtB,kBAAQ,aAAa,GAAG;AACxB,kBAAQ,aAAa,GAAG;AACxB,oBAAW,QAAQ,SAAS,gBAAgB;AAC5C,cAAI,eAAe,QAAQ,IAAI,SAAU,QAAM;AAC3C,mBAAO,EAAC,OAAO,MAAM,MAAM,GAAG,OAAO,MAAM,MAAM,EAAC;WACrD,EAAE,KAAK,SAAS,GAAE,GAAC;AAChB,mBAAO,QAAQ,EAAE,OAAO,EAAE,KAAK;WAClC;AACD,yBAAe,aAAa,IAAI,SAAU,IAAE;AAAG,mBAAO,GAAG;UAAM,CAAE;AACjE,yBAAe,aAAa,IAAI,SAAU,IAAE;AAAG,mBAAO,GAAG;UAAM,CAAE;AACjE,sBAAY;AACZ,0BAAiB,QAAQ,SAAS,KAAK;;AAE3C,sBAAc,MAAM;AAEpB,YAAI,IAAI,IAAI,YAAY,WACpB,aACA,WAAA;AAAI,iBAAA,YAAY,aAAa,CAAC,GAAG,aAAa,aAAW,CAAC,IAAI,MAAM;QAAC,CAAA;AAGzE,UAAE,qBAAqB,SAAUY,YAAS;AAEtC,wBAAcA,UAAS;;AAG3B,YAAI,sBAAsB;AAE1B,UAAE,cAAc,SAAU,QAAQ,SAAS,SAAO;AAI9C,cAAI,MAAM,OAAO;AACjB,cAAI,OAAO,QAAQ;AAAU,mBAAO;AACpC,cAAI,WAAW,MAAM,GAAG;AACxB,cAAI,MAAM,UAAU,cAAc,mBAAmB,GAAG;AACpD,mBAAO;iBACJ;AACH,gBAAI,uBAAuB;AAC3B,qBAAS,IAAE,qBAAqB,IAAE,YAAY,EAAE,GAAG;AAC/C,kBAAI,SAAS,WAAW,KAAK,UAAU,aAAa,CAAC,GAAG,aAAa,CAAC,GAAG,SAAS,SAAS;AAC3F,kBAAI,WAAW,QAAQ,yBAAyB;AAC5C,sCAAsB,IAAI;uBACrB,yBAAyB,QAAQ,QAAQ,sBAAsB,MAAM,IAAI,GAAG;AACjF,uCAAuB;;;AAG/B,gBAAI,yBAAyB,MAAM;AAC/B,sBAAQ,WAAA;AAAc,uBAAO,SAAS,uBAAuB,aAAa;cAAE,CAAE;mBAC3E;AACH,sBAAQ,OAAO;;AAEnB,mBAAO;;SAEd;AACD,eAAO;MACT;eAEgB,YAAa,OAAsB,OAAsB,WAAqB,WAAmB;AAC7G,eAAO;UACH,MAAI;UACJ;UACA;UACA;UACA;;MAER;eAEgB,WAAY,OAAoB;AAC5C,eAAO;UACH,MAAI;UACJ,OAAO;UACP,OAAO;;MAEf;ACpHA,UAAA,cAAA,WAAA;AAAA,iBAAAC,eAAA;;AAcE,eAAA,eAAIA,aAAA,WAAA,cAAU;eAAd,WAAA;AACE,mBAAO,KAAK,KAAK,MAAM,GAAG;;;;;AAQ5B,QAAAA,aAAA,UAAA,UAAA,SAAQ,OAAsB,OAAsB,cAAwB,cAAsB;AAChG,yBAAe,iBAAiB;AAChC,yBAAe,iBAAiB;AAChC,cAAI;AACF,gBAAK,KAAK,KAAK,OAAO,KAAK,IAAI,KAC5B,KAAK,KAAK,OAAO,KAAK,MAAM,MAAM,gBAAgB,iBAAiB,EAAE,gBAAgB;AACtF,qBAAO,gBAAgB,IAAI;AAC7B,mBAAO,IAAI,KAAK,WAAW,MAAM,WAAA;AAAI,qBAAA,YAAY,OAAO,OAAO,CAAC,cAAc,CAAC,YAAY;YAAC,CAAA;mBACrF,GAAG;AACV,mBAAO,KAAK,MAAM,oBAAoB;;;AAS1C,QAAAA,aAAA,UAAA,SAAA,SAAO,OAAoB;AACzB,cAAI,SAAS;AAAM,mBAAO,KAAK,MAAM,oBAAoB;AACzD,iBAAO,IAAI,KAAK,WAAW,MAAM,WAAA;AAAM,mBAAA,WAAW,KAAK;UAAC,CAAA;;AAQ1D,QAAAA,aAAA,UAAA,QAAA,SAAM,OAAoB;AACxB,cAAI,SAAS;AAAM,mBAAO,KAAK,MAAM,oBAAoB;AACzD,iBAAO,IAAI,KAAK,WAAW,MAAM,WAAA;AAAM,mBAAA,YAAY,OAAO,QAAW,IAAI;UAAC,CAAA;;AAQ5E,QAAAA,aAAA,UAAA,eAAA,SAAa,OAAoB;AAC/B,cAAI,SAAS;AAAM,mBAAO,KAAK,MAAM,oBAAoB;AACzD,iBAAO,IAAI,KAAK,WAAW,MAAM,WAAA;AAAM,mBAAA,YAAY,OAAO,QAAW,KAAK;UAAC,CAAA;;AAQ7E,QAAAA,aAAA,UAAA,QAAA,SAAM,OAAoB;AACxB,cAAI,SAAS;AAAM,mBAAO,KAAK,MAAM,oBAAoB;AACzD,iBAAO,IAAI,KAAK,WAAW,MAAM,WAAA;AAAM,mBAAA,YAAY,QAAW,OAAO,OAAO,IAAI;UAAC,CAAA;;AAQnF,QAAAA,aAAA,UAAA,eAAA,SAAa,OAAoB;AAC/B,cAAI,SAAS;AAAM,mBAAO,KAAK,MAAM,oBAAoB;AACzD,iBAAO,IAAI,KAAK,WAAW,MAAM,WAAA;AAAM,mBAAA,YAAY,QAAW,KAAK;UAAC,CAAA;;AAQtE,QAAAA,aAAA,UAAA,aAAA,SAAW,KAAW;AACpB,cAAI,OAAO,QAAQ;AAAU,mBAAO,KAAK,MAAM,eAAe;AAC9D,iBAAO,KAAK,QAAQ,KAAK,MAAM,WAAW,MAAM,IAAI;;AAQtD,QAAAA,aAAA,UAAA,uBAAA,SAAqB,KAAW;AAC9B,cAAI,QAAQ;AAAI,mBAAO,KAAK,WAAW,GAAG;AAC1C,iBAAO,uBAAuB,MAAM,SAAC,GAAG,GAAC;AAAK,mBAAA,EAAE,QAAQ,EAAE,CAAC,CAAC,MAAM;UAAC,GAAE,CAAC,GAAG,GAAG,SAAS;;AAQvF,QAAAA,aAAA,UAAA,mBAAA,SAAiB,KAAW;AAC1B,iBAAO,uBAAuB,MAAM,SAAC,GAAG,GAAC;AAAK,mBAAA,MAAM,EAAE,CAAC;UAAC,GAAE,CAAC,GAAG,GAAG,EAAE;;AAUrE,QAAAA,aAAA,UAAA,kBAAA,WAAA;AACE,cAAI,MAAM,WAAW,MAAM,eAAe,SAAS;AACnD,cAAI,IAAI,WAAW;AAAG,mBAAO,gBAAgB,IAAI;AACjD,iBAAO,uBAAuB,MAAM,SAAC,GAAG,GAAC;AAAK,mBAAA,EAAE,QAAQ,CAAC,MAAM;UAAE,GAAE,KAAK,EAAE;;AAU5E,QAAAA,aAAA,UAAA,4BAAA,WAAA;AACE,cAAI,MAAM,WAAW,MAAM,eAAe,SAAS;AACnD,cAAI,IAAI,WAAW;AAAG,mBAAO,gBAAgB,IAAI;AACjD,iBAAO,uBAAuB,MAAM,SAAC,GAAG,GAAC;AAAK,mBAAA,EAAE,KAAK,SAAA,GAAC;AAAI,qBAAA,EAAE,QAAQ,CAAC,MAAM;YAAC,CAAA;UAAC,GAAE,KAAK,SAAS;;AAU/F,QAAAA,aAAA,UAAA,QAAA,WAAA;AAAA,cAAA,QAAA;AACE,cAAM,MAAM,WAAW,MAAM,eAAe,SAAS;AACrD,cAAI,UAAU,KAAK;AACnB,cAAI;AAAE,gBAAI,KAAK,OAAO;mBAAY,GAAG;AAAE,mBAAO,KAAK,MAAM,oBAAoB;;AAC7E,cAAI,IAAI,WAAW;AAAG,mBAAO,gBAAgB,IAAI;AACjD,cAAM,IAAI,IAAI,KAAK,WAAW,MAAM,WAAA;AAAM,mBAAA,YAAY,IAAI,CAAC,GAAG,IAAI,IAAI,SAAS,CAAC,CAAC;UAAC,CAAA;AAElF,YAAE,qBAAqB,SAAA,WAAS;AAC9B,sBAAW,cAAc,SACvB,MAAK,aACL,MAAK;AACP,gBAAI,KAAK,OAAO;;AAGlB,cAAI,IAAI;AACR,YAAE,cAAc,SAAC,QAAQ,SAAS,SAAO;AACvC,gBAAM,MAAM,OAAO;AACnB,mBAAO,QAAQ,KAAK,IAAI,CAAC,CAAC,IAAI,GAAG;AAE/B,gBAAE;AACF,kBAAI,MAAM,IAAI,QAAQ;AAEpB,wBAAQ,OAAO;AACf,uBAAO;;;AAGX,gBAAI,QAAQ,KAAK,IAAI,CAAC,CAAC,MAAM,GAAG;AAE9B,qBAAO;mBACF;AAEL,sBAAQ,WAAA;AAAQ,uBAAO,SAAS,IAAI,CAAC,CAAC;cAAE,CAAE;AAC1C,qBAAO;;WAEV;AACD,iBAAO;;AAQT,QAAAA,aAAA,UAAA,WAAA,SAAS,OAAoB;AAC3B,iBAAO,KAAK,WAAW,CAAC,CAAC,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,eAAe,OAAO,eAAe,MAAK,CAAE;;AAUpH,QAAAA,aAAA,UAAA,SAAA,WAAA;AACE,cAAM,MAAM,WAAW,MAAM,eAAe,SAAS;AACrD,cAAI,IAAI,WAAW;AAAG,mBAAO,IAAI,KAAK,WAAW,IAAI;AACrD,cAAI;AAAE,gBAAI,KAAK,KAAK,UAAU;mBAAY,GAAG;AAAE,mBAAO,KAAK,MAAM,oBAAoB;;AAErF,cAAM,SAAS,IAAI,OACjB,SAAC,KAAK,KAAG;AAAK,mBAAA,MACZ,IAAI,OAAO,CAAC,CAAC,IAAI,IAAI,SAAS,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,IAC1C,CAAC,CAAC,QAAQ,GAAG,CAAC;UAAC,GACjB,IAAI;AACN,iBAAO,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;AAClD,iBAAO,KAAK,WAAW,QAAQ,EAAE,eAAe,OAAO,eAAe,MAAK,CAAE;;AAQ/E,QAAAA,aAAA,UAAA,aAAA,SACE,QACA,SAA8D;AAFhE,cAAA,QAAA;AAIE,cAAMb,OAAM,KAAK,MACX,YAAY,KAAK,YACjB,aAAa,KAAK,aAClB,MAAM,KAAK,MACX,MAAM,KAAK;AAEjB,cAAI,OAAO,WAAW;AAAG,mBAAO,gBAAgB,IAAI;AACpD,cAAI,CAAC,OAAO,MAAM,SAAA,OAAK;AACrB,mBAAA,MAAM,CAAC,MAAM,UACb,MAAM,CAAC,MAAM,UACb,UAAU,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK;WAAC,GAAG;AACrC,mBAAO,KACL,MACA,8HACA,WAAW,eAAe;;AAE9B,cAAM,gBAAgB,CAAC,WAAW,QAAQ,kBAAkB;AAC5D,cAAM,gBAAgB,WAAW,QAAQ,kBAAkB;AAE3D,mBAASc,UAASC,SAAQ,UAAQ;AAChC,gBAAI,IAAI,GAAG,IAAIA,QAAO;AACtB,mBAAO,IAAI,GAAG,EAAE,GAAG;AACjB,kBAAM,QAAQA,QAAO,CAAC;AACtB,kBAAIf,KAAI,SAAS,CAAC,GAAG,MAAM,CAAC,CAAC,IAAI,KAAKA,KAAI,SAAS,CAAC,GAAG,MAAM,CAAC,CAAC,IAAI,GAAG;AACpE,sBAAM,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC;AACpC,sBAAM,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC;AACpC;;;AAGJ,gBAAI,MAAM;AACR,cAAAe,QAAO,KAAK,QAAQ;AACtB,mBAAOA;;AAGT,cAAI,gBAAgB;AACpB,mBAAS,YAAY,GAAG,GAAC;AAAI,mBAAO,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;UAAE;AAG9D,cAAI;AACJ,cAAI;AACF,kBAAM,OAAO,OAAOD,WAAU,CAAA,CAAE;AAChC,gBAAI,KAAK,WAAW;mBACb,IAAI;AACX,mBAAO,KAAK,MAAM,oBAAoB;;AAGxC,cAAI,WAAW;AACf,cAAM,0BAA0B,gBAC9B,SAAA,KAAG;AAAI,mBAAA,UAAU,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,IAAI;UAAC,IAC3C,SAAA,KAAG;AAAI,mBAAA,UAAU,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,KAAK;UAAC;AAE9C,cAAM,0BAA0B,gBAC9B,SAAA,KAAG;AAAI,mBAAA,WAAW,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,IAAI;UAAC,IAC5C,SAAA,KAAG;AAAI,mBAAA,WAAW,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,KAAK;UAAC;AAE/C,mBAAS,sBAAsB,KAAG;AAChC,mBAAO,CAAC,wBAAwB,GAAG,KAAK,CAAC,wBAAwB,GAAG;;AAGtE,cAAI,WAAW;AAEf,cAAM,IAAI,IAAI,KAAK,WACjB,MACA,WAAA;AAAM,mBAAA,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,IAAI,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa;UAAC,CAAA;AAEtF,YAAE,qBAAqB,SAAA,WAAS;AAC9B,gBAAI,cAAc,QAAQ;AACxB,yBAAW;AACX,8BAAgB;mBACX;AACL,yBAAW;AACX,8BAAgB;;AAElB,gBAAI,KAAK,WAAW;;AAGtB,YAAE,cAAc,SAAC,QAAQ,SAAS,SAAO;AACvC,gBAAI,MAAM,OAAO;AACjB,mBAAO,SAAS,GAAG,GAAG;AAEpB,gBAAE;AACF,kBAAI,aAAa,IAAI,QAAQ;AAE3B,wBAAQ,OAAO;AACf,uBAAO;;;AAGX,gBAAI,sBAAsB,GAAG,GAAG;AAE9B,qBAAO;uBACE,MAAK,KAAK,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,MAAM,KAAK,MAAK,KAAK,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,MAAM,GAAG;AAG3F,qBAAO;mBACF;AAEL,sBAAQ,WAAA;AACN,oBAAI,kBAAkB;AAAW,yBAAO,SAAS,IAAI,QAAQ,EAAE,CAAC,CAAC;;AAC5D,yBAAO,SAAS,IAAI,QAAQ,EAAE,CAAC,CAAC;eACtC;AACD,qBAAO;;WAEV;AACD,iBAAO;;AAUT,QAAAD,aAAA,UAAA,kBAAA,WAAA;AACE,cAAM,MAAM,WAAW,MAAM,eAAe,SAAS;AAErD,cAAI,CAAC,IAAI,MAAM,SAAA,GAAC;AAAI,mBAAA,OAAO,MAAM;UAAQ,CAAA,GAAG;AACxC,mBAAO,KAAK,MAAM,2CAA2C;;AAEjE,cAAI,IAAI,WAAW;AAAG,mBAAO,gBAAgB,IAAI;AAEjD,iBAAO,KAAK,WAAW,IAAI,IAAI,SAAC,KAAW;AAAK,mBAAA,CAAC,KAAK,MAAM,SAAS;UAAC,CAAA,CAAC;;AAG3E,eAAAA;MAAA,EAAC;eCzVe,6BAA6B,IAAS;AACpD,eAAO,qBACL,YAAY,WAEZ,SAASA,aAA+B,OAAc,OAAgB,cAAyB;AAC7F,eAAK,KAAK;AACV,eAAK,OAAO;YACV;YACA,OAAO,UAAU,QAAQ,OAAO;YAChC,IAAI;;AAEN,eAAK,OAAO,KAAK,aAAab;AAC9B,eAAK,cAAc,SAAC,GAAG,GAAC;AAAK,mBAAAA,KAAI,GAAG,CAAC;UAAC;AACtC,eAAK,OAAO,SAAC,GAAG,GAAC;AAAK,mBAAAA,KAAI,GAAE,CAAC,IAAI,IAAI,IAAI;UAAC;AAC1C,eAAK,OAAO,SAAC,GAAG,GAAC;AAAK,mBAAAA,KAAI,GAAE,CAAC,IAAI,IAAI,IAAI;UAAC;AAC1C,eAAK,eAAe,GAAG,MAAM;AAC7B,cAAI,CAAC,KAAK;AAAc,kBAAM,IAAI,WAAW,WAAU;SACxD;MAEL;eCpCgB,mBAAmB,QAAM;AACvC,eAAO,KAAK,SAAU,OAAK;AACvB,yBAAe,KAAK;AACpB,iBAAQ,MAAM,OAAO,KAAK;AAC1B,iBAAO;SACV;MACH;eA4CgB,eAAe,OAAK;AAClC,YAAI,MAAM;AACN,gBAAM,gBAAe;AACzB,YAAI,MAAM;AACN,gBAAM,eAAc;MAC1B;ACtDO,UAAM,mCAAmC;AAazC,UAAM,iCAAiC;AAEvC,UAAM,eAAe,OAAO,MAAM,gCAAgC;ACCzE,UAAA,cAAA,WAAA;AAAA,iBAAAgB,eAAA;;AA8BE,QAAAA,aAAA,UAAA,QAAA,WAAA;AACE,iBAAO,CAAC,IAAI,MAAM;AAElB,YAAE,KAAK;AACP,cAAI,KAAK,cAAc,KAAK,CAAC,IAAI;AAAQ,gBAAI,eAAe;AAC5D,iBAAO;;AAOT,QAAAA,aAAA,UAAA,UAAA,WAAA;AACE,iBAAO,CAAC,IAAI,MAAM;AAClB,cAAI,EAAE,KAAK,cAAc,GAAG;AAC1B,gBAAI,CAAC,IAAI;AAAQ,kBAAI,eAAe;AACpC,mBAAO,KAAK,cAAc,SAAS,KAAK,CAAC,KAAK,QAAO,GAAI;AACvD,kBAAI,WAAW,KAAK,cAAc,MAAK;AACvC,kBAAI;AAAE,uBAAO,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;uBAAY,GAAG;cAAA;;;AAGxD,iBAAO;;AAOT,QAAAA,aAAA,UAAA,UAAA,WAAA;AAWE,iBAAO,KAAK,aAAa,IAAI,iBAAiB;;AAQhD,QAAAA,aAAA,UAAA,SAAA,SAAO,UAAiD;AAAxD,cAAA,QAAA;AACE,cAAI,CAAC,KAAK;AAAM,mBAAO;AACvB,cAAM,QAAQ,KAAK,GAAG;AACtB,cAAM,cAAc,KAAK,GAAG,OAAO;AACnC,iBAAO,CAAC,KAAK,QAAQ;AACrB,cAAI,CAAC,YAAY,CAAC,OAAO;AACvB,oBAAQ,eAAe,YAAY,MAAI;cACrC,KAAK;AAEH,sBAAM,IAAI,WAAW,eAAe,WAAW;cACjD,KAAK;AAEH,sBAAM,IAAI,WAAW,WAAW,YAAY,SAAS,WAAW;cAClE;AAEE,sBAAM,IAAI,WAAW,WAAW,WAAW;;;AAGjD,cAAI,CAAC,KAAK;AAAQ,kBAAM,IAAI,WAAW,oBAAmB;AAC1D,iBAAO,KAAK,YAAY,WAAW,IAAI;AAEvC,qBAAW,KAAK,WAAW,aACxB,KAAK,GAAG,OACL,KAAK,GAAG,KAAK,YAAY,KAAK,YAAY,KAAK,MAAkC,EAAE,YAAY,KAAK,4BAA2B,CAAE,IACjI,MAAM,YAAY,KAAK,YAAY,KAAK,MAAM,EAAE,YAAY,KAAK,4BAA2B,CAAE;AAGpG,mBAAS,UAAU,KAAK,SAAA,IAAE;AACxB,2BAAe,EAAE;AACjB,kBAAK,QAAQ,SAAS,KAAK;WAC5B;AACD,mBAAS,UAAU,KAAK,SAAA,IAAE;AACxB,2BAAe,EAAE;AACjB,kBAAK,UAAU,MAAK,QAAQ,IAAI,WAAW,MAAM,SAAS,KAAK,CAAC;AAChE,kBAAK,SAAS;AACd,kBAAK,GAAG,OAAO,EAAE,KAAK,EAAE;WACzB;AACD,mBAAS,aAAa,KAAK,WAAA;AACzB,kBAAK,SAAS;AACd,kBAAK,SAAQ;AACb,gBAAI,kBAAkB,UAAU;AAC9B,2BAAa,eAAe,KAAK,SAAS,cAAc,CAAC;;WAE5D;AACD,iBAAO;;AAOT,QAAAA,aAAA,UAAA,WAAA,SACE,MACA,IACA,YAA6B;AAH/B,cAAA,QAAA;AAKE,cAAI,SAAS,eAAe,KAAK,SAAS;AACxC,mBAAO,UAAU,IAAI,WAAW,SAAS,yBAAyB,CAAC;AAErE,cAAI,CAAC,KAAK;AACR,mBAAO,UAAU,IAAI,WAAW,oBAAmB,CAAE;AAEvD,cAAI,KAAK,QAAO,GAAI;AAClB,mBAAO,IAAI,aAAa,SAAC,SAAS,QAAM;AACtC,oBAAK,cAAc,KAAK,CAAC,WAAA;AACvB,sBAAK,SAAS,MAAM,IAAI,UAAU,EAAE,KAAK,SAAS,MAAM;iBACvD,GAAG,CAAC;aACR;qBAEQ,YAAY;AACrB,mBAAO,SAAS,WAAA;AACd,kBAAIC,KAAI,IAAI,aAAa,SAAC,SAAS,QAAM;AACvC,sBAAK,MAAK;AACV,oBAAM,KAAK,GAAG,SAAS,QAAQ,KAAI;AACnC,oBAAI,MAAM,GAAG;AAAM,qBAAG,KAAK,SAAS,MAAM;eAC3C;AACD,cAAAA,GAAE,QAAQ,WAAA;AAAM,uBAAA,MAAK,QAAO;cAAE,CAAA;AAC9B,cAAAA,GAAE,OAAO;AACT,qBAAOA;aACR;iBAEI;AACL,gBAAI,IAAI,IAAI,aAAa,SAAC,SAAS,QAAM;AACvC,kBAAI,KAAK,GAAG,SAAS,QAAQ,KAAI;AACjC,kBAAI,MAAM,GAAG;AAAM,mBAAG,KAAK,SAAS,MAAM;aAC3C;AACD,cAAE,OAAO;AACT,mBAAO;;;AAQX,QAAAD,aAAA,UAAA,QAAA,WAAA;AACE,iBAAO,KAAK,SAAS,KAAK,OAAO,MAAK,IAAK;;AAS7C,QAAAA,aAAA,UAAA,UAAA,SAAQ,aAA6B;AAEnC,cAAI,OAAO,KAAK,MAAK;AAGrB,cAAM,UAAU,aAAa,QAAQ,WAAW;AAChD,cAAI,KAAK,aAAa;AAEpB,iBAAK,cAAc,KAAK,YAAY,KAAK,WAAA;AAAM,qBAAA;YAAO,CAAA;iBACjD;AAEL,iBAAK,cAAc;AACnB,iBAAK,gBAAgB,CAAA;AAErB,gBAAI,QAAQ,KAAK,SAAS,YAAY,KAAK,WAAW,CAAC,CAAC;AACxD,aAAC,SAAS,OAAI;AACZ,gBAAE,KAAK;AACP,qBAAO,KAAK,cAAc;AAAQ,gBAAC,KAAK,cAAc,MAAK,EAAE;AAC7D,kBAAI,KAAK;AAAa,sBAAM,IAAI,SAAS,EAAE,YAAY;eACxD;;AAEH,cAAI,qBAAqB,KAAK;AAC9B,iBAAO,IAAI,aAAa,SAAC,SAAS,QAAM;AACtC,oBAAQ,KACN,SAAA,KAAG;AAAI,qBAAA,KAAK,cAAc,KAAK,KAAK,QAAQ,KAAK,MAAM,GAAG,CAAC,CAAC;YAAC,GAC7D,SAAA,KAAG;AAAI,qBAAA,KAAK,cAAc,KAAK,KAAK,OAAO,KAAK,MAAM,GAAG,CAAC,CAAC;YAAC,CAAA,EAC5D,QAAQ,WAAA;AACR,kBAAI,KAAK,gBAAgB,oBAAoB;AAE3C,qBAAK,cAAc;;aAEtB;WACF;;AAOH,QAAAA,aAAA,UAAA,QAAA,WAAA;AACE,cAAI,KAAK,QAAQ;AACf,iBAAK,SAAS;AACd,gBAAI,KAAK;AAAU,mBAAK,SAAS,MAAK;AACtC,iBAAK,QAAQ,IAAI,WAAW,MAAK,CAAE;;;AAQvC,QAAAA,aAAA,UAAA,QAAA,SAAM,WAAiB;AACrB,cAAM,iBAAkB,KAAK,oBAAoB,KAAK,kBAAkB,CAAA;AACxE,cAAI,OAAO,gBAAgB,SAAS;AAClC,mBAAO,eAAe,SAAS;AACjC,cAAM,cAAc,KAAK,OAAO,SAAS;AACzC,cAAI,CAAC,aAAa;AAChB,kBAAM,IAAI,WAAW,SAAS,WAAW,YAAY,0BAA0B;;AAGjF,cAAM,wBAAwB,IAAI,KAAK,GAAG,MAAM,WAAW,aAAa,IAAI;AAC5E,gCAAsB,OAAO,KAAK,GAAG,KAAK,MAAM,SAAS;AACzD,yBAAe,SAAS,IAAI;AAC5B,iBAAO;;AAEX,eAAAA;MAAA,EAAC;eCnPe,6BAA6B,IAAS;AACpD,eAAO,qBACL,YAAY,WACZ,SAASA,aAEP,MACA,YACA,UACA,6BACA,QAAoB;AANtB,cAAA,QAAA;AAQE,eAAK,KAAK;AACV,eAAK,OAAO;AACZ,eAAK,aAAa;AAClB,eAAK,SAAS;AACd,eAAK,8BAA8B;AACnC,eAAK,WAAW;AAChB,eAAK,KAAK,OAAO,MAAM,YAAY,SAAS,OAAO;AACnD,eAAK,SAAS,UAAU;AACxB,eAAK,SAAS;AACd,eAAK,YAAY;AACjB,eAAK,gBAAgB,CAAA;AACrB,eAAK,WAAW;AAChB,eAAK,UAAU;AACf,eAAK,cAAc;AACnB,eAAK,gBAAgB;AACrB,eAAK,aAAa;AAClB,eAAK,cAAc,IAAIZ,aAAS,SAAC,SAAS,QAAM;AAC5C,kBAAK,WAAW;AAChB,kBAAK,UAAU;WAClB;AAED,eAAK,YAAY,KACb,WAAA;AACI,kBAAK,SAAS;AACd,kBAAK,GAAG,SAAS,KAAI;aAEzB,SAAA,GAAC;AACG,gBAAI,YAAY,MAAK;AACrB,kBAAK,SAAS;AACd,kBAAK,GAAG,MAAM,KAAK,CAAC;AACpB,kBAAK,SACD,MAAK,OAAO,QAAQ,CAAC,IACrB,aAAa,MAAK,YAAY,MAAK,SAAS,MAAK;AACrD,mBAAO,UAAU,CAAC;WACrB;SAEN;MACL;eCrEgB,gBACd,MACA,SACA,QACA,OACA,MACA,UACA,WAAkB;AAElB,eAAO;UACL;UACA;UACA;UACA;UACA;UACA;UACA,MAAM,UAAU,CAAC,YAAY,MAAM,OAAO,QAAQ,MAAM,OAAO,OAAO,OAAO,MAAM,gBAAgB,OAAO;;MAE9G;eAEgB,gBAAiB,SAA2B;AAC1D,eAAO,OAAO,YAAY,WACxB,UACA,UAAW,MAAM,CAAA,EAAG,KAAK,KAAK,SAAS,GAAG,IAAI,MAAO;MACzD;eCrBgB,kBACd,MACA,SACA,SAAoB;AAEpB,eAAO;UACL;UACA;UACA;UACA,aAAa;UACb,WAAW,cAAc,SAAS,SAAA,OAAK;AAAI,mBAAA,CAAC,MAAM,MAAM,KAAK;UAAC,CAAA;;MAElE;eCfgB,oBAAoB,YAAoB;AACtD,eAAO,WAAW,WAAW,IAAI,WAAW,CAAC,IAAI;MACnD;AAOO,UAAI,YAAY,SAAC,aAA+B;AACrD,YAAI;AACF,sBAAY,KAAK,CAAC,CAAA,CAAE,CAAC;AACrB,sBAAY,WAAA;AAAM,mBAAA,CAAC,CAAA,CAAE;UAAC;AACtB,iBAAO,CAAC,CAAA,CAAE;iBACH,GAAG;AACV,sBAAY,WAAA;AAAM,mBAAA;UAAS;AAC3B,iBAAO;;MAEX;eClBgB,gBAAiB,SAAiC;AAChE,YAAI,WAAW,MAAM;AACnB,iBAAO,WAAA;AAAM,mBAAA;UAAS;mBACb,OAAO,YAAY,UAAU;AACtC,iBAAO,0BAA0B,OAAO;eACnC;AACL,iBAAO,SAAA,KAAG;AAAI,mBAAA,aAAa,KAAK,OAAO;UAAC;;MAE5C;eAEgB,0BAA0B,SAAe;AACvD,YAAM,QAAQ,QAAQ,MAAM,GAAG;AAC/B,YAAI,MAAM,WAAW,GAAG;AACtB,iBAAO,SAAA,KAAG;AAAI,mBAAA,IAAI,OAAO;UAAC;eACrB;AACL,iBAAO,SAAA,KAAG;AAAI,mBAAA,aAAa,KAAK,OAAO;UAAC;;MAE5C;eCCgB,SAAY,WAA+C;AACzE,eAAO,CAAA,EAAG,MAAM,KAAK,SAAS;MAChC;AAOA,UAAI,cAAc;eAEF,gBAAgB,SAAiC;AAC/D,eAAO,WAAW,OAChB,QACA,OAAO,YAAY,WACjB,UACA,IAAA,OAAI,QAAQ,KAAK,GAAG,GAAC,GAAA;MAC3B;eAEgB,aACd,IACA,aACA,UAAwB;AAExB,iBAAS,cAAcc,KAAiB,OAAqB;AAC3D,cAAMC,UAAS,SAASD,IAAG,gBAAgB;AAC3C,iBAAO;YACL,QAAQ;cACN,MAAMA,IAAG;cACT,QAAQC,QAAO,IAAI,SAAA,OAAK;AAAI,uBAAA,MAAM,YAAY,KAAK;cAAC,CAAA,EAAE,IAAI,SAAA,OAAK;AACtD,oBAAA,UAA0B,MAAK,SAAtB,gBAAiB,MAAK;AACtC,oBAAM,WAAW,QAAQ,OAAO;AAChC,oBAAM,WAAW,WAAW;AAC5B,oBAAM,iBAAwD,CAAA;AAC9D,oBAAM,SAAS;kBACb,MAAM,MAAM;kBACZ,YAAY;oBACV,MAAM;oBACN,cAAc;oBACd;oBACA;oBACA;oBACA;oBACA,QAAQ;oBACR,YAAY,gBAAgB,OAAO;;kBAErC,SAAS,SAAS,MAAM,UAAU,EAAE,IAAI,SAAA,WAAS;AAAI,2BAAA,MAAM,MAAM,SAAS;kBAAC,CAAA,EACxE,IAAI,SAAA,OAAK;AACD,wBAAA,OAAqC,MAAK,MAApC,SAA+B,MAAK,QAA5B,aAAuB,MAAK,YAAhBC,WAAW,MAAK;AACjD,wBAAMC,YAAW,QAAQD,QAAO;AAChC,wBAAME,UAAsB;sBAC1B;sBACA,UAAQD;sBACR,SAAOD;sBACP;sBACA;sBACA,YAAY,gBAAgBA,QAAO;;AAErC,mCAAe,gBAAgBA,QAAO,CAAC,IAAIE;AAC3C,2BAAOA;mBACR;kBACH,mBAAmB,SAACF,UAAiC;AAAK,2BAAA,eAAe,gBAAgBA,QAAO,CAAC;kBAAC;;AAEpG,+BAAe,KAAK,IAAI,OAAO;AAC/B,oBAAI,WAAW,MAAM;AACnB,iCAAe,gBAAgB,OAAO,CAAC,IAAI,OAAO;;AAEpD,uBAAO;eACR;;YAEH,WAAWD,QAAO,SAAS,KAAM,YAAY,MAAM,YAAYA,QAAO,CAAC,CAAC,KACtE,EAAE,OAAO,cAAc,eAAe,SAAS,KAAK,UAAU,SAAS,KACvE,CAAC,oBAAoB,KAAK,UAAU,SAAS,KAC7C,CAAA,EAAG,OAAO,UAAU,UAAU,MAAM,eAAe,CAAC,EAAE,CAAC,IAAI;;;AAIjE,iBAAS,gBAAiB,OAAqB;AAC7C,cAAI,MAAM,SAAI;AAA0B,mBAAO;AAC/C,cAAI,MAAM,SAAI;AAA4B,kBAAM,IAAI,MAAM,0CAA0C;AAC7F,cAAA,QAAsC,MAAK,OAApC,QAA+B,MAAK,OAA7B,YAAwB,MAAK,WAAlB,YAAa,MAAK;AAClD,cAAM,WAAW,UAAU,SACzB,UAAU,SACR,OACA,YAAY,WAAW,OAAO,CAAC,CAAC,SAAS,IAC3C,UAAU,SACR,YAAY,WAAW,OAAO,CAAC,CAAC,SAAS,IACzC,YAAY,MAAM,OAAO,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,SAAS;AAC5D,iBAAO;;AAGT,iBAAS,kBAAkB,aAA8B;AACvD,cAAM,YAAY,YAAY;AAE9B,mBAAS,OAAQzB,KAAkC;gBAAjC,QAAKA,IAAA,OAAEC,QAAID,IAAA,MAAEW,QAAIX,IAAA,MAAE,SAAMA,IAAA,QAAE,QAAKA,IAAA;AAChD,mBAAO,IAAI,QAA8B,SAAC,SAAS,QAAM;AACvD,wBAAU,KAAK,OAAO;AACtB,kBAAM,QAAS,MAAyB,YAAY,SAAS;AAC7D,kBAAM,WAAW,MAAM,WAAW;AAClC,kBAAM,aAAaC,UAAS,SAASA,UAAS;AAC9C,kBAAI,CAAC,cAAcA,UAAS,YAAYA,UAAS;AAC/C,sBAAM,IAAI,MAAO,6BAA6BA,KAAI;AAE7C,kBAAA,UAAUU,SAAQ,UAAU,EAAC,QAAQ,EAAC,GAAC;AAC9C,kBAAIA,SAAQ,UAAUA,MAAK,WAAW,OAAO,QAAQ;AACnD,sBAAM,IAAI,MAAM,+DAA+D;;AAEjF,kBAAI,WAAW;AAEb,uBAAO,QAAQ,EAAC,aAAa,GAAG,UAAU,CAAA,GAAI,SAAS,CAAA,GAAI,YAAY,OAAS,CAAC;AAEnF,kBAAI;AACJ,kBAAM,OAAqB,CAAA;AAE3B,kBAAM,WAA+C,CAAA;AACrD,kBAAI,cAAc;AAClB,kBAAM,eACJ,SAAA,OAAK;AACH,kBAAE;AACF,+BAAe,KAAK;;AAGxB,kBAAIV,UAAS,eAAe;AAE1B,oBAAI,MAAM,SAAI;AACZ,yBAAO,QAAQ,EAAC,aAAa,UAAU,SAAS,CAAA,GAAI,YAAY,OAAS,CAAC;AAC5E,oBAAI,MAAM,SAAI;AACZ,uBAAK,KAAK,MAAM,MAAM,MAAK,CAAE;;AAE7B,uBAAK,KAAK,MAAM,MAAM,OAAO,gBAAgB,KAAK,CAAC,CAAC;qBACjD;AAEC,oBAAAD,MAAiB,aACrB,WACE,CAAC,QAAQW,KAAI,IACb,CAAC,QAAQ,IAAI,IACf,CAACA,OAAM,IAAI,GAJN,QAAKX,IAAA,CAAA,GAAE,QAAKA,IAAA,CAAA;AAMnB,oBAAI,YAAY;AACd,2BAAS,IAAE,GAAG,IAAE,QAAQ,EAAE,GAAG;AAC3B,yBAAK,KAAK,MAAO,SAAS,MAAM,CAAC,MAAM,SACrC,MAAMC,KAAI,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,IAC9B,MAAMA,KAAI,EAAE,MAAM,CAAC,CAAC,CAAgB;AACtC,wBAAI,UAAU;;uBAEX;AACL,2BAAS,IAAE,GAAG,IAAE,QAAQ,EAAE,GAAG;AAC3B,yBAAK,KAAK,MAAM,MAAMA,KAAI,EAAE,MAAM,CAAC,CAAC,CAAe;AACnD,wBAAI,UAAU;;;;AAIpB,kBAAM,OAAO,SAAA,OAAK;AAChB,oBAAM,aAAa,MAAM,OAAO;AAChC,qBAAK,QAAQ,SAAC4B,MAAKjB,IAAC;AAAK,yBAAAiB,KAAI,SAAS,SAAS,SAASjB,EAAC,IAAIiB,KAAI;gBAAM,CAAA;AACvE,wBAAQ;kBACN;kBACA;kBACA,SAAS5B,UAAS,WAAWU,QAAO,KAAK,IAAI,SAAAkB,MAAG;AAAI,2BAAAA,KAAI;kBAAM,CAAA;kBAC9D;iBACD;;AAGH,kBAAI,UAAU,SAAA,OAAK;AACjB,6BAAa,KAAK;AAClB,qBAAK,KAAK;;AAGZ,kBAAI,YAAY;aACjB;;AAGH,mBAASC,YAAY9B,KAAgE;gBAA/D,QAAKA,IAAA,OAAE,SAAMA,IAAA,QAAE+B,SAAK/B,IAAA,OAAE,UAAOA,IAAA,SAAE,SAAMA,IAAA;AAEzD,mBAAO,IAAI,QAAQ,SAAC,SAAS,QAAM;AACjC,wBAAU,KAAK,OAAO;AACf,kBAAA,QAAgB+B,OAAK,OAAd,QAASA,OAAK;AAC5B,kBAAM,QAAS,MAAyB,YAAY,SAAS;AAE7D,kBAAM,SAAS,MAAM,eACnB,QACA,MAAM,MAAM,MAAM,IAAI;AAExB,kBAAM,YAAY,UAChB,SACE,eACA,SACF,SACE,eACA;AAEJ,kBAAM,MAAM,UAAU,EAAE,mBAAmB,UACzC,OAAO,WAAW,gBAAgB,KAAK,GAAG,SAAS,IACnD,OAAO,cAAc,gBAAgB,KAAK,GAAG,SAAS;AAGxD,kBAAI,UAAU,mBAAmB,MAAM;AACvC,kBAAI,YAAY,KAAK,SAAA,IAAE;AAErB,oBAAM,SAAS,IAAI;AACnB,oBAAI,CAAC,QAAQ;AACX,0BAAQ,IAAI;AACZ;;AAED,uBAAe,QAAQ,EAAE;AACzB,uBAAe,OAAO;AACvB,oBAAM,kBAAkB,OAAO,SAAS,KAAK,MAAM;AACnD,oBAAI,4BAA4B,OAAO;AACvC,oBAAI;AAA2B,8CAA4B,0BAA0B,KAAK,MAAM;AAChG,oBAAM,iBAAiB,OAAO,QAAQ,KAAK,MAAM;AACjD,oBAAM,4BAA4B,WAAA;AAAK,wBAAM,IAAI,MAAM,oBAAoB;gBAAE;AAC7E,oBAAM,yBAAyB,WAAA;AAAK,wBAAM,IAAI,MAAM,oBAAoB;gBAAE;AACzE,uBAAe,QAAQ;AACxB,uBAAO,OAAO,OAAO,WAAW,OAAO,qBAAqB,OAAO,UAAU;AAC7E,uBAAO,OAAO,KAAK,MAAM;AACzB,uBAAO,OAAO,WAAA;AAAA,sBAAA,QAAA;AAGZ,sBAAI,SAAS;AACb,yBAAO,KAAK,MAAM,WAAA;AAAM,2BAAA,WAAW,MAAK,SAAQ,IAAK,MAAK,KAAI;kBAAE,CAAA,EAAE,KAAK,WAAA;AAAM,2BAAA;kBAAI,CAAA;;AAEnF,uBAAO,QAAQ,SAAC,UAAQ;AAEtB,sBAAM,mBAAmB,IAAI,QAAc,SAAC,kBAAkB,iBAAe;AAC3E,uCAAmB,KAAK,gBAAgB;AACxC,wBAAI,UAAU,mBAAmB,eAAe;AAChD,2BAAO,OAAO;AACd,2BAAO,OAAO,SAAA,OAAK;AAEjB,6BAAO,OAAO,OAAO,WAAW,OAAO,qBAAqB,OAAO,UAAU;AAC7E,uCAAiB,KAAK;;mBAEzB;AAED,sBAAM,kBAAkB,WAAA;AACtB,wBAAI,IAAI,QAAQ;AAEd,0BAAI;AACF,iCAAQ;+BACD,KAAK;AACZ,+BAAO,KAAK,GAAG;;2BAEZ;AACJ,6BAAe,OAAO;AACvB,6BAAO,QAAQ,WAAA;AAAK,8BAAM,IAAI,MAAM,0BAA0B;sBAAE;AAChE,6BAAO,KAAI;;;AAGf,sBAAI,YAAY,KAAK,SAAAC,KAAE;AAIrB,wBAAI,YAAY;AAChB,oCAAe;mBAChB;AACD,yBAAO,WAAW;AAClB,yBAAO,qBAAqB;AAC5B,yBAAO,UAAU;AACjB,kCAAe;AACf,yBAAO;;AAET,wBAAQ,MAAM;iBACb,MAAM;aACV;;AAGH,mBAAS,MAAOC,YAAkB;AAChC,mBAAO,SAAC,SAA2B;AACjC,qBAAO,IAAI,QAA6B,SAAC,SAAS,QAAM;AACtD,0BAAU,KAAK,OAAO;AACf,oBAAA,QAA+B,QAAO,OAA/B,SAAwB,QAAO,QAAvB,QAAgB,QAAO,OAAhBF,SAAS,QAAO;AAC7C,oBAAM,kBAAkB,UAAU,WAAW,SAAY;AAClD,oBAAA,QAAgBA,OAAK,OAAd,QAASA,OAAK;AAC5B,oBAAM,QAAS,MAAyB,YAAY,SAAS;AAC7D,oBAAM,SAAS,MAAM,eAAe,QAAQ,MAAM,MAAM,MAAM,IAAI;AAClE,oBAAM,cAAc,gBAAgB,KAAK;AACzC,oBAAI,UAAU;AAAG,yBAAO,QAAQ,EAAC,QAAQ,CAAA,EAAE,CAAC;AAC5C,oBAAIE,YAAW;AACb,sBAAM,MAAM,SACP,OAAe,OAAO,aAAa,eAAe,IAClD,OAAe,WAAW,aAAa,eAAe;AAC3D,sBAAI,YAAY,SAAA,OAAK;AAAI,2BAAA,QAAQ,EAAC,QAAQ,MAAM,OAAO,OAAM,CAAC;kBAAC;AAC/D,sBAAI,UAAU,mBAAmB,MAAM;uBAClC;AACL,sBAAI,UAAQ;AACZ,sBAAM,QAAM,UAAU,EAAE,mBAAmB,UACzC,OAAO,WAAW,WAAW,IAC7B,OAAO,cAAc,WAAW;AAClC,sBAAM,WAAS,CAAA;AACf,wBAAI,YAAY,SAAA,OAAK;AACnB,wBAAM,SAAS,MAAI;AACnB,wBAAI,CAAC;AAAQ,6BAAO,QAAQ,EAAC,QAAM,SAAA,CAAC;AACpC,6BAAO,KAAK,SAAS,OAAO,QAAQ,OAAO,UAAU;AACrD,wBAAI,EAAE,YAAU;AAAO,6BAAO,QAAQ,EAAC,QAAM,SAAA,CAAC;AAC9C,2BAAO,SAAQ;;AAEjB,wBAAI,UAAU,mBAAmB,MAAM;;eAE1C;;;AAIL,iBAAO;YACL,MAAM;YACN,QAAQ;YAER;YAEA,SAAO,SAAEjC,KAAa;kBAAZ,QAAKA,IAAA,OAAEW,QAAIX,IAAA;AACnB,qBAAO,IAAI,QAAe,SAAC,SAAS,QAAM;AACxC,0BAAU,KAAK,OAAO;AACtB,oBAAM,QAAS,MAAyB,YAAY,SAAS;AAC7D,oBAAM,SAASW,MAAK;AACpB,oBAAM,SAAS,IAAI,MAAM,MAAM;AAC/B,oBAAI,WAAW;AACf,oBAAI,gBAAgB;AAEpB,oBAAI;AAEJ,oBAAM,iBAAiB,SAAA,OAAK;AAC1B,sBAAMkB,OAAM,MAAM;AAClB,uBAAK,OAAOA,KAAI,IAAI,IAAIA,KAAI,WAAW;AAAM;AAC7C,sBAAI,EAAE,kBAAkB;AAAU,4BAAQ,MAAM;;AAElD,oBAAM,eAAe,mBAAmB,MAAM;AAE9C,yBAAS,IAAE,GAAG,IAAE,QAAQ,EAAE,GAAG;AAC3B,sBAAM,MAAMlB,MAAK,CAAC;AAClB,sBAAI,OAAO,MAAM;AACf,0BAAM,MAAM,IAAIA,MAAK,CAAC,CAAC;AACvB,wBAAI,OAAO;AACX,wBAAI,YAAY;AAChB,wBAAI,UAAU;AACd,sBAAE;;;AAGN,oBAAI,aAAa;AAAG,0BAAQ,MAAM;eACnC;;YAGH,KAAG,SAAEX,KAAY;kBAAX,QAAKA,IAAA,OAAE,MAAGA,IAAA;AACd,qBAAO,IAAI,QAAa,SAAC,SAAS,QAAM;AACtC,0BAAU,KAAM,OAAO;AACvB,oBAAM,QAAS,MAAyB,YAAY,SAAS;AAC7D,oBAAM,MAAM,MAAM,IAAI,GAAG;AACzB,oBAAI,YAAY,SAAA,OAAK;AAAI,yBAAA,QAAS,MAAM,OAAe,MAAM;gBAAC;AAC9D,oBAAI,UAAU,mBAAmB,MAAM;eACxC;;YAGH,OAAO,MAAM,SAAS;YAEtB,YAAU8B;YAEV,OAAK,SAAE9B,KAAc;kBAAb+B,SAAK/B,IAAA,OAAE,QAAKA,IAAA;AACX,kBAAA,QAAgB+B,OAAK,OAAd,QAASA,OAAK;AAC5B,qBAAO,IAAI,QAAgB,SAAC,SAAS,QAAM;AACzC,oBAAM,QAAS,MAAyB,YAAY,SAAS;AAC7D,oBAAM,SAAS,MAAM,eAAe,QAAQ,MAAM,MAAM,MAAM,IAAI;AAClE,oBAAM,cAAc,gBAAgB,KAAK;AACzC,oBAAM,MAAM,cAAc,OAAO,MAAM,WAAW,IAAI,OAAO,MAAK;AAClE,oBAAI,YAAY,KAAK,SAAA,IAAE;AAAI,yBAAA,QAAS,GAAG,OAAsB,MAAM;gBAAC,CAAA;AACpE,oBAAI,UAAU,mBAAmB,MAAM;eACxC;;;;AAKD,YAAA/B,MAAsB,cAAc,IAAI,QAAQ,GAA/C,SAAMA,IAAA,QAAE,YAASA,IAAA;AACxB,YAAM,SAAS,OAAO,OAAO,IAAI,SAAA,aAAW;AAAI,iBAAA,kBAAkB,WAAW;QAAC,CAAA;AAC9E,YAAM,WAA0C,CAAA;AAChD,eAAO,QAAQ,SAAA,OAAK;AAAI,iBAAA,SAAS,MAAM,IAAI,IAAI;QAAK,CAAA;AACpD,eAAO;UACL,OAAO;UAEP,aAAa,GAAG,YAAY,KAAK,EAAE;UAEnC,OAAK,SAAC,MAAY;AAChB,gBAAM,SAAS,SAAS,IAAI;AAC5B,gBAAI,CAAC;AAAQ,oBAAM,IAAI,MAAM,UAAA,OAAU,MAAI,aAAA,CAAa;AACxD,mBAAO,SAAS,IAAI;;UAGtB,SAAS;UAET,SAAS,UAAU,WAAW;UAE9B;;MAGJ;ACnZA,eAAS,sBACP,WACA,aAA0C;AAC1C,eAAO,YAAY,OAAO,SAAC,MAAMA,KAAQ;cAAP,SAAMA,IAAA;AAAM,iBAAA,SAAA,SAAA,CAAA,GAAK,IAAI,GAAK,OAAO,IAAI,CAAC;WAAI,SAAS;MACvF;AAEA,eAAS,uBACP,aACA,OACAA,KACA,UAAwB;YADvB,cAAWA,IAAA;AAAA,QAAAA,IAAA;AAGZ,YAAM,SAAS,sBACb,aAAa,OAAO,aAAa,QAAQ,GACzC,YAAY,MAAM;AAKpB,eAAO;UACL;;MAEJ;eAEgB,yBAAyB,IAAW,UAAwB;AAC1E,YAAM,QAAQ,SAAS;AACvB,YAAM,SAAS,uBAAuB,GAAG,cAAc,OAAO,GAAG,OAAO,QAAQ;AAChF,WAAG,OAAO,OAAO;AACjB,WAAG,OAAO,QAAQ,SAAA,OAAK;AACrB,cAAM,YAAY,MAAM;AACxB,cAAI,GAAG,KAAK,OAAO,OAAO,KAAK,SAAA,KAAG;AAAI,mBAAA,IAAI,SAAS;UAAS,CAAA,GAAG;AAC7D,kBAAM,OAAO,GAAG,KAAK,MAAM,SAAS;AACpC,gBAAI,GAAG,SAAS,aAAa,GAAG,OAAO;AACnC,iBAAG,SAAS,EAAE,OAAO,MAAM;;;SAGlC;MACH;eC5BgB,cAAc,IAAW,MAAgB,YAAsB,UAAkB;AAC/F,mBAAW,QAAQ,SAAA,WAAS;AAC1B,cAAM,SAAS,SAAS,SAAS;AACjC,eAAK,QAAQ,SAAA,KAAG;AACd,gBAAM,WAAW,sBAAsB,KAAK,SAAS;AACrD,gBAAI,CAAC,YAAa,WAAW,YAAY,SAAS,UAAU,QAAY;AAEtE,kBAAI,QAAQ,GAAG,YAAY,aAAa,eAAe,GAAG,aAAa;AAGrE,wBAAQ,KAAK,WAAW;kBACtB,KAAG,WAAA;AAAsB,2BAAO,KAAK,MAAM,SAAS;kBAAE;kBACtD,KAAG,SAAC,OAAU;AAGZ,mCAAe,MAAM,WAAW,EAAC,OAAO,UAAU,MAAM,cAAc,MAAM,YAAY,KAAI,CAAC;;iBAEhG;qBACI;AAEL,oBAAI,SAAS,IAAI,IAAI,GAAG,MAAM,WAAW,MAAM;;;WAGpD;SACF;MACH;eAEgB,gBAAgB,IAAW,MAAc;AACvD,aAAK,QAAQ,SAAA,KAAG;AACd,mBAAS,OAAO,KAAK;AACnB,gBAAI,IAAI,GAAG,aAAa,GAAG;AAAO,qBAAO,IAAI,GAAG;;SAEnD;MACH;eAEgB,kBAAkB,GAAY,GAAU;AACtD,eAAO,EAAE,KAAK,UAAU,EAAE,KAAK;MACjC;eAEgB,aAAa,IAAW,YAAoB,iBAAiC,QAAM;AACjG,YAAM,eAAe,GAAG;AACxB,YAAI,gBAAgB,iBAAiB,SAAS,OAAO,KAAK,CAAC,aAAa,OAAO;AAC7E,uBAAa,QAAQ,kBAAkB,SAAS,iBAAiB,EAAE,EAAE,CAAC,GAAG,CAAA,CAAE;AAC3E,aAAG,YAAY,KAAK,OAAO;;AAE7B,YAAM,QAAQ,GAAG,mBAAmB,aAAa,GAAG,aAAa,YAAY;AAC7E,cAAM,OAAO,eAAe;AAC5B,cAAM,YAAY,MAAM,MAAM;AAC9B,YAAM,oBAAoB,MAAM,QAAQ,KAAK,KAAK;AAClD,YAAM,YAAY,IAAI,aAAa;AACnC,iBAAS,WAAA;AACP,cAAI,QAAQ;AACZ,cAAI,YAAY;AAChB,cAAI,eAAe,GAAG;AAEpB,iBAAK,YAAY,EAAE,QAAQ,SAAA,WAAS;AAClC,0BAAY,iBAAiB,WAAW,aAAa,SAAS,EAAE,SAAS,aAAa,SAAS,EAAE,OAAO;aACzG;AACD,qCAAyB,IAAI,eAAe;AAC5CU,yBAAQ,OAAO,WAAA;AAAM,qBAAA,GAAG,GAAG,SAAS,KAAK,KAAK;YAAC,CAAA,EAAE,MAAM,iBAAiB;iBACnE;AACL,qCAAyB,IAAI,eAAe;AAC5C,mBAAO,mBAAmB,IAAI,OAAO,UAAU,EAC5C,KAAK,SAAAwB,aAAU;AAAI,qBAAA,uBAAuB,IAAIA,aAAY,OAAO,eAAe;YAAC,CAAA,EACjF,MAAM,iBAAiB;;SAE7B;MACH;eAIgB,oBAAoB,IAAW,iBAA+B;AAC5E,4BAAoB,GAAG,WAAW,eAAe;AACjD,YAAI,gBAAgB,GAAG,UAAU,OAAO,KAAK,CAAC,gBAAgB,iBAAiB,SAAS,OAAO,GAAG;AAKhG,0BAAgB,GAAG,kBAAkB,OAAO,EAAE,IAAI,KAAK,KAAM,gBAAgB,GAAG,UAAU,KAAM,CAAC,GAAG,SAAS;;AAE/G,YAAM,eAAe,kBAAkB,IAAI,GAAG,OAAO,eAAe;AACpE,mCAA2B,IAAI,GAAG,WAAW,eAAe;AAC5D,YAAM,OAAO,cAAc,cAAc,GAAG,SAAS;+BAC1CC,cAAW;AACpB,cAAIA,aAAY,OAAO,UAAUA,aAAY,UAAU;AACrD,oBAAQ,KAAK,oCAAA,OAAoCA,aAAY,MAAI,8DAAA,CAA8D;;;AAGjI,cAAM,QAAQ,gBAAgB,YAAYA,aAAY,IAAI;AAC1D,UAAAA,aAAY,IAAI,QAAQ,SAAA,KAAG;AACzB,gBAAI;AAAO,sBAAQ,MAAM,+CAAA,OAA+CA,aAAY,MAAI,GAAA,EAAA,OAAI,IAAI,GAAG,CAAE;AACrG,qBAAS,OAAO,GAAG;WACpB;;AATH,iBAA0B,KAAA,GAAAnC,MAAA,KAAK,QAAL,KAAAA,IAAA,QAAA,MAAW;AAAhC,cAAM,cAAWA,IAAA,EAAA;gCAAX,WAAW;;;;MAWxB;AAEA,eAAS,mBAAmB,IAAW,OAAoB,YAAkB;AAM3E,YAAI,MAAM,WAAW,SAAS,OAAO,GAAG;AACtC,iBAAO,MAAM,MAAM,OAAO,EAAE,IAAI,SAAS,EAAE,KAAK,SAAA,aAAW;AACzD,mBAAO,eAAe,OAAO,cAAc;WAC5C;eACI;AACL,iBAAOU,aAAQ,QAAQ,UAAU;;MAErC;AAEA,eAAS,uBACP,IACA,YACA,OACA,iBAA+B;AAI/B,YAAM,QAA4B,CAAA;AAClC,YAAM,WAAW,GAAG;AACpB,YAAI,eAAe,GAAG,YAAY,kBAAkB,IAAI,GAAG,OAAO,eAAe;AAGjF,YAAM,YAAY,SAAS,OAAO,SAAA,GAAC;AAAI,iBAAA,EAAE,KAAK,WAAW;QAAU,CAAA;AACnE,YAAI,UAAU,WAAW,GAAG;AAU1B,iBAAOA,aAAQ,QAAO;;AAGxB,kBAAU,QAAQ,SAAA,SAAO;AACvB,gBAAM,KAAK,WAAA;AACT,gBAAM,YAAY;AAClB,gBAAM,YAAY,QAAQ,KAAK;AAC/B,uCAA2B,IAAI,WAAW,eAAe;AACzD,uCAA2B,IAAI,WAAW,eAAe;AAEzD,2BAAe,GAAG,YAAY;AAE9B,gBAAM,OAAO,cAAc,WAAW,SAAS;AAE/C,iBAAK,IAAI,QAAQ,SAAA,OAAK;AACpB,0BAAY,iBAAiB,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,OAAO;aAC1E;AAED,iBAAK,OAAO,QAAQ,SAAA,QAAM;AACxB,kBAAI,OAAO,UAAU;AACnB,sBAAM,IAAI,WAAW,QAAQ,0CAA0C;qBAClE;AACL,oBAAM,UAAQ,gBAAgB,YAAY,OAAO,IAAI;AAErD,uBAAO,IAAI,QAAQ,SAAA,KAAG;AAAI,yBAAA,SAAS,SAAO,GAAG;gBAAC,CAAA;AAE9C,uBAAO,OAAO,QAAQ,SAAA,KAAG;AACvB,0BAAM,YAAY,IAAI,IAAI;AAC1B,2BAAS,SAAO,GAAG;iBACpB;AAED,uBAAO,IAAI,QAAQ,SAAA,SAAO;AAAI,yBAAA,QAAM,YAAY,OAAO;gBAAC,CAAA;;aAE3D;AAED,gBAAM,iBAAiB,QAAQ,KAAK;AAEpC,gBAAI,kBAAkB,QAAQ,KAAK,UAAU,YAAY;AAEvD,uCAAyB,IAAI,eAAe;AAC5C,oBAAM,kBAAkB,CAAA;AAKxB,kBAAI,kBAAgB,aAAa,SAAS;AAC1C,mBAAK,IAAI,QAAQ,SAAA,OAAK;AACpB,gCAAc,KAAK,IAAI,UAAU,KAAK;eACvC;AAMD,8BAAgB,IAAI,CAAC,GAAG,YAAY,SAAS,CAAC;AAC9C,4BAAc,IAAI,CAAC,GAAG,YAAY,SAAS,GAAG,KAAK,eAAa,GAAG,eAAa;AAChF,oBAAM,SAAS;AAGf,kBAAM,0BAAwB,gBAAgB,cAAc;AAC5D,kBAAI,yBAAuB;AACzB,wCAAuB;;AAGzB,kBAAI;AACJ,kBAAM,kBAAkBA,aAAQ,OAAO,WAAA;AAErC,gCAAc,eAAe,KAAK;AAClC,oBAAI,eAAa;AACf,sBAAI,yBAAuB;AAEzB,wBAAI,cAAc,wBAAwB,KAAK,MAAM,IAAI;AACzD,kCAAY,KAAK,aAAa,WAAW;;;eAG9C;AACD,qBAAQ,iBAAe,OAAO,cAAY,SAAS,aACjDA,aAAQ,QAAQ,aAAW,IAAI,gBAAgB,KAAK,WAAA;AAAI,uBAAA;cAAW,CAAA;;WAExE;AACD,gBAAM,KAAK,SAAA,UAAQ;AACjB,gBAAM,YAAY,QAAQ,KAAK;AAE/B,gCAAoB,WAAW,QAAQ;AAEvC,4BAAgB,IAAI,CAAC,GAAG,YAAY,SAAS,CAAC;AAC9C,0BAAc,IAAI,CAAC,GAAG,YAAY,SAAS,GAAG,GAAG,aAAa,GAAG,SAAS;AAC1E,kBAAM,SAAS,GAAG;WACnB;AAED,gBAAM,KAAK,SAAA,UAAQ;AACjB,gBAAI,GAAG,MAAM,iBAAiB,SAAS,OAAO,GAAG;AAC/C,kBAAI,KAAK,KAAK,GAAG,MAAM,UAAU,EAAE,MAAM,QAAQ,KAAK,SAAS;AAE7D,mBAAG,MAAM,kBAAkB,OAAO;AAClC,uBAAO,GAAG,UAAU;AACpB,mBAAG,cAAc,GAAG,YAAY,OAAO,SAAA,MAAI;AAAI,yBAAA,SAAS;gBAAO,CAAA;qBAC1D;AAGL,yBAAS,YAAY,OAAO,EAAE,IAAI,QAAQ,KAAK,SAAS,SAAS;;;WAGtE;SACF;AAGD,iBAAS,WAAQ;AACf,iBAAO,MAAM,SAASA,aAAQ,QAAQ,MAAM,MAAK,EAAG,MAAM,QAAQ,CAAC,EAAE,KAAK,QAAQ,IAChFA,aAAQ,QAAO;;AAGnB,eAAO,SAAQ,EAAG,KAAK,WAAA;AACrB,8BAAoB,cAAc,eAAe;SAClD;MACH;eAgBgB,cAAc,WAAqB,WAAmB;AACpE,YAAM,OAAmB;UACvB,KAAK,CAAA;UACL,KAAK,CAAA;UACL,QAAQ,CAAA;;AAEV,YAAI;AACJ,aAAK,SAAS,WAAW;AACvB,cAAI,CAAC,UAAU,KAAK;AAAG,iBAAK,IAAI,KAAK,KAAK;;AAE5C,aAAK,SAAS,WAAW;AACvB,cAAM,SAAS,UAAU,KAAK,GAC5B,SAAS,UAAU,KAAK;AAC1B,cAAI,CAAC,QAAQ;AACX,iBAAK,IAAI,KAAK,CAAC,OAAO,MAAM,CAAC;iBACxB;AACL,gBAAM,SAAS;cACb,MAAM;cACN,KAAK;cACL,UAAU;cACV,KAAK,CAAA;cACL,KAAK,CAAA;cACL,QAAQ,CAAA;;AAEV,gBAIM,MAAI,OAAO,QAAQ,WAAS,QAE5B,MAAI,OAAO,QAAQ,WAAS,OAG7B,OAAO,QAAQ,SAAS,OAAO,QAAQ,MAC5C;AAEE,qBAAO,WAAW;AAClB,mBAAK,OAAO,KAAK,MAAM;mBAClB;AAEL,kBAAM,aAAa,OAAO;AAC1B,kBAAM,aAAa,OAAO;AAC1B,kBAAI,UAAO;AACX,mBAAK,WAAW,YAAY;AAC1B,oBAAI,CAAC,WAAW,OAAO;AAAG,yBAAO,IAAI,KAAK,OAAO;;AAEnD,mBAAK,WAAW,YAAY;AAC1B,oBAAM,SAAS,WAAW,OAAO,GAC/B,SAAS,WAAW,OAAO;AAC7B,oBAAI,CAAC;AAAQ,yBAAO,IAAI,KAAK,MAAM;yBAC1B,OAAO,QAAQ,OAAO;AAAK,yBAAO,OAAO,KAAK,MAAM;;AAE/D,kBAAI,OAAO,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,OAAO,OAAO,SAAS,GAAG;AAC9E,qBAAK,OAAO,KAAK,MAAM;;;;;AAK/B,eAAO;MACT;eAEgB,YACd,UACA,WACA,SACA,SAAoB;AAEpB,YAAM,QAAQ,SAAS,GAAG,kBACxB,WACA,QAAQ,UACN,EAAE,SAAS,QAAQ,SAAS,eAAe,QAAQ,KAAI,IACvD,EAAE,eAAe,QAAQ,KAAI,CAAE;AAEnC,gBAAQ,QAAQ,SAAA,KAAG;AAAI,iBAAA,SAAS,OAAO,GAAG;QAAC,CAAA;AAC3C,eAAO;MACT;eAEgB,oBAAoB,WAAqB,UAAwB;AAC/E,aAAK,SAAS,EAAE,QAAQ,SAAA,WAAS;AAC/B,cAAI,CAAC,SAAS,GAAG,iBAAiB,SAAS,SAAS,GAAG;AACrD,gBAAI;AAAO,sBAAQ,MAAM,iCAAiC,SAAS;AACnE,wBAAY,UAAU,WAAW,UAAU,SAAS,EAAE,SAAS,UAAU,SAAS,EAAE,OAAO;;SAE9F;MACH;eAEgB,oBAAoB,WAAqB,UAAwB;AAC/E,SAAA,EAAG,MAAM,KAAK,SAAS,GAAG,gBAAgB,EAAE,QAAQ,SAAA,WAAS;AAC3D,iBAAA,UAAU,SAAS,KAAK,QAAQ,SAAS,GAAG,kBAAkB,SAAS;SAAC;MAC5E;eAEgB,SAAS,OAAuB,KAAc;AAC5D,cAAM,YAAY,IAAI,MAAM,IAAI,SAAS,EAAE,QAAQ,IAAI,QAAQ,YAAY,IAAI,MAAK,CAAE;MACxF;AAEA,eAAS,kBACP,IACA,OACA,UAAwB;AAExB,YAAM,eAAe,CAAA;AACrB,YAAM,eAAe,MAAM,MAAM,kBAAkB,CAAC;AACpD,qBAAa,QAAQ,SAAA,WAAS;AAC5B,cAAM,QAAQ,SAAS,YAAY,SAAS;AAC5C,cAAI,UAAU,MAAM;AACpB,cAAM,UAAU,gBACd,gBAAgB,OAAO,GACvB,WAAW,IACX,MACA,OACA,CAAC,CAAC,MAAM,eACR,WAAW,OAAO,YAAY,UAC9B,IAAI;AAEN,cAAM,UAAuB,CAAA;AAC7B,mBAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,EAAE,GAAG;AAChD,gBAAM,WAAW,MAAM,MAAM,MAAM,WAAW,CAAC,CAAC;AAChD,sBAAU,SAAS;AACnB,gBAAI,QAAQ,gBACV,SAAS,MACT,SACA,CAAC,CAAC,SAAS,QACX,CAAC,CAAC,SAAS,YACX,OACA,WAAW,OAAO,YAAY,UAC9B,KAAK;AAEP,oBAAQ,KAAK,KAAK;;AAEpB,uBAAa,SAAS,IAAI,kBAAkB,WAAW,SAAS,OAAO;SACxE;AACD,eAAO;MACT;eAEgB,iBAAiB,IAAW,OAAoB,UAAwB;AACtF,WAAG,QAAQ,MAAM,UAAU;AAC3B,YAAM,eAAe,GAAG,YAAY,kBAAkB,IAAI,OAAO,QAAQ;AACzE,WAAG,cAAc,MAAM,MAAM,kBAAkB,CAAC;AAChD,sBAAc,IAAI,CAAC,GAAG,UAAU,GAAG,KAAK,YAAY,GAAG,YAAY;MACrE;eAEgB,sBAAsB,IAAW,UAAwB;AACvE,YAAM,kBAAkB,kBAAkB,IAAI,GAAG,OAAO,QAAQ;AAChE,YAAM,OAAO,cAAc,iBAAiB,GAAG,SAAS;AACxD,eAAO,EAAE,KAAK,IAAI,UAAU,KAAK,OAAO,KAAK,SAAA,IAAE;AAAI,iBAAA,GAAG,IAAI,UAAU,GAAG,OAAO;QAAM,CAAA;MACtF;eAEgB,2BAA2B,IAAW,QAAkB,UAAwB;AAE9F,YAAM,aAAa,SAAS,GAAG;AAE/B,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,cAAM,YAAY,WAAW,CAAC;AAC9B,cAAM,QAAQ,SAAS,YAAY,SAAS;AAC5C,aAAG,aAAa,YAAY;AAE5B,mBAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,EAAE,GAAG;AAChD,gBAAM,YAAY,MAAM,WAAW,CAAC;AACpC,gBAAM,UAAU,MAAM,MAAM,SAAS,EAAE;AACvC,gBAAM,YAAY,OAAO,YAAY,WAAW,UAAU,MAAM,MAAM,OAAO,EAAE,KAAK,GAAG,IAAI;AAC3F,gBAAI,OAAO,SAAS,GAAG;AACrB,kBAAM,YAAY,OAAO,SAAS,EAAE,UAAU,SAAS;AACvD,kBAAI,WAAW;AACb,0BAAU,OAAO;AACjB,uBAAO,OAAO,SAAS,EAAE,UAAU,SAAS;AAC5C,uBAAO,SAAS,EAAE,UAAU,SAAS,IAAI;;;;;AAOjD,YAAI,OAAO,cAAc,eAAe,SAAS,KAAK,UAAU,SAAS,KACvE,CAAC,oBAAoB,KAAK,UAAU,SAAS,KAC7C,QAAQ,qBAAqB,mBAAmB,QAAQ,qBACxD,CAAA,EAAG,OAAO,UAAU,UAAU,MAAM,eAAe,CAAC,EAAE,CAAC,IAAI,KAC7D;AACE,aAAG,aAAa;;MAEpB;eAEgB,iBAAiB,mBAAyB;AACxD,eAAO,kBAAkB,MAAM,GAAG,EAAE,IAAI,SAAC,OAAO,UAAQ;AACtD,kBAAQ,MAAM,KAAI;AAClB,cAAM,OAAO,MAAM,QAAQ,gBAAgB,EAAE;AAE7C,cAAM,UAAU,MAAM,KAAK,IAAI,IAAI,KAAK,MAAM,YAAY,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;AAE5E,iBAAO,gBACL,MACA,WAAW,MACX,KAAK,KAAK,KAAK,GACf,KAAK,KAAK,KAAK,GACf,OAAO,KAAK,KAAK,GACjB,QAAQ,OAAO,GACf,aAAa,CAAC;SAEjB;MACH;ACldA,UAAA,UAAA,WAAA;AAAA,iBAAA0B,WAAA;;AAUE,QAAAA,SAAA,UAAA,mBAAA,SAAiB,QAAgD,WAAmB;AAClF,eAAK,MAAM,EAAE,QAAQ,SAAA,WAAS;AAC5B,gBAAI,OAAO,SAAS,MAAM,MAAM;AAC5B,kBAAI,UAAU,iBAAiB,OAAO,SAAS,CAAC;AAChD,kBAAI,UAAU,QAAQ,MAAK;AAC3B,sBAAQ,SAAS;AACjB,kBAAI,QAAQ;AAAO,sBAAM,IAAI,WAAW,OAAO,oCAAoC;AACnF,sBAAQ,QAAQ,SAAA,KAAG;AACf,oBAAI,IAAI;AAAM,wBAAM,IAAI,WAAW,OAAO,sDAAsD;AAChG,oBAAI,CAAC,IAAI;AAAS,wBAAM,IAAI,WAAW,OAAO,sDAAsD;eACvG;AACD,wBAAU,SAAS,IAAI,kBAAkB,WAAW,SAAS,OAAO;;WAEzE;;AAGH,QAAAA,SAAA,UAAA,SAAA,SAAO,QAAyC;AAC9C,cAAM,KAAK,KAAK;AAChB,eAAK,KAAK,eAAe,KAAK,KAAK,eACjC,OAAO,KAAK,KAAK,cAAc,MAAM,IACrC;AACF,cAAM,WAAW,GAAG;AAGpB,cAAM,aAAyC,CAAA;AAC/C,cAAI,WAAW,CAAA;AACf,mBAAS,QAAQ,SAAA,SAAO;AACtB,mBAAO,YAAY,QAAQ,KAAK,YAAY;AAC5C,uBAAY,QAAQ,KAAK,WAAW,CAAA;AACpC,oBAAQ,iBAAiB,YAAY,QAAQ;WAC9C;AAED,aAAG,YAAY;AAEf,0BAAgB,IAAI,CAAC,GAAG,YAAY,IAAI,GAAG,YAAY,SAAS,CAAC;AACjE,wBAAc,IAAI,CAAC,GAAG,YAAY,IAAI,GAAG,YAAY,WAAW,KAAK,KAAK,MAAM,GAAG,KAAK,QAAQ,GAAG,QAAQ;AAC3G,aAAG,cAAc,KAAK,QAAQ;AAC9B,iBAAO;;AAGT,QAAAA,SAAA,UAAA,UAAA,SAAQ,iBAAgE;AACtE,eAAK,KAAK,iBAAiB,gBAAgB,KAAK,KAAK,kBAAkB,KAAK,eAAe;AAC3F,iBAAO;;AAEX,eAAAA;MAAA,EAAC;eCrDe,yBAAyB,IAAS;AAChD,eAAO,qBACL,QAAQ,WAER,SAASA,SAAuB,eAAqB;AACnD,eAAK,KAAK;AACV,eAAK,OAAO;YACV,SAAS;YACT,cAAc;YACd,UAAU,CAAA;YACV,QAAQ,CAAA;YACR,gBAAgB;;SAEnB;MAEL;ACtBA,eAAS,gBAAgBC,YAAuB,aAA2B;AACzE,YAAI,YAAYA,WAAU,YAAY;AACtC,YAAI,CAAC,WAAW;AACd,sBAAYA,WAAU,YAAY,IAAI,IAAIC,QAAM,YAAY;YAC1D,QAAQ,CAAA;YACR,WAASD;YACT;WACD;AACD,oBAAU,QAAQ,CAAC,EAAE,OAAO,EAAE,SAAS,OAAM,CAAE;;AAEjD,eAAO,UAAU,MAAM,SAAS;MAClC;AAEA,eAAS,mBAAmBA,YAAqB;AAC/C,eAAOA,cAAa,OAAOA,WAAU,cAAc;MACrD;eAEgB,iBAAiBrC,KAGV;YAFrBqC,aAASrC,IAAA,WACT,cAAWA,IAAA;AAEX,eAAO,mBAAmBqC,UAAS,IAC/B,QAAQ,QAAQA,WAAU,UAAS,CAAE,EAAE,KAAK,SAAC,OAAK;AAChD,iBAAA,MAEG,IAAI,SAAC,MAAI;AAAK,mBAAA,KAAK;UAAI,CAAA,EAEvB,OAAO,SAAC,MAAI;AAAK,mBAAA,SAAS;UAAU,CAAA;SAAC,IAE1C,gBAAgBA,YAAW,WAAW,EAAE,aAAY,EAAG,YAAW;MACxE;eAEgB,mBACdrC,KACA,MAAY;YADVqC,aAASrC,IAAA,WAAE,cAAWA,IAAA;AAGxB,SAAC,mBAAmBqC,UAAS,KAC3B,SAAS,cACT,gBAAgBA,YAAW,WAAW,EAAE,IAAI,EAAC,KAAI,CAAC,EAAE,MAAM,GAAG;MACjE;eAEgB,mBACdrC,KACA,MAAY;YADVqC,aAASrC,IAAA,WAAE,cAAWA,IAAA;AAGxB,SAAC,mBAAmBqC,UAAS,KAC3B,SAAS,cACT,gBAAgBA,YAAW,WAAW,EAAE,OAAO,IAAI,EAAE,MAAM,GAAG;MAClE;eCrDgB,IAAK,IAAE;AASrB,eAAO,SAAS,WAAA;AACd,cAAI,aAAa;AACjB,iBAAO,GAAE;SACV;MACH;ACVA,eAAS,WAAW;AAChB,YAAI,WAAW,CAAC,UAAU,iBACtB,WAAW,KAAK,UAAU,SAAS,KACnC,CAAC,iBAAiB,KAAK,UAAU,SAAS;AAE9C,YAAI,CAAC,YAAY,CAAC,UAAU;AACxB,iBAAO,QAAQ,QAAO;AAC1B,YAAI;AACJ,eAAO,IAAI,QAAQ,SAAU,SAAS;AAClC,cAAI,SAAS,WAAY;AAAE,mBAAO,UAAU,UAAS,EAAG,QAAQ,OAAO;UAAE;AACzE,uBAAa,YAAY,QAAQ,GAAG;AACpC,iBAAM;QACd,CAAK,EAAE,QAAQ,WAAY;AAAE,iBAAO,cAAc,UAAU;QAAE,CAAE;MAChE;;ACFA,eAAS,aAAa,MAA6D;AACjF,eAAO,EAAE,UAAU;MACrB;AAIO,UAAME,YAAW,SAAS,YAAiB,IAAQ;AACxD,YAAI,MAAM;AAER,iBAAO,MAAM,UAAU,SAAS,EAAC,GAAE,GAAG,MAAM,YAAY,IAAI,UAAU,SAAS,IAAI,KAAK,WAAU,IAAI,EAAC,GAAE,EAAC,CAAC;eACtG;AAEL,cAAM,KAAK,IAAIA,UAAQ;AACvB,cAAI,cAAe,OAAO,YAAa;AACrC,mBAAO,IAAI,UAAU;;AAEvB,iBAAO;;MAEX;AAEA,YAAMA,UAAS,YAAS,KAAA;QACtB,KAAG,SAAC,UAAiE;AACnE,UAAAC,aAAY,MAAM,QAAQ;AAC1B,iBAAO;;QAET,QAAM,SAAC,KAAkB;AACvB,mBAAS,MAAM,KAAK,GAAG;AACvB,iBAAO;;QAET,SAAO,SAAC7B,OAAqB;AAA7B,cAAA,QAAA;AACE,UAAAA,MAAK,QAAQ,SAAA,KAAG;AAAI,mBAAA,SAAS,OAAM,KAAK,GAAG;UAAC,CAAA;AAC5C,iBAAO;;QAET,QAAM,SAAC,KAAkB;AACvB,cAAM,OAAO,oBAAoB,IAAI,EAAE,KAAK,GAAG,EAAE;AACjD,iBAAO,QAAQL,KAAI,KAAK,MAAM,GAAG,KAAK,KAAKA,KAAI,KAAK,IAAI,GAAG,KAAK;;SAGlE,GAAC,cAAc,IAAf,WAAA;AACE,eAAO,oBAAoB,IAAI;;AAInC,eAAS,SAAS,QAAsB,MAAqB,IAAiB;AAC5E,YAAM,OAAOA,KAAI,MAAM,EAAE;AAGzB,YAAI,MAAM,IAAI;AAAG;AAGjB,YAAI,OAAO;AAAG,gBAAM,WAAU;AAE9B,YAAI,aAAa,MAAM;AAAG,iBAAO,OAAO,QAAQ,EAAE,MAAM,IAAI,GAAG,EAAC,CAAE;AAClE,YAAM,OAAO,OAAO;AACpB,YAAM,QAAQ,OAAO;AACrB,YAAIA,KAAI,IAAI,OAAO,IAAI,IAAI,GAAG;AAC5B,iBACI,SAAS,MAAM,MAAM,EAAE,IACtB,OAAO,IAAI,EAAE,MAAM,IAAI,GAAG,GAAG,GAAG,MAAM,GAAG,KAAI;AAClD,iBAAO,UAAU,MAAM;;AAEzB,YAAIA,KAAI,MAAM,OAAO,EAAE,IAAI,GAAG;AAC5B,kBACI,SAAS,OAAO,MAAM,EAAE,IACvB,OAAO,IAAI,EAAE,MAAM,IAAI,GAAG,GAAG,GAAG,MAAM,GAAG,KAAI;AAClD,iBAAO,UAAU,MAAM;;AAKzB,YAAIA,KAAI,MAAM,OAAO,IAAI,IAAI,GAAG;AAC9B,iBAAO,OAAO;AACd,iBAAO,IAAI;AACX,iBAAO,IAAI,QAAQ,MAAM,IAAI,IAAI;;AAGnC,YAAIA,KAAI,IAAI,OAAO,EAAE,IAAI,GAAG;AAC1B,iBAAO,KAAK;AACZ,iBAAO,IAAI;AACX,iBAAO,IAAI,OAAO,IAAI,OAAO,EAAE,IAAI,IAAI;;AAEzC,YAAM,iBAAiB,CAAC,OAAO;AAE/B,YAAI,QAAQ,CAAC,OAAO,GAAG;AAGrB,UAAAkC,aAAY,QAAQ,IAAI;;AAG1B,YAAI,SAAS,gBAAgB;AAG3B,UAAAA,aAAY,QAAQ,KAAK;;MAE7B;eAEgBA,aAAY,QAAsB,QAA+D;AAC/G,iBAAS,aACPC,SACAzC,KAA6G;cAA3G,OAAIA,IAAA,MAAE,KAAEA,IAAA,IAAE,IAACA,IAAA,GAAE,IAACA,IAAA;AAEhB,mBAASyC,SAAQ,MAAM,EAAE;AACzB,cAAI;AAAG,yBAAaA,SAAQ,CAAC;AAC7B,cAAI;AAAG,yBAAaA,SAAQ,CAAC;;AAG/B,YAAG,CAAC,aAAa,MAAM;AAAG,uBAAa,QAAQ,MAAM;MACvD;eAEgBC,eACd,WACA,WAAuB;AAGrB,YAAM,KAAK,oBAAoB,SAAS;AACxC,YAAI,cAAc,GAAG,KAAI;AACzB,YAAI,YAAY;AAAM,iBAAO;AAC7B,YAAI,IAAI,YAAY;AAGpB,YAAM,KAAK,oBAAoB,SAAS;AACxC,YAAI,cAAc,GAAG,KAAK,EAAE,IAAI;AAChC,YAAI,IAAI,YAAY;AAEpB,eAAO,CAAC,YAAY,QAAQ,CAAC,YAAY,MAAM;AAC7C,cAAIpC,KAAI,EAAG,MAAM,EAAE,EAAE,KAAK,KAAKA,KAAI,EAAG,IAAI,EAAE,IAAI,KAAK;AAAG,mBAAO;AAC/D,UAAAA,KAAI,EAAE,MAAM,EAAG,IAAI,IAAI,IAClB,KAAK,cAAc,GAAG,KAAK,EAAG,IAAI,GAAG,QACrC,KAAK,cAAc,GAAG,KAAK,EAAE,IAAI,GAAG;;AAE7C,eAAO;MACT;eAUgB,oBACd,MAAmC;AAEnC,YAAI,QAA+B,aAAa,IAAI,IAAI,OAAO,EAAE,GAAG,GAAG,GAAG,KAAI;AAE9E,eAAO;UACL,MAAI,SAAC,KAAI;AACP,gBAAM,cAAc,UAAU,SAAS;AACvC,mBAAO,OAAO;AACZ,sBAAQ,MAAM,GAAC;gBACb,KAAK;AAGH,wBAAM,IAAI;AACV,sBAAI,aAAa;AACf,2BAAO,MAAM,EAAE,KAAKA,KAAI,KAAK,MAAM,EAAE,IAAI,IAAI;AAC3C,8BAAQ,EAAE,IAAI,OAAO,GAAG,MAAM,EAAE,GAAG,GAAG,EAAC;yBACpC;AACL,2BAAO,MAAM,EAAE;AAAG,8BAAQ,EAAE,IAAI,OAAO,GAAG,MAAM,EAAE,GAAG,GAAG,EAAC;;gBAG7D,KAAK;AAEH,wBAAM,IAAI;AACV,sBAAI,CAAC,eAAeA,KAAI,KAAK,MAAM,EAAE,EAAE,KAAK;AAC1C,2BAAO,EAAE,OAAO,MAAM,GAAG,MAAM,MAAK;gBACxC,KAAK;AAEH,sBAAI,MAAM,EAAE,GAAG;AACb,0BAAM,IAAI;AACV,4BAAQ,EAAE,IAAI,OAAO,GAAG,MAAM,EAAE,GAAG,GAAG,EAAC;AACvC;;gBAGJ,KAAK;AACH,0BAAQ,MAAM;;;AAGpB,mBAAO,EAAE,MAAM,KAAI;;;MAGzB;AAEA,eAAS,UAAU,QAAwB;;AACzC,YAAM,UAAQN,MAAA,OAAO,OAAC,QAAAA,QAAA,SAAA,SAAAA,IAAE,MAAK,QAAM,KAAA,OAAO,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,MAAK;AAClD,YAAM,IAAI,OAAO,IAAI,MAAM,OAAO,KAAK,MAAM;AAC7C,YAAI,GAAG;AAsBL,cAAM,IAAI,MAAM,MAAM,MAAM;AAC5B,cAAM,YAAS,SAAA,CAAA,GAAQ,MAAM;AAI7B,cAAM,eAAe,OAAO,CAAC;AAC7B,iBAAO,OAAO,aAAa;AAC3B,iBAAO,KAAK,aAAa;AACzB,iBAAO,CAAC,IAAI,aAAa,CAAC;AAC1B,oBAAU,CAAC,IAAI,aAAa,CAAC;AAC7B,iBAAO,CAAC,IAAI;AACZ,oBAAU,IAAI,aAAa,SAAS;;AAEtC,eAAO,IAAI,aAAa,MAAM;MAChC;AAEA,eAAS,aAAaA,KAA2C;YAAzC,IAACA,IAAA,GAAE,IAACA,IAAA;AAC1B,gBAAQ,IAAK,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,IAAK,IAAI,EAAE,IAAI,KAAK;MAC9D;eChPgB,uBACd,QACA,QAAwB;AAExB,aAAK,MAAM,EAAE,QAAQ,SAAA,MAAI;AACvB,cAAI,OAAO,IAAI;AAAG,YAAAwC,aAAY,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC;;AACnD,mBAAO,IAAI,IAAI,sBAAsB,OAAO,IAAI,CAAC;SACvD;AACD,eAAO;MACT;eCVgB,eAAe,KAAuB,KAAqB;AACzE,eAAO,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,GAAG,EAAE,KAC5C,SAAC,KAAG;AAAK,iBAAA,IAAI,GAAG,KAAKE,eAAc,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;QAAC,CAAA;MAE1D;ACLO,UAAM,QAA0B,CAAA;ACIvC,UAAI,kBAAoC,CAAA;AACxC,UAAI,iBAAiB;eAEL,wBAAwB,MAAwB,YAAkB;AAChF,+BAAuB,iBAAiB,IAAI;AAC5C,YAAI,CAAC,gBAAgB;AACnB,2BAAiB;AACjB,qBAAW,WAAA;AACT,6BAAiB;AACjB,gBAAM,QAAQ;AACd,8BAAkB,CAAA;AAClB,iCAAqB,OAAO,KAAK;aAChC,CAAC;;MAER;eAEgB,qBACd,cACA,4BAAkC;AAAlC,YAAA,+BAAA,QAAA;AAAA,uCAAA;QAAkC;AAElC,YAAM,kBAAkB,oBAAI,IAAG;AAC/B,YAAI,aAAa,KAAK;AAEpB,mBAAuB,KAAA,GAAA1C,MAAA,OAAO,OAAO,KAAK,GAAnB,KAAAA,IAAA,QAAA,MAAsB;AAAxC,gBAAM,WAAQA,IAAA,EAAA;AACjB,oCACE,UACA,cACA,iBACA,0BAA0B;;eAGzB;AACL,mBAAW,OAAO,cAAc;AAC9B,gBAAM,QAAQ,yBAAyB,KAAK,GAAG;AAC/C,gBAAI,OAAO;AACA,kBAAA,SAAqB,MAAK,CAAA,GAAlB,YAAa,MAAK,CAAA;AACnC,kBAAM,WAAW,MAAM,SAAA,OAAS,QAAM,GAAA,EAAA,OAAI,SAAS,CAAE;AACrD,kBAAI;AACF,wCACE,UACA,cACA,iBACA,0BAA0B;;;;AAMpC,wBAAgB,QAAQ,SAAC,SAAO;AAAK,iBAAA,QAAO;QAAE,CAAA;MAChD;AAEA,eAAS,wBACP,UACA,cACA,oBACA,4BAAmC;AAEnC,YAAM,oBAA8C,CAAA;AACpD,iBAAmC,KAAA,GAAAA,MAAA,OAAO,QAAQ,SAAS,QAAQ,KAAK,GAArC,KAAAA,IAAA,QAAA,MAAwC;AAAhE,cAAA,KAAAA,IAAA,EAAA,GAAC,YAAS,GAAA,CAAA,GAAE,UAAO,GAAA,CAAA;AAC5B,cAAM,kBAAgC,CAAA;AACtC,mBAAoB,KAAA,GAAA,YAAA,SAAA,KAAA,UAAA,QAAA,MAAS;AAAxB,gBAAM,QAAK,UAAA,EAAA;AACd,gBAAI,eAAe,cAAc,MAAM,MAAM,GAAG;AAG9C,oBAAM,YAAY,QAAQ,SAAC,SAAO;AAAK,uBAAA,mBAAmB,IAAI,OAAO;cAAC,CAAA;uBAC7D,4BAA4B;AACrC,8BAAgB,KAAK,KAAK;;;AAI9B,cAAI;AACF,8BAAkB,KAAK,CAAC,WAAW,eAAe,CAAC;;AAEvD,YAAI,4BAA4B;AAC9B,mBAA2C,KAAA,GAAA,sBAAA,mBAAA,KAAA,oBAAA,QAAA,MAAmB;AAAnD,gBAAA,KAAA,oBAAA,EAAA,GAAC,YAAS,GAAA,CAAA,GAAE,kBAAe,GAAA,CAAA;AACpC,qBAAS,QAAQ,MAAM,SAAS,IAAI;;;MAG1C;eChEgB,UAAW,IAAS;AAClC,YAAM,QAAQ,GAAG;AACV,YAAAqC,aAAa,GAAG,MAAK;AAC5B,YAAI,MAAM,iBAAiB,GAAG;AAC1B,iBAAO,MAAM,eAAe,KAAY,WAAA;AAAM,mBAAA,MAAM,cAClD,UAAW,MAAM,WAAW,IAC5B;UAAE,CAAA;AACR,cAAM,gBAAgB;AACtB,cAAM,cAAc;AACpB,cAAM,eAAe;AACrB,YAAM,gBAAgB,MAAM;AAC5B,YAAI,kBAAkB,KAAK,MAAM,GAAG,QAAQ,EAAE;AAC9C,YAAI,kBAAkB;AAEtB,iBAAS,mBAAgB;AAGvB,cAAI,MAAM,kBAAkB;AAAe,kBAAM,IAAI,WAAW,eAAe,yBAAyB;;AAI1G,YAAI,iBAAiB,MAAM,gBAEvB,qBAA8C,MAC9C,aAAa;AAEjB,YAAM,YAAY,WAAA;AAAM,iBAAA,IAAI3B,aAAQ,SAAC,SAAS,QAAM;AAClD,6BAAgB;AAEhB,gBAAI,CAAC2B;AAAW,oBAAM,IAAI,WAAW,WAAU;AAC/C,gBAAM,SAAS,GAAG;AAElB,gBAAM,MAAM,MAAM,cAAc,CAAC,kBAC/BA,WAAU,KAAK,MAAM,IACrBA,WAAU,KAAK,QAAQ,eAAe;AACxC,gBAAI,CAAC;AAAK,oBAAM,IAAI,WAAW,WAAU;AACzC,gBAAI,UAAU,mBAAmB,MAAM;AACvC,gBAAI,YAAY,KAAK,GAAG,cAAc;AACtC,gBAAI,kBAAkB,KAAM,SAAA,GAAC;AACzB,mCAAqB,IAAI;AACzB,kBAAI,MAAM,cAAc,CAAC,GAAG,SAAS,cAAc;AAI/C,oBAAI,UAAU;AACd,mCAAmB,MAAK;AAExB,oBAAI,OAAO,MAAK;AAChB,oBAAM,SAASA,WAAU,eAAe,MAAM;AAC9C,uBAAO,YAAY,OAAO,UAAU,KAAK,WAAA;AACrC,yBAAQ,IAAI,WAAW,eAAe,YAAA,OAAY,QAAM,eAAA,CAAe,CAAC;iBAC3E;qBACE;AACH,mCAAmB,UAAU,mBAAmB,MAAM;AACtD,oBAAM,SAAS,EAAE,aAAa,KAAK,IAAI,GAAG,EAAE,IAAI,IAAI,EAAE;AACtD,6BAAa,SAAS;AACtB,mBAAG,QAAQ,IAAI;AACf,oBAAI,iBAAiB;AACnB,sCAAoB,IAAI,kBAAkB;;AAE5C,6BAAa,IAAI,SAAS,IAAI,oBAAoB,MAAM;;eAE7D,MAAM;AAET,gBAAI,YAAY,KAAM,WAAA;AAElB,mCAAqB;AACrB,kBAAM,QAAQ,GAAG,QAAQ,IAAI;AAE7B,kBAAM,mBAAmB,MAAM,MAAM,gBAAgB;AACrD,kBAAI,iBAAiB,SAAS;AAAG,oBAAI;AACnC,sBAAM,WAAW,MAAM,YAAY,oBAAoB,gBAAgB,GAAG,UAAU;AACpF,sBAAI,MAAM;AAAY,qCAAiB,IAAI,OAAO,QAAQ;uBACrD;AACD,+CAA2B,IAAI,GAAG,WAAW,QAAQ;AACrD,wBAAI,CAAC,sBAAsB,IAAI,QAAQ,KAAK,CAAC,iBAAiB;AAC5D,8BAAQ,KAAK,kLAAkL;AAC/L,4BAAM,MAAK;AACX,wCAAkB,MAAM,UAAU;AAClC,wCAAkB;AAClB,6BAAO,QAAS,UAAS,CAAE;;;AAGjC,2CAAyB,IAAI,QAAQ;yBAC9B,GAAG;;AASZ,0BAAY,KAAK,EAAE;AAEnB,oBAAM,kBAAkB,KAAK,SAAA,IAAE;AAC3B,sBAAM,UAAU;AAChB,mBAAG,GAAG,eAAe,EAAE,KAAK,EAAE;eACjC;AAED,oBAAM,UAAU,KAAK,SAAA,IAAE;AACnB,mBAAG,GAAG,OAAO,EAAE,KAAK,EAAE;eACzB;AAED,kBAAI;AAAY,mCAAmB,GAAG,OAAO,MAAM;AAEnD,sBAAO;eAER,MAAM;WACV,EAAE,MAAM,SAAA,KAAG;AACV,oBAAQ,QAAG,QAAH,QAAG,SAAA,SAAH,IAAK,MAAI;cACf,KAAK;AACH,oBAAI,MAAM,iBAAiB,GAAG;AAG5B,wBAAM;AACN,0BAAQ,KAAK,qDAAqD;AAClE,yBAAO,UAAS;;AAElB;cACF,KAAK;AACH,oBAAI,kBAAkB,GAAG;AACvB,oCAAkB;AAClB,yBAAO,UAAS;;AAElB;;AAEJ,mBAAO3B,aAAQ,OAAO,GAAG;WAC1B;QAAC;AAGF,eAAOA,aAAQ,KAAK;UAClB;WACC,OAAO,cAAc,cAAcA,aAAQ,QAAO,IAAKiC,SAAkB,GAAI,KAAK,SAAS;SAC7F,EAAE,KAAK,WAAA;AAKJ,2BAAgB;AAChB,gBAAM,oBAAoB,CAAA;AAC1B,iBAAOjC,aAAQ,QAAQ,IAAI,WAAA;AAAI,mBAAA,GAAG,GAAG,MAAM,KAAK,GAAG,GAAG;UAAC,CAAA,CAAC,EAAE,KAAK,SAAS,iBAAc;AAClF,gBAAI,MAAM,kBAAkB,SAAS,GAAG;AAEpC,kBAAI,eAAa,MAAM,kBAAkB,OAAO,iBAAiB,GAAG;AACpE,oBAAM,oBAAoB,CAAA;AAC1B,qBAAOA,aAAQ,QAAQ,IAAI,WAAA;AAAI,uBAAA,aAAW,GAAG,GAAG;cAAC,CAAA,CAAC,EAAE,KAAK,cAAc;;WAE9E;SACJ,EAAE,QAAQ,WAAA;AACP,cAAI,MAAM,kBAAkB,eAAe;AAEzC,kBAAM,oBAAoB;AAC1B,kBAAM,gBAAgB;;SAE3B,EAAE,MAAM,SAAA,KAAG;AACR,gBAAM,cAAc;AACpB,cAAI;AAEF,kCAAsB,mBAAmB,MAAK;mBAC9CV,KAAM;UAAA;AACR,cAAI,kBAAkB,MAAM,eAAe;AAGzC,eAAG,OAAM;;AAEX,iBAAO,UAAW,GAAG;SACxB,EAAE,QAAQ,WAAA;AACT,gBAAM,eAAe;AACrB,yBAAc;SACf,EAAE,KAAK,WAAA;AACN,cAAI,YAAY;AAGd,gBAAM,eAA+B,CAAA;AACrC,eAAG,OAAO,QAAQ,SAAA,OAAK;AACrB,oBAAM,OAAO,QAAQ,QAAQ,SAAA,KAAG;AAC9B,oBAAI,IAAI;AAAM,+BAAW,SAAA,OAAS,GAAG,MAAI,GAAA,EAAA,OAAI,MAAM,MAAI,GAAA,EAAA,OAAI,IAAI,IAAI,CAAE,IAAI,IAAIuC,UAAS,WAAW,CAAC,CAAC,CAAA,CAAE,CAAC,CAAC;eACxG;AACD,2BAAW,SAAA,OAAS,GAAG,MAAI,GAAA,EAAA,OAAI,MAAM,MAAI,GAAA,CAAG,IAAI,aAAW,SAAA,OAAS,GAAG,MAAI,GAAA,EAAA,OAAI,MAAM,MAAI,QAAA,CAAQ,IAAI,IAAIA,UAAS,WAAW,CAAC,CAAC,CAAA,CAAE,CAAC,CAAC;aACpI;AAED,yBAAa,gCAAgC,EAAE,KAAK,YAAU;AAE9D,iCAAqB,cAAY,IAAI;;AAGvC,iBAAO;SACR;MACH;eC/MgB,cAAe,UAAuB;AACpD,YAAI,WAAW,SAAA,QAAM;AAAI,iBAAA,SAAS,KAAK,MAAM;QAAC,GAC1C,UAAU,SAAA,OAAK;AAAI,iBAAA,SAAS,MAAM,KAAK;QAAC,GACxC,YAAY,KAAK,QAAQ,GACzB,UAAU,KAAK,OAAO;AAE1B,iBAAS,KAAK,SAAmB;AAC7B,iBAAO,SAAC,KAAI;AACR,gBAAI,OAAO,QAAQ,GAAG,GAClB,QAAQ,KAAK;AAEjB,mBAAO,KAAK,OAAO,QACd,CAAC,SAAS,OAAO,MAAM,SAAS,aAC7B,QAAQ,KAAK,IAAI,QAAQ,IAAI,KAAK,EAAE,KAAK,WAAW,OAAO,IAAI,UAAU,KAAK,IAC9E,MAAM,KAAK,WAAW,OAAO;;;AAI7C,eAAO,KAAK,QAAQ,EAAC;MACvB;eCPgB,uBAAuB,MAAuB,aAAa,WAAS;AAElF,YAAI,IAAI,UAAU;AAClB,YAAI,IAAI;AAAG,gBAAM,IAAI,WAAW,gBAAgB,mBAAmB;AAGnE,YAAI,OAAO,IAAI,MAAM,IAAI,CAAC;AAC1B,eAAO,EAAE;AAAG,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAErC,oBAAY,KAAK,IAAG;AACpB,YAAI,SAAS,QAAQ,IAAI;AACzB,eAAO,CAAC,MAAM,QAAQ,SAAS;MACjC;eAEgB,sBACd,IACA,MACA,YACA,mBACA,WAAqC;AAErC,eAAO7B,aAAQ,QAAO,EAAG,KAAK,WAAA;AAE5B,cAAM,YAAY,IAAI,aAAa;AAGnC,cAAM,QAAQ,GAAG,mBAAmB,MAAM,YAAY,GAAG,WAAW,iBAAiB;AACrF,gBAAM,WAAW;AAEjB,cAAM,YAAY;YAChB;YACA;;AAGF,cAAI,mBAAmB;AAErB,kBAAM,WAAW,kBAAkB;iBAC9B;AACL,gBAAI;AACF,oBAAM,OAAM;AAEZ,oBAAM,SAAS,YAAY;AAC3B,iBAAG,OAAO,iBAAiB;qBACpB,IAAI;AACX,kBAAI,GAAG,SAAS,SAAS,gBAAgB,GAAG,OAAM,KAAM,EAAE,GAAG,OAAO,iBAAiB,GAAG;AACtF,wBAAQ,KAAK,0BAA0B;AACvC,mBAAG,MAAM,EAAC,iBAAiB,MAAK,CAAC;AACjC,uBAAO,GAAG,KAAI,EAAG,KAAK,WAAA;AAAM,yBAAA,sBAC1B,IACA,MACA,YACA,MACA,SAAS;gBACV,CAAA;;AAEH,qBAAO,UAAU,EAAE;;;AAKvB,cAAM,mBAAmB,gBAAgB,SAAS;AAClD,cAAI,kBAAkB;AACpB,oCAAuB;;AAGzB,cAAI;AACJ,cAAM,kBAAkBA,aAAQ,OAAO,WAAA;AAErC,0BAAc,UAAU,KAAK,OAAO,KAAK;AACzC,gBAAI,aAAa;AACf,kBAAI,kBAAkB;AAEpB,oBAAI,cAAc,wBAAwB,KAAK,MAAM,IAAI;AACzD,4BAAY,KAAK,aAAa,WAAW;yBAChC,OAAO,YAAY,SAAS,cAAc,OAAO,YAAY,UAAU,YAAY;AAE5F,8BAAc,cAAc,WAAW;;;aAG1C,SAAS;AACZ,kBAAQ,eAAe,OAAO,YAAY,SAAS,aAEjDA,aAAQ,QAAQ,WAAW,EAAE,KAAK,SAAA,GAAC;AAAI,mBAAA,MAAM,SAC3C,IACE,UAAU,IAAI,WAAW,gBACzB,4DAA4D,CAAC;UAAC,CAAA,IAEhE,gBAAgB,KAAK,WAAA;AAAM,mBAAA;UAAW,CAAA,GACxC,KAAK,SAAA,GAAC;AAEN,gBAAI;AAAmB,oBAAM,SAAQ;AAGrC,mBAAO,MAAM,YAAY,KAAK,WAAA;AAAM,qBAAA;YAAC,CAAA;WACtC,EAAE,MAAM,SAAA,GAAC;AACR,kBAAM,QAAQ,CAAC;AACf,mBAAO,UAAU,CAAC;WACnB;SACF;MACH;eC7EgB,IAAK,GAAgB,OAAY,OAAa;AAC5D,YAAM,SAAS,QAAQ,CAAC,IAAI,EAAE,MAAK,IAAK,CAAC,CAAC;AAC1C,iBAAS,IAAE,GAAG,IAAE,OAAO,EAAE;AAAG,iBAAO,KAAK,KAAK;AAC7C,eAAO;MACT;eAGgB,6BAA8B,MAAY;AACxD,eAAA,SAAA,SAAA,CAAA,GACK,IAAI,GAAA,EACP,OAAK,SAAC,WAAiB;AACrB,cAAM,QAAQ,KAAK,MAAM,SAAS;AAC3B,cAAA,SAAU,MAAK;AACtB,cAAM,cAAsD,CAAA;AAC5D,cAAM,oBAAoC,CAAA;AAE1C,mBAAS,kBAAmB,SAAmC,SAAiB,eAA0B;AACxG,gBAAM,eAAe,gBAAgB,OAAO;AAC5C,gBAAM,YAAa,YAAY,YAAY,IAAI,YAAY,YAAY,KAAK,CAAA;AAC5E,gBAAM,YAAY,WAAW,OAAO,IAAG,OAAO,YAAY,WAAW,IAAI,QAAQ;AACjF,gBAAM,YAAY,UAAU;AAC5B,gBAAM,eAAY,SAAA,SAAA,CAAA,GACb,aAAa,GAAA,EAChB,MAAM,YACF,GAAA,OAAG,cAAY,gBAAA,EAAA,OAAiB,cAAc,MAAI,GAAA,IAClD,cAAc,MAClB,eACA,WACA,SACA,WACA,YAAY,gBAAgB,OAAO,GACnC,QAAQ,CAAC,aAAa,cAAc,OAAM,CAAA;AAE5C,sBAAU,KAAK,YAAY;AAC3B,gBAAI,CAAC,aAAa,cAAc;AAC9B,gCAAkB,KAAK,YAAY;;AAErC,gBAAI,YAAY,GAAG;AACjB,kBAAM,iBAAiB,cAAc,IACnC,QAAQ,CAAC,IACT,QAAQ,MAAM,GAAG,YAAY,CAAC;AAChC,gCAAkB,gBAAgB,UAAU,GAAG,aAAa;;AAE9D,sBAAU,KAAK,SAAC,GAAE,GAAC;AAAK,qBAAA,EAAE,UAAU,EAAE;YAAO,CAAA;AAC7C,mBAAO;;AAGT,cAAM,aAAa,kBAAkB,OAAO,WAAW,SAAS,GAAG,OAAO,UAAU;AACpF,sBAAY,KAAK,IAAI,CAAC,UAAU;AAChC,mBAAoB,KAAA,GAAAV,MAAA,OAAO,SAAP,KAAAA,IAAA,QAAA,MAAgB;AAA/B,gBAAM,QAAKA,IAAA,EAAA;AACd,8BAAkB,MAAM,SAAS,GAAG,KAAK;;AAG3C,mBAAS,cAAc,SAAiC;AACtD,gBAAM4B,UAAS,YAAY,gBAAgB,OAAO,CAAC;AACnD,mBAAOA,WAAUA,QAAO,CAAC;;AAG3B,mBAAS,eAAgB,OAAuB,SAAe;AAC7D,mBAAO;cACL,MAAM,MAAM,SAAI,QAEd,MAAM;cACR,OAAO,IAAI,MAAM,OAAO,MAAM,YAAY,KAAK,UAAU,KAAK,SAAS,OAAO;cAC9E,WAAW;cACX,OAAO,IAAI,MAAM,OAAO,MAAM,YAAY,KAAK,UAAU,KAAK,SAAS,OAAO;cAC9E,WAAW;;;AAMf,mBAAS,iBAAkB,KAAuB;AAChD,gBAAMgB,SAAQ,IAAI,MAAM;AACxB,mBAAOA,OAAM,YAAS,SAAA,SAAA,CAAA,GACjB,GAAG,GAAA,EACN,OAAO;cACL,OAAOA,OAAM;cACb,OAAO,eAAe,IAAI,MAAM,OAAOA,OAAM,OAAO;cACrD,CAAA,IACC;;AAGN,cAAM,SAAM,SAAA,SAAA,CAAA,GACP,KAAK,GAAA,EACR,QAAM,SAAA,SAAA,CAAA,GACD,MAAM,GAAA,EACT,YACA,SAAS,mBACT,mBAAmB,cAAa,CAAA,GAGlC,OAAK,SAAC,KAAG;AACP,mBAAO,MAAM,MAAM,iBAAiB,GAAG,CAAC;aAG1C,OAAK,SAAC,KAAG;AACP,mBAAO,MAAM,MAAM,iBAAiB,GAAG,CAAC;aAG1C,YAAU,SAAC,KAAG;AACN,gBAAA5C,MAAmC,IAAI,MAAM,OAA5C,UAAOA,IAAA,SAAE,YAASA,IAAA,WAAE,YAASA,IAAA;AACpC,gBAAI,CAAC;AAAW,qBAAO,MAAM,WAAW,GAAG;AAE3C,qBAAS,oBAAoB,QAAoB;AAC/C,uBAAS,UAAW,KAAS;AAC3B,uBAAO,OACL,OAAO,SAAS,IAAI,KAAK,IAAI,UAAU,KAAK,UAAU,KAAK,SAAS,OAAO,CAAC,IAC5E,IAAI,SACF,OAAO,SACL,OAAO,IAAI,MAAM,GAAG,SAAS,EAC1B,OAAO,IAAI,UACR,KAAK,UACL,KAAK,SAAS,OAAO,CAAC,IAE9B,OAAO,SAAQ;;AAErB,kBAAM,gBAAgB,OAAO,OAAO,QAAQ;gBAC1C,UAAU,EAAC,OAAO,UAAS;gBAC3B,oBAAoB;kBAClB,OAAK,SAAC,KAAU6C,aAAe;AAC7B,2BAAO,mBAAmB,IAAI,KAAK,KAAK,SAAS,OAAO,GAAGA,WAAU;;;gBAGzE,YAAY;kBACV,KAAG,WAAA;AACD,2BAAO,OAAO;;;gBAGlB,KAAK;kBACH,KAAG,WAAA;AACD,wBAAM,MAAM,OAAO;AACnB,2BAAO,cAAc,IACnB,IAAI,CAAC,IACL,IAAI,MAAM,GAAG,SAAS;;;gBAG5B,OAAO;kBACL,KAAG,WAAA;AACD,2BAAO,OAAO;;;eAGnB;AACD,qBAAO;;AAGT,mBAAO,MAAM,WAAW,iBAAiB,GAAG,CAAC,EAC1C,KAAK,SAAA,QAAM;AAAI,qBAAA,UAAU,oBAAoB,MAAM;YAAC,CAAA;YACxD,CAAA;AAEH,iBAAO;UACR,CAAA;MAEL;AAEO,UAAM,yBAA8C;QACzD,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ;;eCjMM,cAAc,GAAQ,GAAQ,IAAU,MAAa;AAEnE,aAAK,MAAM,CAAA;AACX,eAAO,QAAQ;AACf,aAAK,CAAC,EAAE,QAAQ,SAAC,MAAI;AACnB,cAAI,CAAC,OAAO,GAAG,IAAI,GAAG;AAEpB,eAAG,OAAO,IAAI,IAAI;iBACb;AACL,gBAAI,KAAK,EAAE,IAAI,GACb,KAAK,EAAE,IAAI;AACb,gBAAI,OAAO,OAAO,YAAY,OAAO,OAAO,YAAY,MAAM,IAAI;AAChE,kBAAM,aAAa,YAAY,EAAE;AACjC,kBAAM,aAAa,YAAY,EAAE;AAEjC,kBAAI,eAAe,YAAY;AAC7B,mBAAG,OAAO,IAAI,IAAI,EAAE,IAAI;yBACf,eAAe,UAAU;AAElC,8BAAc,IAAI,IAAI,IAAI,OAAO,OAAO,GAAG;yBAClC,OAAO,IAAI;AAKpB,mBAAG,OAAO,IAAI,IAAI,EAAE,IAAI;;uBAEjB,OAAO;AAAI,iBAAG,OAAO,IAAI,IAAI,EAAE,IAAI;;SAEjD;AACD,aAAK,CAAC,EAAE,QAAQ,SAAC,MAAI;AACnB,cAAI,CAAC,OAAO,GAAG,IAAI,GAAG;AACpB,eAAG,OAAO,IAAI,IAAI,EAAE,IAAI;;SAE3B;AACD,eAAO;MACT;eC9BgB,iBACd,YACA,KAAiI;AAGjI,YAAI,IAAI,SAAS;AAAU,iBAAO,IAAI;AACtC,eAAO,IAAI,QAAQ,IAAI,OAAO,IAAI,WAAW,UAAU;MACzD;ACKO,UAAM,kBAAuC;QAClD,OAAO;QACP,MAAM;QACN,OAAO;QACP,QAAQ,SAAC,UAAgB;AAAK,iBAAA,SAAA,SAAA,CAAA,GACzB,QAAQ,GAAA,EACX,OAAK,SAAC,WAAiB;AACrB,gBAAM,YAAY,SAAS,MAAM,SAAS;AACnC,gBAAA,aAAc,UAAU,OAAM;AAErC,gBAAM,kBAAe,SAAA,SAAA,CAAA,GAChB,SAAS,GAAA,EACZ,QAAM,SAAC,KAAG;AACR,kBAAM,UAAU,IAAI;AAGd,kBAAA7C,MAAiC,QAAQ,MAAM,SAAS,EAAE,MAAzD,WAAQA,IAAA,UAAE,WAAQA,IAAA,UAAE,WAAQA,IAAA;AACnC,sBAAQ,IAAI,MAAI;gBACd,KAAK;AACH,sBAAI,SAAS,SAAS;AAAK;AAC3B,yBAAO,QAAQ,SAAS,aAAa,WAAA;AAAI,2BAAA,eAAe,GAAG;kBAAC,GAAE,IAAI;gBACpE,KAAK;AACH,sBAAI,SAAS,SAAS,OAAO,SAAS,SAAS;AAAK;AACpD,yBAAO,QAAQ,SAAS,aAAa,WAAA;AAAI,2BAAA,eAAe,GAAG;kBAAC,GAAE,IAAI;gBACpE,KAAK;AACH,sBAAI,SAAS,SAAS;AAAK;AAC3B,yBAAO,QAAQ,SAAS,aAAa,WAAA;AAAI,2BAAA,eAAe,GAAG;kBAAC,GAAE,IAAI;gBACpE,KAAK;AACH,sBAAI,SAAS,SAAS;AAAK;AAC3B,yBAAO,QAAQ,SAAS,aAAa,WAAA;AAAI,2BAAA,YAAY,GAAG;kBAAC,GAAE,IAAI;;AAGnE,qBAAO,UAAU,OAAO,GAAG;AAG3B,uBAAS,eAAe6B,MAA8D;AACpF,oBAAMiB,WAAU,IAAI;AACpB,oBAAMnC,QAAOkB,KAAI,QAAQ,iBAAiB,YAAYA,IAAG;AACzD,oBAAI,CAAClB;AAAM,wBAAM,IAAI,MAAM,cAAc;AAEzC,gBAAAkB,OAAMA,KAAI,SAAS,SAASA,KAAI,SAAS,QAAK,SAAA,SAAA,CAAA,GACxCA,IAAG,GAAA,EAAE,MAAIlB,MAAA,CAAA,IAAA,SAAA,CAAA,GACTkB,IAAG;AACT,oBAAIA,KAAI,SAAS;AAAU,kBAAAA,KAAI,SAAM,cAAA,CAAA,GAAOA,KAAI,QAAM,IAAA;AACtD,oBAAIA,KAAI;AAAM,kBAAAA,KAAI,OAAI,cAAA,CAAA,GAAOA,KAAI,MAAI,IAAA;AAErC,uBAAO,kBAAkB,WAAWA,MAAKlB,KAAI,EAAE,KAAM,SAAA,gBAAc;AACjE,sBAAM,WAAWA,MAAK,IAAI,SAAC,KAAK,GAAC;AAC/B,wBAAM,gBAAgB,eAAe,CAAC;AACtC,wBAAM,MAAM,EAAE,SAAS,MAAM,WAAW,KAAI;AAC5C,wBAAIkB,KAAI,SAAS,UAAU;AAEzB,+BAAS,KAAK,KAAK,KAAK,KAAK,eAAeiB,QAAO;+BAC1CjB,KAAI,SAAS,SAAS,kBAAkB,QAAW;AAE5D,0BAAM,sBAAsB,SAAS,KAAK,KAAK,KAAK,KAAKA,KAAI,OAAO,CAAC,GAAGiB,QAAO;AAC/E,0BAAI,OAAO,QAAQ,uBAAuB,MAAM;AAC9C,8BAAM;AACN,wBAAAjB,KAAI,KAAK,CAAC,IAAI;AACd,4BAAI,CAAC,WAAW,UAAU;AACxB,uCAAaA,KAAI,OAAO,CAAC,GAAG,WAAW,SAAS,GAAG;;;2BAGlD;AAEL,0BAAM,aAAa,cAAc,eAAeA,KAAI,OAAO,CAAC,CAAC;AAC7D,0BAAM,sBAAoB,SAAS,KAAK,KAAK,KAAK,YAAY,KAAK,eAAeiB,QAAO;AACzF,0BAAI,qBAAmB;AACrB,4BAAM,mBAAiBjB,KAAI,OAAO,CAAC;AACnC,+BAAO,KAAK,mBAAiB,EAAE,QAAQ,SAAA,SAAO;AAC5C,8BAAI,OAAO,kBAAgB,OAAO,GAAG;AAEnC,6CAAe,OAAO,IAAI,oBAAkB,OAAO;iCAC9C;AAEL,yCAAa,kBAAgB,SAAS,oBAAkB,OAAO,CAAC;;yBAEnE;;;AAGL,2BAAO;mBACR;AACD,yBAAO,UAAU,OAAOA,IAAG,EAAE,KAAK,SAAC7B,KAA4C;wBAA3C,WAAQA,IAAA,UAAE,UAAOA,IAAA,SAAE,cAAWA,IAAA,aAAE,aAAUA,IAAA;AAC5E,6BAAS,IAAE,GAAG,IAAEW,MAAK,QAAQ,EAAE,GAAG;AAChC,0BAAM,UAAU,UAAU,QAAQ,CAAC,IAAIA,MAAK,CAAC;AAC7C,0BAAM,MAAM,SAAS,CAAC;AACtB,0BAAI,WAAW,MAAM;AACnB,4BAAI,WAAW,IAAI,QAAQ,SAAS,CAAC,CAAC;6BACjC;AACL,4BAAI,aAAa,IAAI;0BACnBkB,KAAI,SAAS,SAAS,eAAe,CAAC,IACpCA,KAAI,OAAO,CAAC,IACZ;;;;AAIR,2BAAO,EAAC,UAAU,SAAS,aAAa,WAAU;mBACnD,EAAE,MAAM,SAAA,OAAK;AACZ,6BAAS,QAAQ,SAAA,KAAG;AAAI,6BAAA,IAAI,WAAW,IAAI,QAAQ,KAAK;oBAAC,CAAA;AACzD,2BAAO,QAAQ,OAAO,KAAK;mBAC5B;iBACF;;AAGH,uBAAS,YAAYA,MAA6B;AAChD,uBAAO,gBAAgBA,KAAI,OAAOA,KAAI,OAAO,GAAK;;AAGpD,uBAAS,gBAAgB,OAA0B,OAAuB,OAAa;AAErF,uBAAO,UAAU,MAAM,EAAC,OAAO,QAAQ,OAAO,OAAO,EAAC,OAAO,YAAY,MAAK,GAAG,MAAK,CAAC,EACtF,KAAK,SAAC7B,KAAQ;sBAAP,SAAMA,IAAA;AAGZ,yBAAO,eAAe,EAAC,MAAM,UAAU,MAAM,QAAQ,MAAK,CAAC,EAAE,KAAK,SAAA,KAAG;AACnE,wBAAI,IAAI,cAAc;AAAG,6BAAO,QAAQ,OAAO,IAAI,SAAS,CAAC,CAAC;AAC9D,wBAAI,OAAO,SAAS,OAAO;AACzB,6BAAO,EAAC,UAAU,CAAA,GAAI,aAAa,GAAG,YAAY,OAAS;2BACtD;AACL,6BAAO,gBAAgB,OAAK,SAAA,SAAA,CAAA,GAAM,KAAK,GAAA,EAAE,OAAO,OAAO,OAAO,SAAS,CAAC,GAAG,WAAW,KAAI,CAAA,GAAG,KAAK;;mBAErG;iBACF;;cAEJ,CAAA;AAIH,mBAAO;YACR,CAAA;QAAA;;AAIL,eAAS,kBACP,OACA,KACA,eAAoB;AAEpB,eAAO,IAAI,SAAS,QAChB,QAAQ,QAAQ,CAAA,CAAE,IAClB,MAAM,QAAQ,EAAE,OAAO,IAAI,OAAO,MAAM,eAAe,OAAO,YAAW,CAAE;MACjF;eC3JgB,wBACdW,OACAoC,QACA,OAAe;AAEf,YAAI;AACF,cAAI,CAACA;AAAO,mBAAO;AACnB,cAAIA,OAAM,KAAK,SAASpC,MAAK;AAAQ,mBAAO;AAC5C,cAAM,SAAgB,CAAA;AAItB,mBAAS,IAAI,GAAG,IAAI,GAAG,IAAIoC,OAAM,KAAK,UAAU,IAAIpC,MAAK,QAAQ,EAAE,GAAG;AACpE,gBAAIL,KAAIyC,OAAM,KAAK,CAAC,GAAGpC,MAAK,CAAC,CAAC,MAAM;AAAG;AACvC,mBAAO,KAAK,QAAQ,UAAUoC,OAAM,OAAO,CAAC,CAAC,IAAIA,OAAM,OAAO,CAAC,CAAC;AAChE,cAAE;;AAGJ,iBAAO,OAAO,WAAWpC,MAAK,SAAS,SAAS;iBAChDX,KAAM;AACN,iBAAO;;MAEX;AAEO,UAAM,gCAAoD;QAC/D,OAAO;QACP,OAAO;QACP,QAAQ,SAAC,MAAI;AACX,iBAAO;YACL,OAAO,SAAC,WAAS;AACf,kBAAM,QAAQ,KAAK,MAAM,SAAS;AAClC,qBAAA,SAAA,SAAA,CAAA,GACK,KAAK,GAAA,EACR,SAAS,SAAC,KAAG;AACX,oBAAI,CAAC,IAAI,OAAO;AACd,yBAAO,MAAM,QAAQ,GAAG;;AAE1B,oBAAM,eAAe,wBACnB,IAAI,MACJ,IAAI,MAAM,QAAQ,GAClB,IAAI,UAAU,OAAO;AAEvB,oBAAI,cAAc;AAChB,yBAAOU,aAAQ,QAAQ,YAAY;;AAErC,uBAAO,MAAM,QAAQ,GAAG,EAAE,KAAK,SAAC,KAAG;AACjC,sBAAI,MAAM,QAAQ,IAAI;oBACpB,MAAM,IAAI;oBACV,QAAQ,IAAI,UAAU,UAAU,UAAU,GAAG,IAAI;;AAEnD,yBAAO;iBACR;iBAEH,QAAQ,SAAC,KAAG;AAEV,oBAAI,IAAI,SAAS;AAAO,sBAAI,MAAM,QAAQ,IAAI;AAC9C,uBAAO,MAAM,OAAO,GAAG;gBACxB,CAAA;;;;;eC5DK,kBAAkB,KAAuB,OAAkB;AACzE,eACE,IAAI,MAAM,SAAS,cACnB,CAAC,CAAC,IAAI,UACN,CAAC,IAAI,MAAM,YACX,IAAI,MAAM,GAAG,SAAS,UAAU,cAChC,CAAC,MAAM,OAAO,WAAW;MAE7B;eCRgB,kBAAkBT,OAAc,KAAyH;AACvK,gBAAQA,OAAI;UACV,KAAK;AACH,mBAAO,IAAI,UAAU,CAAC,IAAI;UAC5B,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO;;MAEb;ACYO,UAAM,0BAA8C;QACzD,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ,SAAC,MAAI;AACX,cAAM,SAAS,KAAK,OAAO;AAC3B,cAAM,aAAa,IAAIsC,UAAS,KAAK,SAAS,KAAK,OAAO;AAE1D,iBAAA,SAAA,SAAA,CAAA,GACK,IAAI,GAAA,EACP,aAAa,SAAC,QAAQ,MAAM,SAAO;AACjC,gBAAI,IAAI,UAAU,SAAS,YAAY;AACrC,oBAAM,IAAI,WAAW,SAAS,+DAAA,OAAgE,IAAyB,OAAO,CAAE;;AAElI,mBAAO,KAAK,YAAY,QAAQ,MAAM,OAAO;aAE/C,OAAO,SAAC,WAAS;AACf,gBAAM,QAAQ,KAAK,MAAM,SAAS;AAC1B,gBAAA,SAAW,MAAK;AAChB,gBAAA,aAAwB,OAAM,YAAlB,UAAY,OAAM;AAC9B,gBAAA,aAAyB,WAAU,YAAvB,WAAa,WAAU;AAC3C,gBAAM,uBAAuB,WAAW,iBAAiB,QAAQ,OAC/D,SAAC,OAAK;AAAK,qBAAA,MAAM,YAAa,MAAM,QAAqB,SAAS,WAAW,OAAiB;YAAC,CAAA;AAEjG,gBAAM,aAAU,SAAA,SAAA,CAAA,GACX,KAAK,GAAA,EACR,QAAQ,SAAC,KAAG;;AACV,kBAAM,QAAQ,IAAI;AAGlB,kBAAM,eAAe,IAAI,iBAAiB,IAAI,eAAe,CAAA;AAC7D,kBAAM,cAAc,SAAC,WAAiB;AACpC,oBAAM,OAAO,SAAA,OAAS,QAAM,GAAA,EAAA,OAAI,WAAS,GAAA,EAAA,OAAI,SAAS;AACtD,uBAAQ,aAAa,IAAI,MACtB,aAAa,IAAI,IAAI,IAAIA,UAAQ;;AAEtC,kBAAM,aAAa,YAAY,EAAE;AACjC,kBAAM,eAAe,YAAY,OAAO;AAEhC,kBAAAtC,QAAS,IAAG;AAChB,kBAAA,KACF,IAAI,SAAS,gBACT,CAAC,IAAI,KAAK,IACV,IAAI,SAAS,WACb,CAAC,IAAI,IAAI,IACT,IAAI,OAAO,SAAS,KACpB,CAAC,iBAAiB,YAAY,GAAG,EAAE,OAAO,SAAA,IAAE;AAAI,uBAAA;cAAE,CAAA,GAAG,IAAI,MAAM,IAC/D,CAAA,GAPDU,QAAI,GAAA,CAAA,GAAE,UAAO,GAAA,CAAA;AASlB,kBAAM,WAAW,IAAI,MAAM,QAAQ;AAInC,kBAAI,QAAQA,KAAI,GAAG;AAGjB,2BAAW,QAAQA,KAAI;AAGvB,oBAAM,UAAUV,UAAS,YAAYU,MAAK,WAAW,QAAQ,SAAS,wBAAwBA,OAAM,QAAQ,IAAI;AAGhH,oBAAI,CAAC,SAAS;AAGZ,+BAAa,QAAQA,KAAI;;AAE3B,oBAAI,WAAW,SAAS;AAEtB,uCAAqB,aAAa,QAAQ,SAAS,OAAO;;yBAEnDA,OAAM;AAKf,oBAAM,QAAQ;kBACZ,OAAMX,MAAAW,MAAK,WAAK,QAAAX,QAAA,SAAAA,MAAI,KAAK;kBACzB,KAAI,KAAAW,MAAK,WAAK,QAAA,OAAA,SAAA,KAAI,KAAK;;AAEzB,6BAAa,IAAI,KAAK;AAEtB,2BAAW,IAAI,KAAK;qBACf;AAIL,2BAAW,IAAI,UAAU;AACzB,6BAAa,IAAI,UAAU;AAC3B,uBAAO,QAAQ,QAAQ,SAAA,KAAG;AAAI,yBAAA,YAAY,IAAI,IAAI,EAAE,IAAI,UAAU;gBAAC,CAAA;;AAGrE,qBAAO,MAAM,OAAO,GAAG,EAAE,KAAK,SAAC,KAAG;AAGhC,oBAAIA,UAAS,IAAI,SAAS,SAAS,IAAI,SAAS,QAAQ;AAGtD,6BAAW,QAAQ,IAAI,OAAO;AAC9B,sBAAI,sBAAsB;AAKxB,yCAAqB,QAAQ,SAAA,KAAG;AAE9B,0BAAM,UAAU,IAAI,OAAO,IAAI,SAAA,GAAC;AAAI,+BAAA,IAAI,WAAW,CAAC;sBAAC,CAAA;AAErD,0BAAM,QAAS,IAAI,QAAqB,UAAU,SAAA,MAAI;AAAI,+BAAA,SAAS,WAAW;sBAAO,CAAA;AAErF,+BAAS,IAAI,GAAG,MAAM,IAAI,QAAS,QAAQ,IAAI,KAAK,EAAE,GAAG;AACvD,gCAAQ,CAAC,EAAE,KAAK,IAAI,IAAI,QAAS,CAAC;;AAGpC,kCAAY,IAAI,IAAI,EAAE,QAAQ,OAAO;qBACtC;;;AAGL,sBAAM,eAAe,uBACnB,MAAM,gBAAgB,CAAA,GACtB,YAAY;AAEd,uBAAO;eACR;cACF,CAAA;AAGH,gBAAM,WAAkD,SAACX,KAK9B;;kBAJzB,KAAAA,IAAA,OAAS,QAAK,GAAA,OAAE,QAAK,GAAA;AAIS,qBAAA;gBAC9B;gBACA,IAAIuC,WAAS,KAAA,MAAM,WAAK,QAAA,OAAA,SAAA,KAAI,KAAK,UAAS,KAAA,MAAM,WAAK,QAAA,OAAA,SAAA,KAAI,KAAK,OAAO;;;AAGvE,gBAAM,kBAGF;cACF,KAAK,SAAC,KAAG;AAAK,uBAAA,CAAC,YAAY,IAAIA,UAAS,IAAI,GAAG,CAAC;cAAC;cACjD,SAAS,SAAC,KAAG;AAAK,uBAAA,CAAC,YAAY,IAAIA,UAAQ,EAAG,QAAQ,IAAI,IAAI,CAAC;cAAC;cAChE,OAAO;cACP,OAAO;cACP,YAAY;;AAGd,iBAAK,eAAe,EAAE,QAAQ,SAAC,QAA4D;AACzF,yBAAW,MAAM,IAAI,SACnB,KAK2B;AAEnB,oBAAA,SAAW,IAAuB;AAC1C,oBAAM,cAAc,CAAC,CAAC;AACtB,oBAAI,WAAW,kBAAkB,KAAyB,KAAK,KAAK,kBAAkB,QAAQ,GAAG;AACjG,oBAAM,SAAS,WACX,IAAI,SAAS,CAAA,IACb;AAEJ,oBAAI,aAAa;AAKf,sBAAM,cAAc,SAAC,WAAiB;AACpC,wBAAM,OAAO,SAAA,OAAS,QAAM,GAAA,EAAA,OAAI,WAAS,GAAA,EAAA,OAAI,SAAS;AACtD,2BAAQ,OAAO,IAAI,MAChB,OAAO,IAAI,IAAI,IAAIA,UAAQ;;AAEhC,sBAAM,eAAa,YAAY,EAAE;AACjC,sBAAM,iBAAe,YAAY,OAAO;AAClC,sBAAAvC,MAAgC,gBAAgB,MAAM,EAAE,GAAG,GAA1D,eAAYA,IAAA,CAAA,GAAE,gBAAaA,IAAA,CAAA;AAElC,sBAAI,WAAW,WAAW,aAAa,gBAAgB,CAAE,IAA2B,QAAQ;AAE1F,mCAAa,IAAI,aAAa;yBACzB;AACL,gCAAY,aAAa,QAAQ,EAAE,EAAE,IAAI,aAAa;;AAExD,sBAAI,CAAC,aAAa,cAAc;AAU9B,wBAAI,WAAW,SAAS;AAKtB,qCAAa,IAAI,UAAU;2BACtB;AAIL,0BAAM,gBACJ,WAAW,WACX,YACC,IAA2B,UAC5B,MAAM,MAAK,SAAA,SAAA,CAAA,GACL,GAA0B,GAAA,EAC9B,QAAQ,MAAK,CAAA,CAAA;AAGjB,6BAAO,MAAM,MAAM,EAAE,MAAM,MAAM,SAAS,EAAE,KAAK,SAAC,KAAG;AACnD,4BAAI,WAAW,SAAS;AACtB,8BAAI,YAAa,IAA2B,QAAQ;AAMlD,mCAAO,cAAY,KACjB,SAACA,KAA8C;kCAApC,gBAAaA,IAAA;AACtB,2CAAW,QAAQ,aAAa;AAChC,qCAAO;6BACR;;AAKL,8BAAM,QAAS,IAA2B,SACrC,IAA4B,OAAO,IAAI,UAAU,IACjD,IAA4B;AACjC,8BAAK,IAA2B,QAAQ;AAGtC,yCAAW,QAAQ,KAAK;iCACnB;AAQL,2CAAa,QAAQ,KAAK;;mCAEnB,WAAW,cAAc;AAKlC,8BAAM,WAA8B;AACpC,8BAAM,eAAc,IAAgC;AACpD,iCACE,YACA,OAAO,OAAO,UAAQ;4BACpB,KAAK;8BACH,KAAG,WAAA;AACD,+CAAa,OAAO,SAAO,UAAU;AACrC,uCAAO,SAAO;;;4BAGlB,YAAY;8BACV,KAAG,WAAA;AACD,oCAAM,OAAO,SAAO;AACpB,+CAAa,OAAO,IAAI;AACxB,uCAAO;;;4BAGX,OAAO;8BACL,KAAG,WAAA;AACD,gDAAc,aAAW,OAAO,SAAO,UAAU;AACjD,uCAAO,SAAO;;;2BAGnB;;AAGL,+BAAO;uBACR;;;;AAIP,uBAAO,MAAM,MAAM,EAAE,MAAM,MAAM,SAAS;;aAE7C;AACD,mBAAO;YACR,CAAA;;;AAKP,eAAS,qBACP,aACA,QACA,SACA,SAAmC;AAEnC,iBAAS,iBAAiB,IAAe;AACvC,cAAM,WAAW,YAAY,GAAG,QAAQ,EAAE;AAC1C,mBAAS,WAAW,KAAQ;AAC1B,mBAAO,OAAO,OAAO,GAAG,WAAW,GAAG,IAAI;;AAE5C,cAAM,eAAe,SAAC,KAAQ;AAAK,mBAAA,GAAG,cAAc,QAAQ,GAAG,IAE3D,IAAI,QAAQ,SAAAgD,MAAG;AAAI,qBAAA,SAAS,OAAOA,IAAG;YAAC,CAAA,IAEvC,SAAS,OAAO,GAAG;UAAC;AAExB,WAAC,WAAW,SAAS,QAAQ,SAAC,GAAG,GAAC;AAChC,gBAAM,SAAS,WAAW,WAAW,QAAQ,CAAC,CAAC;AAC/C,gBAAM,SAAS,WAAW,WAAW,QAAQ,CAAC,CAAC;AAC/C,gBAAI1C,KAAI,QAAQ,MAAM,MAAM,GAAG;AAE7B,kBAAI,UAAU;AAAM,6BAAa,MAAM;AACvC,kBAAI,UAAU;AAAM,6BAAa,MAAM;;WAE1C;;AAEH,eAAO,QAAQ,QAAQ,gBAAgB;MACzC;eCtVgB,6BACd,UACA,KACA,KAAyB;AAEzB,YAAI,IAAI,gBAAgB;AAAG,iBAAO;AAClC,YAAI,IAAI,SAAS,eAAe;AAE9B,iBAAO;;AAGT,YAAM,aAAa,IAAI,OACnB,IAAI,KAAK,SACT,YAAY,OAAO,IAAI,SACvB,IAAI,OAAO,SACX;AACJ,YAAI,IAAI,gBAAgB,YAAY;AAElC,iBAAO;;AAGT,YAAM,QAAK,SAAA,CAAA,GAA6B,GAAG;AAE3C,YAAI,QAAQ,MAAM,IAAI,GAAG;AACvB,gBAAM,OAAO,MAAM,KAAK,OAAO,SAAC,GAAG,GAAC;AAAK,mBAAA,EAAE,KAAK,IAAI;UAAS,CAAA;;AAE/D,YAAI,YAAY,SAAS,QAAQ,MAAM,MAAM,GAAG;AAC9C,gBAAM,SAAS,MAAM,OAAO,OAAO,SAAC,GAAG,GAAC;AAAK,mBAAA,EAAE,KAAK,IAAI;UAAS,CAAA;;AAEnE,eAAO;MACT;eCjCgB,aAAa,KAAoB,OAAqB;AACpE,eAAO,MAAM,UAAU,SACnB,OACA,MAAM,YACNA,KAAI,KAAK,MAAM,KAAK,IAAI,IACxBA,KAAI,KAAK,MAAM,KAAK,KAAK;MAC/B;eAEgB,aAAa,KAAoB,OAAqB;AACpE,eAAO,MAAM,UAAU,SACnB,OACA,MAAM,YACNA,KAAI,KAAK,MAAM,KAAK,IAAI,IACxBA,KAAI,KAAK,MAAM,KAAK,KAAK;MAC/B;eAEgB,cAAc,KAAoB,OAAqB;AACrE,eAAO,aAAa,KAAK,KAAK,KAAK,aAAa,KAAK,KAAK;MAC5D;eCXgB,mBACd,QACA,KACA,KACA,OACA,YACA,WAAkB;AAElB,YAAI,CAAC,OAAO,IAAI,WAAW;AAAG,iBAAO;AACrC,YAAM,QAAQ,IAAI,MAAM;AAChB,YAAA,aAAe,MAAK;AAC5B,YAAM,aAAa,IAAI,MAAM;AAC7B,YAAM,aAAa,MAAM,OAAO;AAChC,YAAM,iBAAiB,WAAW;AAClC,YAAM,eAAe,MAAM;AAC3B,YAAM,wBAAwB,MAAM,iBAAiB,OAAO;AAE5D,YAAI,cAAc,IAAI,OAAO,SAACsB,SAAQ,IAAE;AACtC,cAAI,gBAAgBA;AACpB,cAAM,iBAAwB,CAAA;AAC9B,cAAI,GAAG,SAAS,SAAS,GAAG,SAAS,OAAO;AAC1C,gBAAM,cAAc,IAAIW,UAAQ;AAChC,qBAAS,IAAI,GAAG,OAAO,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAE9C,kBAAM,QAAQ,GAAG,OAAO,CAAC;AACzB,kBAAM,KAAK,eAAe,KAAK;AAC/B,kBAAI,YAAY,OAAO,EAAE;AAAG;AAC5B,kBAAM,MAAM,aAAa,KAAK;AAC9B,kBACE,cAAc,QAAQ,GAAG,IACrB,IAAI,KAAK,SAAC,GAAC;AAAK,uBAAA,cAAc,GAAG,UAAU;cAAC,CAAA,IAC5C,cAAc,KAAK,UAAU,GACjC;AACA,4BAAY,OAAO,EAAE;AACrB,+BAAe,KAAK,KAAK;;;;AAI/B,kBAAQ,GAAG,MAAI;YACb,KAAK,OAAO;AACV,kBAAM,iBAAe,IAAIA,UAAQ,EAAG,QAClC,IAAI,SAASX,QAAO,IAAI,SAAC,GAAC;AAAK,uBAAA,eAAe,CAAC;cAAC,CAAA,IAAIA,OAAM;AAG5D,8BAAgBA,QAAO,OACrB,IAAI,SACA,eAAe,OAAO,SAAC,GAAC;AACtB,oBAAMoB,OAAM,eAAe,CAAC;AAC5B,oBAAI,eAAa,OAAOA,IAAG;AAAG,yBAAO;AACrC,+BAAa,OAAOA,IAAG;AACvB,uBAAO;eACR,IACD,eACG,IAAI,SAAC,GAAC;AAAK,uBAAA,eAAe,CAAC;cAAC,CAAA,EAC5B,OAAO,SAAC,GAAC;AACR,oBAAI,eAAa,OAAO,CAAC;AAAG,yBAAO;AACnC,+BAAa,OAAO,CAAC;AACrB,uBAAO;eACR,CAAC;AAEV;;YAEF,KAAK,OAAO;AACV,kBAAM,WAAS,IAAIT,UAAQ,EAAG,QAC5B,GAAG,OAAO,IAAI,SAAC,GAAC;AAAK,uBAAA,eAAe,CAAC;cAAC,CAAA,CAAC;AAEzC,8BAAgBX,QACb;gBAEC,SAAC,MAAI;AAAK,yBAAA,CAAC,SAAO,OAAO,IAAI,SAAS,eAAe,IAAI,IAAI,IAAI;gBAAC;cAAA,EAEnE;gBAEC,IAAI,SACA,iBACA,eAAe,IAAI,SAAC,GAAC;AAAK,yBAAA,eAAe,CAAC;gBAAC,CAAA;cAAC;AAEpD;;YAEF,KAAK;AACH,kBAAM,iBAAe,IAAIW,UAAQ,EAAG,QAAQ,GAAG,IAAI;AACnD,8BAAgBX,QAAO,OACrB,SAAC,MAAI;AACH,uBAAA,CAAC,eAAa,OAAO,IAAI,SAAS,eAAe,IAAI,IAAI,IAAI;eAAC;AAGlE;YACF,KAAK;AACH,kBAAM,UAAQ,GAAG;AACjB,8BAAgBA,QAAO,OACrB,SAAC,MAAI;AAAK,uBAAA,CAAC,cAAc,eAAe,IAAI,GAAG,OAAK;cAAC,CAAA;AAEvD;;AAEJ,iBAAO;WACN,MAAM;AAGT,YAAI,gBAAgB;AAAQ,iBAAO;AAGnC,oBAAY,KAAK,SAAC,GAAG,GAAC;AACpB,iBAAAtB,KAAI,qBAAqB,CAAC,GAAG,qBAAqB,CAAC,CAAC,KACpDA,KAAI,eAAe,CAAC,GAAG,eAAe,CAAC,CAAC;SAAC;AAI3C,YAAI,IAAI,SAAS,IAAI,QAAQ,UAAU;AACrC,cAAI,YAAY,SAAS,IAAI,OAAO;AAClC,wBAAY,SAAS,IAAI;qBAChB,OAAO,WAAW,IAAI,SAAS,YAAY,SAAS,IAAI,OAAO;AAIxE,uBAAW,QAAQ;;;AAGvB,eAAO,YAAY,OAAO,OAAO,WAAW,IAAa;MAC3D;eC9HgB,eAAe,IAAoB,IAAkB;AACnE,eACEA,KAAI,GAAG,OAAO,GAAG,KAAK,MAAM,KAC5BA,KAAI,GAAG,OAAO,GAAG,KAAK,MAAM,KAC5B,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG,aACxB,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG;MAE5B;eCPgB,cAAc,QAAa,QAAa,YAAqB,YAAmB;AAC9F,YAAI,WAAW;AAAW,iBAAO,WAAW,SAAY,KAAK;AAC7D,YAAI,WAAW;AAAW,iBAAO;AACjC,YAAM,IAAIA,KAAI,QAAQ,MAAM;AAC5B,YAAI,MAAM,GAAG;AACX,cAAI,cAAc;AAAY,mBAAO;AACrC,cAAI;AAAY,mBAAO;AACvB,cAAI;AAAY,mBAAO;;AAEzB,eAAO;MACT;eAEgB,cAAc,QAAa,QAAa,YAAqB,YAAmB;AAC9F,YAAI,WAAW;AAAW,iBAAO,WAAW,SAAY,IAAI;AAC5D,YAAI,WAAW;AAAW,iBAAO;AACjC,YAAM,IAAIA,KAAI,QAAQ,MAAM;AAC5B,YAAI,MAAM,GAAG;AACX,cAAI,cAAc;AAAY,mBAAO;AACrC,cAAI;AAAY,mBAAO;AACvB,cAAI;AAAY,mBAAO;;AAEzB,eAAO;MACT;eAEgB,aAAa,IAAoB,IAAkB;AACjE,eACE,cAAc,GAAG,OAAO,GAAG,OAAO,GAAG,WAAW,GAAG,SAAS,KAAK,KACjE,cAAc,GAAG,OAAO,GAAG,OAAO,GAAG,WAAW,GAAG,SAAS,KAAK;MAErE;eCXgB,oBACd,QACA,WACAL,OACA,KAA8D;AAE9D,YAAM,WAAW,MAAM,SAAA,OAAS,QAAM,GAAA,EAAA,OAAI,SAAS,CAAE;AACrD,YAAI,CAAC;AAAU,iBAAO,CAAA;AACtB,YAAM,UAAU,SAAS,QAAQA,KAAI;AACrC,YAAI,CAAC;AAAS,iBAAO,CAAC,MAAM,OAAO,UAAU,IAAI;AACjD,YAAM,YAAY,IAAI,QAAQ,IAAI,MAAM,MAAM,OAAO;AACrD,YAAM,UAAU,QAAQ,aAAa,EAAE;AACvC,YAAI,CAAC;AAAS,iBAAO,CAAC,MAAM,OAAO,UAAU,IAAI;AAEjD,gBAAQA,OAAI;UACV,KAAK;AACH,gBAAM,aAAa,QAAQ,KACzB,SAAC,OAAK;AACJ,qBAAC,MAAM,IAA2B,UAAU,IAAI,SAC/C,MAAM,IAA2B,WAAW,IAAI,UACjD,eAAe,MAAM,IAAI,MAAM,OAAO,IAAI,MAAM,KAAK;aAAC;AAE1D,gBAAI;AACF,qBAAO;gBACL;gBACA;gBACA;gBACA;;AAEJ,gBAAM,aAAa,QAAQ,KAAK,SAAC,OAAK;AACpC,kBAAM,QAAQ,WAAW,MAAM,MAAM,MAAM,IAAI,QAAQ;AACvD,qBACE,SAAS,IAAI,UACZ,IAAI,SAAU,MAAM,IAA2B,SAAS,SACzD,aAAa,MAAM,IAAI,MAAM,OAAO,IAAI,MAAM,KAAK;aAEtD;AACD,mBAAO,CAAC,YAAY,OAAO,UAAU,OAAO;UAC9C,KAAK;AACH,gBAAM,aAAa,QAAQ,KAAK,SAAC,OAAK;AACpC,qBAAA,eAAe,MAAM,IAAI,MAAM,OAAO,IAAI,MAAM,KAAK;aAAC;AAExD,mBAAO,CAAC,YAAY,CAAC,CAAC,YAAY,UAAU,OAAO;;MAEzD;eC9DgB,sBAAsB,YAAwB,WAAyB,SAAmB,QAAmB;AAC3H,mBAAW,YAAY,IAAI,OAAO;AAClC,eAAO,iBAAiB,SAAS,WAAA;AAC/B,qBAAW,YAAY,OAAO,OAAO;AACrC,cAAI,WAAW,YAAY,SAAS,GAAG;AACrC,6BAAiB,YAAY,SAAS;;SAEzC;MACH;AAGA,eAAS,iBAAiB,YAAwB,WAAuB;AACvE,mBAAW,WAAA;AACT,cAAI,WAAW,YAAY,SAAS,GAAG;AACrC,yBAAa,WAAW,UAAU;;WAEnC,GAAI;MACT;ACCO,UAAM,kBAAsC;QACjD,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ,SAAC,MAAI;AACX,cAAM,SAAS,KAAK,OAAO;AAC3B,cAAM,SAAM,SAAA,SAAA,CAAA,GACP,IAAI,GAAA,EACP,aAAa,SAAC,QAAQ,MAAM,SAAO;AACjC,gBAAM,WAAW,KAAK,YACpB,QACA,MACA,OAAO;AAMT,gBAAI,SAAS,aAAa;AACxB,kBAAM,OAAK,IAAI,gBAAe;AACtB,kBAAA,SAAW,KAAE;AACrB,kBAAM,iBAAiB,SAAC,cAAqB;AAAK,uBAAA,WAAA;AAChD,uBAAG,MAAK;AACR,sBAAI,SAAS,aAAa;AAExB,wBAAM,wBAAsB,oBAAI,IAAG;AAGnC,6BAAwB,KAAA,GAAA,WAAA,QAAA,KAAA,SAAA,QAAA,MAAQ;AAA3B,0BAAM,YAAS,SAAA,EAAA;AAClB,0BAAM,WAAW,MAAM,SAAA,OAAS,QAAM,GAAA,EAAA,OAAI,SAAS,CAAE;AACrD,0BAAI,UAAU;AACZ,4BAAM,QAAQ,KAAK,MAAM,SAAS;AAElC,4BAAM,MAAM,SAAS,cAAc,OACjC,SAAC,IAAE;AAAK,iCAAA,GAAG,UAAU;wBAAQ,CAAA;AAG/B,4BAAI,SAAS,aAAa,gBAAgB,SAAS,cAAc;AAE/D,mCAAsBD,MAAA,GAAA,KAAA,OAAO,OAC3B,SAAS,QAAQ,KAAK,GADFA,MAAA,GAAA,QAAAA,OAEnB;AAFE,gCAAM,UAAO,GAAAA,GAAA;AAGhB,qCAAoB,KAAA,GAAA,KAAA,QAAQ,MAAK,GAAb,KAAA,GAAA,QAAA,MAAiB;AAAhC,kCAAM,QAAK,GAAA,EAAA;AACd,kCAAI,eAAe,MAAM,QAAQ,SAAS,YAAY,GAAG;AACvD,6CAAa,SAAS,KAAK;AAC3B,sCAAM,YAAY,QAAQ,SAAC,SAAO;AAAK,yCAAA,sBAAoB,IAAI,OAAO;gCAAC,CAAA;;;;mCAIpE,IAAI,SAAS,GAAG;AAEzB,mCAAS,gBAAgB,SAAS,cAAc,OAC9C,SAAC,IAAE;AAAK,mCAAA,GAAG,UAAU;0BAAQ,CAAA;AAG/B,mCAAsB,KAAA,GAAA,KAAA,OAAO,OAC3B,SAAS,QAAQ,KAAK,GADF,KAAA,GAAA,QAAA,MAEnB;AAFE,gCAAM,UAAO,GAAA,EAAA;AAGhB,qCAAoB,KAAA,GAAA,KAAA,QAAQ,MAAK,GAAb,KAAA,GAAA,QAAA,MAAiB;AAAhC,kCAAM,QAAK,GAAA,EAAA;AACd,kCACE,MAAM,OAAO,QACb,SAAS,cAET;AACA,oCAAI,gBAAgB,CAAC,MAAM,OAAO;AAChC,sCAAM,gBAAgB,OAAO,SAAS,MAAM,GAAG;AAC/C,sCAAM,SAAS,mBACb,MAAM,KACN,MAAM,KACN,KACA,OACA,OACA,aAAa;AAEf,sCAAI,MAAM,OAAO;AAEf,iDAAa,SAAS,KAAK;AAC3B,0CAAM,YAAY,QAAQ,SAAC,SAAO;AAAK,6CAAA,sBAAoB,IAAI,OAAO;oCAAC,CAAA;6CAC9D,WAAW,MAAM,KAAK;AAC/B,0CAAM,MAAM;AAEZ,0CAAM,UAAU,aAAa,QAAQ,EAAC,QAAQ,OAAM,CAA+B;;uCAQhF;AACL,sCAAI,MAAM,OAAO;AAGf,iDAAa,SAAS,KAAK;;AAI7B,wCAAM,YAAY,QAAQ,SAAC,SAAO;AAAK,2CAAA,sBAAoB,IAAI,OAAO;kCAAC,CAAA;;;;;;;;AAQrF,0CAAoB,QAAQ,SAAC,SAAO;AAAK,6BAAA,QAAO;oBAAE,CAAA;;;cAErD;AACD,uBAAS,iBAAiB,SAAS,eAAe,KAAK,GAAG;gBACxD;eACD;AACD,uBAAS,iBAAiB,SAAS,eAAe,KAAK,GAAG;gBACxD;eACD;AACD,uBAAS,iBAAiB,YAAY,eAAe,IAAI,GAAG;gBAC1D;eACD;;AAEH,mBAAO;aAET,OAAK,SAAC,WAAiB;AACrB,gBAAM,YAAY,KAAK,MAAM,SAAS;AACtC,gBAAM,UAAU,UAAU,OAAO;AACjC,gBAAM,UAAO,SAAA,SAAA,CAAA,GACR,SAAS,GAAA,EACZ,QAAM,SAAC,KAAwB;AAC7B,kBAAM,QAAQ,IAAI;AAClB,kBACE,QAAQ,YACR,MAAM,GAAG,SAAS,UAAU,cAC5B,MAAM,YACN,MAAM,SAAS,SAAS,aACxB;AAEA,uBAAO,UAAU,OAAO,GAAG;;AAG7B,kBAAM,WAAW,MAAM,SAAA,OAAS,QAAM,GAAA,EAAA,OAAI,SAAS,CAAE;AACrD,kBAAI,CAAC;AAAU,uBAAO,UAAU,OAAO,GAAG;AAE1C,kBAAM,UAAU,UAAU,OAAO,GAAG;AACpC,mBAAK,IAAI,SAAS,SAAS,IAAI,SAAS,WAAW,IAAI,OAAO,UAAU,MAAM,iBAAiB,SAAS,GAAG,EAAE,KAAK,SAAA,KAAG;AAAI,uBAAA,OAAO;cAAI,CAAA,IAAI;AAGtI,wBAAQ,KAAK,SAAC,KAAG;AAEf,sBAAM,sBAAmB,SAAA,SAAA,CAAA,GACpB,GAAG,GAAA,EACN,QAAQ,IAAI,OAAO,IAAI,SAAC,OAAO,GAAC;;AAC9B,wBAAI,IAAI,SAAS,CAAC;AAAG,6BAAO;AAC5B,wBAAM,iBAAeA,MAAA,QAAQ,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAE,SAAS,GAAG,KAC9C,UAAU,KAAK,iBAEZ,KAAK;AAEZ,iCAAa,cAAc,QAAQ,SAAS,IAAI,QAAS,CAAC,CAAC;AAC3D,2BAAO;mBACR,EAAC,CAAA;AAEJ,sBAAM,cAAc,6BAA6B,UAAU,qBAAqB,GAAG;AACnF,2BAAS,cAAc,KAAK,WAAW;AAIvC,iCAAe,WAAA;AAAI,2BAAA,IAAI,gBAAgB,wBAAwB,IAAI,YAAY;kBAAC,CAAA;iBACjF;qBACI;AAEL,yBAAS,cAAc,KAAK,GAAG;AAE/B,oBAAI,gBAAgB,wBAAwB,IAAI,YAAY;AAC5D,wBAAQ,KAAK,SAAC,KAAG;AACf,sBAAI,IAAI,cAAc,GAAG;AAEvB,iCAAa,SAAS,eAAe,GAAG;AACxC,wBAAM,cAAc,6BAA6B,UAAU,KAAK,GAAG;AACnE,wBAAI,aAAa;AACf,+BAAS,cAAc,KAAK,WAAW;;AAEzC,wBAAI,gBAAgB,wBAAwB,IAAI,YAAY;;iBAE/D;AACD,wBAAQ,MAAM,WAAA;AAEZ,+BAAa,SAAS,eAAe,GAAG;AACxC,sBAAI,gBAAgB,wBAAwB,IAAI,YAAY;iBAC7D;;AAEH,qBAAO;eAET,OAAK,SAAC,KAAuB;;AAC3B,kBAAI,CAAC,kBAAkB,KAAK,SAAS,KAAK,CAAC,kBAAkB,SAAS,GAAG;AAAG,uBAAO,UAAU,MAAM,GAAG;AACtG,kBAAM,kBACJA,MAAC,IAAyB,WAAK,QAAAA,QAAA,SAAA,SAAAA,IAAE,GAAG,SAAS,WAAU;AACnD,kBAAA,KAAsB,KAApB,UAAO,GAAA,SAAE,SAAM,GAAA;AACnB,kBAAA,KACF,oBAAoB,QAAQ,WAAW,SAAS,GAAG,GADhD,aAAU,GAAA,CAAA,GAAE,aAAU,GAAA,CAAA,GAAE,WAAQ,GAAA,CAAA,GAAE,YAAS,GAAA,CAAA;AAEhD,kBAAI,cAAc,YAAY;AAC5B,2BAAW,SAAS,IAAI;qBAWnB;AAKL,oBAAM,UAAU,UAAU,MAAM,GAAG,EAAE,KAAK,SAAC,KAAG;AAE5C,sBAAM,SAAS,IAAI;AACnB,sBAAI;AAAY,+BAAW,MAAM;AACjC,sBAAI,eAAe;AAMjB,6BAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC7C,6BAAO,OAAO,OAAO,CAAC,CAAC;;AAEzB,2BAAO,OAAO,MAAM;yBACf;AAIL,wBAAI,SAAS,UAAU,MAAM;;AAE/B,yBAAO;iBACR,EAAE,MAAM,SAAA,OAAK;AAIZ,sBAAI,aAAa;AAAY,iCAAa,WAAW,UAAU;AAC/D,yBAAO,QAAQ,OAAO,KAAK;iBAC5B;AACD,6BAAa;kBACX,QAAQ,IAAI;kBACZ;kBACA,aAAa,oBAAI,IAAG;kBACpB,MAAM;kBACN;kBACA,OAAO;;AAET,oBAAI,WAAW;AACb,4BAAU,KAAK,UAAU;uBACpB;AACL,8BAAY,CAAC,UAAU;AACvB,sBAAI,CAAC,UAAU;AACb,+BAAW,MAAM,SAAA,OAAS,QAAM,GAAA,EAAA,OAAI,SAAS,CAAE,IAAI;sBACjD,SAAS;wBACP,OAAO,CAAA;wBACP,OAAO,CAAA;;sBAET,MAAM,oBAAI,IAAG;sBACb,eAAe,CAAA;sBACf,iBAAiB,CAAA;;;AAGrB,2BAAS,QAAQ,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,IAAI;;;AAGzD,oCAAsB,YAAY,WAAY,SAAS,MAAM;AAC7D,qBAAO,WAAW,QAAQ,KAAK,SAAC,KAAwB;AACtD,uBAAO;kBACL,QAAQ,mBACN,IAAI,QACJ,KACA,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAU,eACV,WACA,YACA,aAAa;;eAGlB;cACF,CAAA;AAEH,mBAAO;YACR,CAAA;AAEH,iBAAO;;;eC/SK,OACd,QACA,OAAY;AAEZ,eAAO,IAAI,MAAM,QAAQ;UACvB,KAAG,SAAEyC,SAAQ,MAAM,UAAQ;AAIzB,gBAAI,SAAS;AAAM,qBAAO;AAC1B,mBAAO,QAAQ,IAAIA,SAAQ,MAAM,QAAQ;;SAE5C;MACH;;AC6EE,iBAAAH,OAAY,MAAc,SAAsB;AAAhD,cAAA,QAAA;AAlBA,eAAA,eAA0F,CAAA;AAM1F,eAAA,QAAgB;AAad,cAAM,OAAQA,OAAkC;AAChD,eAAK,WAAW,UAAO,SAAA;YAErB,QAASA,OAAkC;YAC3C,UAAU;YAEV,WAAW,KAAK;YAChB,aAAa,KAAK;YAClB,OAAO;UAAQ,GACZ,OAAO;AAEZ,eAAK,QAAQ;YACX,WAAW,QAAQ;YACnB,aAAa,QAAQ;;AAGrB,cAAA,SACE,QAAO;AACX,eAAK,YAAY,CAAA;AACjB,eAAK,YAAY,CAAA;AACjB,eAAK,cAAc,CAAA;AACnB,eAAK,aAAa,CAAA;AAClB,eAAK,QAAQ;AACb,eAAK,SAAS;AACd,cAAM,QAAsB;YAC1B,aAAa;YACb,eAAe;YACf,mBAAmB;YACnB,cAAc;YACd,gBAAgB;YAChB,gBAAgB;YAChB,YAAY;YACZ,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,UAAU,QAAQ;;AAEpB,gBAAM,iBAAiB,IAAI5B,aAAQ,SAAA,SAAO;AACxC,kBAAM,iBAAiB;WACxB;AACD,gBAAM,gBAAgB,IAAIA,aAAQ,SAAC,GAAG,QAAM;AAC1C,kBAAM,aAAa;WACpB;AACD,eAAK,SAAS;AACd,eAAK,OAAO;AACZ,eAAK,KAAK,OAAO,MAAM,YAAY,WAAW,iBAAiB,SAAS,EAAE,OAAO,CAAC,iBAAiB,GAAG,EAAC,CAAE;AACzG,eAAK,GAAG,MAAM,YAAY,SAAS,KAAK,GAAG,MAAM,WAAW,SAAA,WAAS;AACnE,mBAAO,SAAC,YAAY,SAAO;AACxB,cAAA4B,OAAkC,IAAI,WAAA;AACrC,oBAAMW,SAAQ,MAAK;AACnB,oBAAIA,OAAM,cAAc;AAEtB,sBAAI,CAACA,OAAM;AAAavC,iCAAQ,QAAO,EAAG,KAAK,UAAU;AAEzD,sBAAI;AAAS,8BAAU,UAAU;2BACxBuC,OAAM,mBAAmB;AAElC,kBAAAA,OAAM,kBAAkB,KAAK,UAAU;AACvC,sBAAI;AAAS,8BAAU,UAAU;uBAC5B;AAEL,4BAAU,UAAU;AAEpB,sBAAM,OAAK;AACX,sBAAI,CAAC;AAAS,8BAAU,SAAS,cAAW;AAC1C,2BAAG,GAAG,MAAM,YAAY,UAAU;AAClC,2BAAG,GAAG,MAAM,YAAY,WAAW;qBACpC;;eAEJ;;WAEJ;AAGD,eAAK,aAAa,4BAA4B,IAAI;AAClD,eAAK,QAAQ,uBAAuB,IAAI;AACxC,eAAK,cAAc,6BAA6B,IAAI;AACpD,eAAK,UAAU,yBAAyB,IAAI;AAC5C,eAAK,cAAc,6BAA6B,IAAI;AAKpD,eAAK,GAAG,iBAAiB,SAAA,IAAE;AAKzB,gBAAI,GAAG,aAAa;AAClB,sBAAQ,KAAK,iDAAA,OAAiD,MAAK,MAAI,0CAAA,CAA0C;;AAEjH,sBAAQ,KAAK,gDAAA,OAAgD,MAAK,MAAI,iDAAA,CAAiD;AACzH,kBAAK,MAAM,EAAC,iBAAiB,MAAK,CAAC;WAOpC;AACD,eAAK,GAAG,WAAW,SAAA,IAAE;AACnB,gBAAI,CAAC,GAAG,cAAc,GAAG,aAAa,GAAG;AACvC,sBAAQ,KAAK,iBAAA,OAAiB,MAAK,MAAI,gBAAA,CAAgB;;AAEvD,sBAAQ,KAAK,YAAA,OAAY,MAAK,MAAI,gDAAA,EAAA,OAAiD,GAAG,aAAa,EAAE,CAAE;WAC1G;AAED,eAAK,UAAU,UAAU,QAAQ,WAAiC;AAElE,eAAK,qBAAqB,SACxB,MACA,YACA,UACA,mBAA+B;AAAK,mBAAA,IAAI,MAAK,YAAY,MAAM,YAAY,UAAU,MAAK,SAAS,6BAA6B,iBAAiB;UAAC;AAEpJ,eAAK,iBAAiB,SAAA,IAAE;AACtB,kBAAK,GAAG,SAAS,EAAE,KAAK,EAAE;AAE1B,wBACG,OAAO,SAAA,GAAC;AAAI,qBAAA,EAAE,SAAS,MAAK,QAAQ,MAAM,SAAQ,CAAC,EAAE,OAAO;YAAO,CAAA,EACnE,IAAI,SAAA,GAAC;AAAI,qBAAA,EAAE,GAAG,eAAe,EAAE,KAAK,EAAE;YAAC,CAAA;;AAI5C,eAAK,IAAI,6BAA6B;AACtC,eAAK,IAAI,eAAe;AACxB,eAAK,IAAI,uBAAuB;AAChC,eAAK,IAAI,sBAAsB;AAC/B,eAAK,IAAI,eAAe;AAExB,cAAM,QAAQ,IAAI,MAAM,MAAM;YAC5B,KAAK,SAAC,GAAG,MAAM,UAAQ;AACrB,kBAAI,SAAS;AAAQ,uBAAO;AAC5B,kBAAI,SAAS;AAAS,uBAAO,SAAC,WAAiB;AAAK,yBAAA,OAAO,MAAK,MAAM,SAAS,GAAG,KAAK;gBAAC;AACxF,kBAAM,KAAK,QAAQ,IAAI,GAAG,MAAM,QAAQ;AACxC,kBAAI,cAAc;AAAO,uBAAO,OAAO,IAAI,KAAK;AAChD,kBAAI,SAAS;AAAU,uBAAQ,GAAe,IAAI,SAAA,GAAC;AAAI,yBAAA,OAAO,GAAG,KAAK;gBAAC,CAAA;AACvE,kBAAI,SAAS;AAAsB,uBAAO,WAAA;AACxC,sBAAM,KAAmB,GAAsC,MAAM,MAAM,SAAS;AACpF,yBAAO,OAAO,IAAI,KAAK;;AAEzB,qBAAO;;WAEV;AACD,eAAK,MAAM;AAGX,iBAAO,QAAQ,SAAA,OAAK;AAAI,mBAAA,MAAM,KAAI;UAAC,CAAA;;AAGrC,QAAAX,OAAA,UAAA,UAAA,SAAQ,eAAqB;AAC3B,cAAI,MAAM,aAAa,KAAK,gBAAgB;AAAK,kBAAM,IAAI,WAAW,KAAK,wCAAwC;AACnH,0BAAgB,KAAK,MAAM,gBAAgB,EAAE,IAAI;AACjD,cAAI,KAAK,SAAS,KAAK,OAAO;AAC5B,kBAAM,IAAI,WAAW,OAAO,0CAA0C;AACxE,eAAK,QAAQ,KAAK,IAAI,KAAK,OAAO,aAAa;AAC/C,cAAM,WAAW,KAAK;AACtB,cAAI,kBAAkB,SAAS,OAC7B,SAAA,GAAC;AAAI,mBAAA,EAAE,KAAK,YAAY;UAAa,CAAA,EAAE,CAAC;AAC1C,cAAI;AAAiB,mBAAO;AAC5B,4BAAkB,IAAI,KAAK,QAAQ,aAAa;AAChD,mBAAS,KAAK,eAAe;AAC7B,mBAAS,KAAK,iBAAiB;AAC/B,0BAAgB,OAAO,CAAA,CAAE;AAEzB,eAAK,OAAO,aAAa;AACzB,iBAAO;;AAGT,QAAAA,OAAA,UAAA,aAAA,SAAc,IAAoB;AAAlC,cAAA,QAAA;AACE,iBAAQ,KAAK,UAAU,KAAK,OAAO,gBAAgB,IAAI,cAAc,KAAK,QAAS,GAAE,IAAK,IAAI5B,aAAW,SAAC,SAAS,QAAM;AACvH,gBAAI,MAAK,OAAO,cAAc;AAG5B,qBAAO,OAAO,IAAI,WAAW,eAAe,MAAK,OAAO,WAAW,CAAC;;AAEtE,gBAAI,CAAC,MAAK,OAAO,eAAe;AAC9B,kBAAI,CAAC,MAAK,OAAO,UAAU;AACzB,uBAAO,IAAI,WAAW,eAAc,CAAE;AACtC;;AAEF,oBAAK,KAAI,EAAG,MAAM,GAAG;;AAEvB,kBAAK,OAAO,eAAe,KAAK,SAAS,MAAM;WAChD,EAAE,KAAK,EAAE;;AAGZ,QAAA4B,OAAA,UAAA,MAAA,SAAItC,KAAgD;cAA/C,QAAKA,IAAA,OAAE,SAAMA,IAAA,QAAE,QAAKA,IAAA,OAAE,OAAIA,IAAA;AAC7B,cAAI;AAAM,iBAAK,MAAM,EAAC,OAAO,KAAI,CAAC;AAClC,cAAM,cAAc,KAAK,aAAa,KAAK,MAAM,KAAK,aAAa,KAAK,IAAI,CAAA;AAC5E,sBAAY,KAAK,EAAC,OAAO,QAAQ,OAAO,SAAS,OAAO,KAAK,OAAO,KAAI,CAAC;AACzE,sBAAY,KAAK,SAAC,GAAG,GAAC;AAAK,mBAAA,EAAE,QAAQ,EAAE;UAAK,CAAA;AAG5C,iBAAO;;AAKT,QAAAsC,OAAA,UAAA,QAAA,SAAMtC,KAAmF;cAAlF,QAAKA,IAAA,OAAE,OAAIA,IAAA,MAAE,SAAMA,IAAA;AACxB,cAAI,SAAS,KAAK,aAAa,KAAK,GAAG;AACrC,iBAAK,aAAa,KAAK,IAAI,KAAK,aAAa,KAAK,EAAE,OAAO,SAAA,IAAE;AAC3D,qBAAA,SAAS,GAAG,WAAW,SACvB,OAAO,GAAG,SAAS,OACnB;aAAK;;AAET,iBAAO;;AAGT,QAAAsC,OAAA,UAAA,OAAA,WAAA;AAAA,cAAA,QAAA;AACE,iBAAO;YACL;YACA,WAAA;AAAM,qBAAA,UAAU,KAAI;YAAC;UAAA;;AAIzB,QAAAA,OAAA,UAAA,SAAA,WAAA;AACE,cAAM,QAAQ,KAAK;AACnB,cAAM,MAAM,YAAY,QAAQ,IAAI;AACpC,cAAI,OAAO;AAAG,wBAAY,OAAO,KAAK,CAAC;AACvC,cAAI,KAAK,OAAO;AACd,gBAAI;AAAE,mBAAK,MAAM,MAAK;qBAAa,GAAG;YAAA;AACtC,iBAAK,QAAQ;;AAGf,cAAI,CAAC,MAAM,eAAe;AAIxB,kBAAM,iBAAiB,IAAI5B,aAAQ,SAAA,SAAO;AACxC,oBAAM,iBAAiB;aACxB;AACD,kBAAM,gBAAgB,IAAIA,aAAQ,SAAC,GAAG,QAAM;AAC1C,oBAAM,aAAa;aACpB;;;AAIL,QAAA4B,OAAA,UAAA,QAAA,SAAMtC,KAA2C;cAA3C,KAAAA,QAAA,SAAoB,EAAC,iBAAiB,KAAI,IAACA,KAA1C,kBAAe,GAAA;AACpB,cAAM,QAAQ,KAAK;AACnB,cAAI,iBAAiB;AACnB,gBAAI,MAAM,eAAe;AAEvB,oBAAM,WAAW,IAAI,WAAW,eAAc,CAAE;;AAElD,iBAAK,OAAM;AACX,kBAAM,WAAW;AACjB,kBAAM,cAAc,IAAI,WAAW,eAAc;iBAC5C;AACL,iBAAK,OAAM;AACX,kBAAM,WAAW,KAAK,SAAS,YAC7B,MAAM;AACR,kBAAM,eAAe;AACrB,kBAAM,cAAc;;;AAIxB,QAAAsC,OAAA,UAAA,SAAA,SAAO,cAAsC;AAA7C,cAAA,QAAA;AAAO,cAAA,iBAAA,QAAA;AAAA,2BAAA,EAAgB,iBAAiB,KAAI;UAAC;AAE3C,cAAM,sBAAsB,UAAU,SAAS,KAAK,OAAO,UAAU,CAAC,MAAM;AAC5E,cAAM,QAAQ,KAAK;AACnB,iBAAO,IAAI5B,aAAQ,SAAC,SAAS,QAAM;AACjC,gBAAM,WAAW,WAAA;AACf,oBAAK,MAAM,YAAY;AACvB,kBAAI,MAAM,MAAK,MAAM,UAAU,eAAe,MAAK,IAAI;AACvD,kBAAI,YAAY,KAAK,WAAA;AACnB,mCAAmB,MAAK,OAAO,MAAK,IAAI;AACxC,wBAAO;eACR;AACD,kBAAI,UAAU,mBAAmB,MAAM;AACvC,kBAAI,YAAY,MAAK;;AAGvB,gBAAI;AAAqB,oBAAM,IAAI,WAAW,gBAAgB,8CAA8C;AAC5G,gBAAI,MAAM,eAAe;AACvB,oBAAM,eAAe,KAAK,QAAQ;mBAC7B;AACL,uBAAQ;;WAEX;;AAGH,QAAA4B,OAAA,UAAA,YAAA,WAAA;AACE,iBAAO,KAAK;;AAGd,QAAAA,OAAA,UAAA,SAAA,WAAA;AACE,iBAAO,KAAK,UAAU;;AAGxB,QAAAA,OAAA,UAAA,gBAAA,WAAA;AACE,cAAM,cAAc,KAAK,OAAO;AAChC,iBAAO,eAAgB,YAAY,SAAS;;AAG9C,QAAAA,OAAA,UAAA,YAAA,WAAA;AACE,iBAAO,KAAK,OAAO,gBAAgB;;AAGrC,QAAAA,OAAA,UAAA,oBAAA,WAAA;AACE,iBAAO,KAAK,OAAO;;AAGrB,eAAA,eAAIA,OAAA,WAAA,UAAM;eAAV,WAAA;AAAA,gBAAA,QAAA;AACE,mBAAO,KAAK,KAAK,UAAU,EAAE,IAAI,SAAA,MAAI;AAAI,qBAAA,MAAK,WAAW,IAAI;YAAC,CAAA;;;;;AAGhE,QAAAA,OAAA,UAAA,cAAA,WAAA;AACE,cAAM,OAAO,uBAAuB,MAAM,MAAM,SAAS;AACzD,iBAAO,KAAK,aAAa,MAAM,MAAM,IAAI;;AAG3C,QAAAA,OAAA,UAAA,eAAA,SAAa,MAAuB,QAAgC,WAAmB;AAAvF,cAAA,QAAA;AACE,cAAI,oBAAoB,IAAI;AAE5B,cAAI,CAAC,qBAAqB,kBAAkB,OAAO,QAAQ,KAAK,QAAQ,GAAG,MAAM;AAAI,gCAAoB;AACzG,cAAM,mBAAmB,KAAK,QAAQ,GAAG,MAAM;AAC/C,iBAAO,KAAK,QAAQ,KAAK,EAAE,EAAE,QAAQ,KAAK,EAAE;AAC5C,cAAI,SACA;AAEJ,cAAI;AAIA,yBAAa,OAAO,IAAI,SAAA,OAAK;AACzB,kBAAI,YAAY,iBAAiB,MAAK,QAAQ,MAAM,OAAO;AAC3D,kBAAI,OAAO,cAAc;AAAU,sBAAM,IAAI,UAAU,iFAAiF;AACxI,qBAAO;aACV;AAKD,gBAAI,QAAQ,OAAO,SAAS;AAC1B,wBAAU;qBACH,QAAQ,QAAQ,QAAQ;AAC/B,wBAAU;;AAER,oBAAM,IAAI,WAAW,gBAAgB,+BAA+B,IAAI;AAE5E,gBAAI,mBAAmB;AAEnB,kBAAI,kBAAkB,SAAS,YAAY,YAAY,WAAW;AAC9D,oBAAI,kBAAkB;AAElB,sCAAoB;;AAEnB,wBAAM,IAAI,WAAW,eAAe,wFAAwF;;AAErI,kBAAI,mBAAmB;AACnB,2BAAW,QAAQ,SAAA,WAAS;AACxB,sBAAI,qBAAqB,kBAAkB,WAAW,QAAQ,SAAS,MAAM,IAAI;AAC7E,wBAAI,kBAAkB;AAElB,0CAAoB;;AAEnB,4BAAM,IAAI,WAAW,eAAe,WAAW,YAChD,sCAAsC;;iBAEjD;;AAEL,kBAAI,oBAAoB,qBAAqB,CAAC,kBAAkB,QAAQ;AAEpE,oCAAoB;;;mBAGvB,GAAG;AACR,mBAAO,oBACH,kBAAkB,SAAS,MAAM,SAAC,GAAG,QAAM;AAAM,qBAAO,CAAC;YAAE,CAAC,IAC5D,UAAW,CAAC;;AAGpB,cAAM,mBAAmB,sBAAsB,KAAK,MAAM,MAAM,SAAS,YAAY,mBAAmB,SAAS;AACjH,iBAAQ,oBACJ,kBAAkB,SAAS,SAAS,kBAAkB,MAAM,IAC5D,IAAI,QAIA,OAAO,IAAI,WAAW,WAAA;AAAI,mBAAA,MAAK,WAAW,gBAAgB;UAAC,CAAA,IAC3D,KAAK,WAAY,gBAAgB;;AAK3C,QAAAA,OAAA,UAAA,QAAA,SAAM,WAAiB;AACrB,cAAI,CAAC,OAAO,KAAK,YAAY,SAAS,GAAG;AACvC,kBAAM,IAAI,WAAW,aAAa,SAAA,OAAS,WAAS,iBAAA,CAAiB;;AACvE,iBAAO,KAAK,WAAW,SAAS;;AAEpC,eAAAA;MAAA,EAAC;ACheD,UAAM,mBACJ,OAAO,WAAW,eAAe,gBAAgB,SAC7C,OAAO,aACP;AAEN,UAAA,aAAA,WAAA;AAKE,iBAAAY,YAAY,WAAkD;AAC5D,eAAK,aAAa;;AASpB,QAAAA,YAAA,UAAA,YAAA,SAAU,GAAS,OAAa,UAAc;AAC5C,iBAAO,KAAK,WACV,CAAC,KAAK,OAAO,MAAM,aAAa,EAAE,MAAM,GAAG,OAAO,SAAQ,IAAK,CAAC;;AAIpE,QAAAA,YAAA,UAAC,gBAAgB,IAAjB,WAAA;AACE,iBAAO;;AAEX,eAAAA;MAAA,EAAC;AChCM,UAAI;AAEX,UAAI;AACF,kBAAU;UAER,WAAW,QAAQ,aAAa,QAAQ,gBAAgB,QAAQ,mBAAmB,QAAQ;UAC3F,aAAa,QAAQ,eAAe,QAAQ;;MAEhD,SAAS,GAAG;AACV,kBAAU,EAAE,WAAW,MAAM,aAAa,KAAI;MAChD;eCuBgBC,WAAa,SAA6B;AACxD,YAAI,WAAW;AACf,YAAI;AACJ,YAAM,aAAa,IAAI,WAAc,SAAC,UAAQ;AAC5C,cAAM,mBAAmB,gBAAgB,OAAO;AAChD,mBAAS,QAAQ,KAAqB;AACpC,gBAAM,cAAc,oBAAmB;AACvC,gBAAI;AACF,kBAAI,kBAAkB;AACpB,wCAAuB;;AAEzB,kBAAI,KAAK,SAAS,SAAS,GAAG;AAC9B,kBAAI,kBAAkB;AAGpB,qBAAM,GAAoB,QAAQ,uBAAuB;;AAE3D,qBAAO;;AAEP,6BAAe,kBAAiB;;;AAIpC,cAAI,SAAS;AACb,cAAI;AAEJ,cAAI,YAA8B,CAAA;AAClC,cAAI,aAA+B,CAAA;AAEnC,cAAM,eAA6B;YACjC,IAAI,SAAM;AACR,qBAAO;;YAET,aAAa,WAAA;AACX,kBAAI;AAAQ;AACZ,uBAAS;AACT,kBAAI;AAAiB,gCAAgB,MAAK;AAC1C,kBAAI;AAAkB,6BAAa,eAAe,YAAY,gBAAgB;;;AAIlF,mBAAS,SAAS,SAAS,MAAM,YAAY;AAE7C,cAAI,mBAAmB;AAEvB,cAAM,UAAU,WAAA;AAAM,mBAAA,oBAAoB,QAAQ;UAAC;AAEnD,mBAAS,eAAY;AACnB,mBAAO,eAAe,YAAY,SAAS;;AAG7C,cAAM,mBAAmB,SAAC,OAAuB;AAC/C,mCAAuB,WAAW,KAAK;AACvC,gBAAI,aAAY,GAAI;AAClB,sBAAO;;;AAIX,cAAM,WAAW,WAAA;AACf,gBACE,UACA,CAAC,QAAQ,WACX;AACE;;AAEF,wBAAY,CAAA;AACZ,gBAAM,SAA2B,CAAA;AAMjC,gBAAI;AAAiB,8BAAgB,MAAK;AAC1C,8BAAkB,IAAI,gBAAe;AAErC,gBAAM,MAAwB;cAC5B;cACA,QAAQ,gBAAgB;cACxB,SAAS;cACT;cACA,OAAO;;AAET,gBAAM,MAAM,QAAQ,GAAG;AACvB,oBAAQ,QAAQ,GAAG,EAAE,KACnB,SAAC,QAAM;AACL,yBAAW;AACX,6BAAe;AACf,kBAAI,UAAU,IAAI,OAAO,SAAS;AAOhC;;AAEF,0BAAY,CAAA;AAEZ,2BAAa;AACb,kBAAI,CAAC,cAAc,UAAU,KAAK,CAAC,kBAAkB;AACnD,6BAAa,kCAAkC,gBAAgB;AAC/D,mCAAmB;;AAErB,kCAAoB,WAAA;AAAI,uBAAA,CAAC,UAAU,SAAS,QAAQ,SAAS,KAAK,MAAM;cAAC,CAAA;eAE3E,SAAC,KAAG;AACF,yBAAW;AACX,kBAAI,CAAC,CAAC,uBAAuB,YAAY,EAAE,SAAS,QAAG,QAAH,QAAG,SAAA,SAAH,IAAK,IAAI,GAAG;AAC9D,oBAAI,CAAC;AAAQ,sCAAoB,WAAA;AAC/B,wBAAI;AAAQ;AACZ,6BAAS,SAAS,SAAS,MAAM,GAAG;mBACrC;;aAEJ;;AAYL,qBAAW,SAAS,CAAC;AACrB,iBAAO;SACR;AACD,mBAAW,WAAW,WAAA;AAAM,iBAAA;QAAQ;AACpC,mBAAW,WAAW,WAAA;AAAM,iBAAA;QAAY;AACxC,eAAO;MACT;ACjIA,UAAMb,SAAQc;AAKd,YAAMd,QAAK,SAAA,SAAA,CAAA,GAIN,kBAAkB,GAAA;QAKrB,QAAM,SAAC,cAAoB;AACzB,cAAM,KAAK,IAAIA,OAAM,cAAc,EAAC,QAAQ,CAAA,EAAE,CAAC;AAC/C,iBAAO,GAAG,OAAM;;QAMlB,QAAM,SAAC,MAAY;AACjB,iBAAO,IAAIA,OAAM,MAAM,EAAE,QAAQ,CAAA,EAAE,CAAE,EAAE,KAAI,EAAG,KAAK,SAAA,IAAE;AACnD,eAAG,MAAK;AACR,mBAAO;WACR,EAAE,MAAM,uBAAuB,WAAA;AAAM,mBAAA;UAAK,CAAA;;QAM7C,kBAAgB,SAAC,IAAE;AACjB,cAAI;AACF,mBAAO,iBAAiBA,OAAM,YAAY,EAAE,KAAK,EAAE;mBACnDtC,KAAM;AACN,mBAAO,UAAU,IAAI,WAAW,WAAU,CAAE;;;QAKhD,aAAW,WAAA;AACT,mBAAS,MAAM,SAAO;AACpB,mBAAO,MAAM,OAAO;;AAEtB,iBAAO;;QAGT,mBAAiB,SAAC,WAAS;AAsBzB,iBAAO,IAAI,QACT,OAAO,IAAI,WAAW,SAAS,IAC/B,UAAS;;QAGb;QAEA,OAAO,SAAU,aAAqB;AACpC,iBAAO,WAAA;AACL,gBAAI;AACF,kBAAI,KAAK,cAAc,YAAY,MAAM,MAAM,SAAS,CAAC;AACzD,kBAAI,CAAC,MAAM,OAAO,GAAG,SAAS;AAC5B,uBAAOU,aAAQ,QAAQ,EAAE;AAC3B,qBAAO;qBACA,GAAG;AACV,qBAAO,UAAU,CAAC;;;;QAKxB,OAAO,SAAU,aAAa,MAAM,MAAI;AACtC,cAAI;AACF,gBAAI,KAAK,cAAc,YAAY,MAAM,MAAM,QAAQ,CAAA,CAAE,CAAC;AAC1D,gBAAI,CAAC,MAAM,OAAO,GAAG,SAAS;AAC5B,qBAAOA,aAAQ,QAAQ,EAAE;AAC3B,mBAAO;mBACA,GAAG;AACV,mBAAO,UAAU,CAAC;;;QAKtB,oBAAoB;UAClB,KAAK,WAAA;AAAM,mBAAA,IAAI,SAAS;UAAI;;QAG9B,SAAS,SAAU,mBAAmB,iBAAe;AAEnD,cAAM,UAAUA,aAAQ,QACtB,OAAO,sBAAsB,aAC3B4B,OAAM,kBAAkB,iBAAiB,IACzC,iBAAiB,EAClB,QAAQ,mBAAmB,GAAK;AAInC,iBAAO,IAAI,QACT,IAAI,MAAM,QAAQ,OAAO,IACzB;;QAIJ,SAAS5B;QAMT,OAAO;UACL,KAAK,WAAA;AAAM,mBAAA2C;UAAW;UACtB,KAAK,SAAA,OAAK;AACRC,qBAAe,KAA6D;;;QAKhF;QACA;QACA;QACA;QAEA;QACA,IAAI;QACJ,WAASH;QACT;QAEA;QACA;QACA;QACA;QACA;QACA;QACA,KAAG7C;QACH,MAAMR;QAEN;QAEA,QAAQ,CAAA;QAER;QAGA;QAcA,cAAc;QACd;QAGA,QAAQ;QACR,SAAS,cAAc,MAAM,GAAG,EAC7B,IAAI,SAAA,GAAC;AAAI,iBAAA,SAAS,CAAC;QAAC,CAAA,EACpB,OAAO,SAAC,GAAG,GAAG,GAAC;AAAK,iBAAA,IAAK,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC;QAAE,CAAA;MAAC,CAAA,CAAA;AAcvD,MAAAwC,OAAM,SAAS,UAAUA,OAAM,aAAa,WAAW;ACvOvD,UAAI,OAAO,kBAAkB,eAAe,OAAO,qBAAqB,aAAa;AACnF,qBAAa,kCAAkC,SAAA,cAAY;AACzD,cAAI,CAAC,oBAAoB;AACvB,gBAAI;AACJ,sBAAQ,IAAI,YAAY,gCAAgC;cACtD,QAAQ;aACT;AACD,iCAAqB;AACrB,0BAAc,OAAK;AACnB,iCAAqB;;SAExB;AACD,yBAAiB,gCAAgC,SAACtC,KAAuC;cAAtC,SAAMA,IAAA;AACvD,cAAI,CAAC,oBAAoB;AACvB,6BAAiB,MAAM;;SAE1B;MACH;eAWgB,iBAAiB,aAA6B;AAC5D,YAAI,QAAQ;AACZ,YAAI;AACF,+BAAqB;AAErB,uBAAa,eAAe,KAAK,WAAW;AAE5C,+BAAqB,aAAa,IAAI;;AAEtC,+BAAqB;;MAEzB;AAEO,UAAI,qBAAqB;ACtCzB,UAAI;AAEJ,UAAI,WAAW,WAAA;MAAA;AAEtB,UAAI,OAAO,qBAAqB,aAAa;AAC3C,mBAAW,WAAA;AACT,eAAK,IAAI,iBAAiB,8BAA8B;AACxD,aAAG,YAAY,SAAA,IAAE;AAAI,mBAAA,GAAG,QAAQ,iBAAiB,GAAG,IAAI;UAAC;;AAE3D,iBAAQ;AAUR,YAAI,OAAQ,GAAW,UAAU,YAAY;AAC1C,aAAW,MAAK;;AAMnB,qBAAa,kCAAkC,SAAC,cAAY;AAC1D,cAAI,CAAC,oBAAoB;AACvB,eAAG,YAAY,YAAY;;SAE9B;MACH;AC9BA,UAAI,OAAO,qBAAqB,aAAa;AAC3C,yBAAiB,YAAY,SAAC,OAAK;AACjC,cAAI,CAACsC,QAAM,kBAAkB,MAAM,WAAW;AAC5C,gBAAI;AAAO,sBAAQ,MAAM,oCAAoC;AAC7D,mBAAE,QAAF,OAAE,SAAA,SAAF,GAAI,MAAK;AACT,qBAAiB,KAAA,GAAA,gBAAA,aAAA,KAAA,cAAA,QAAA,MAAa;AAAzB,kBAAM,KAAE,cAAA,EAAA;AACX,iBAAG,MAAM,EAAC,iBAAiB,MAAK,CAAC;;;SAGtC;AACD,yBAAiB,YAAY,SAAC,OAAK;AACjC,cAAI,CAACA,QAAM,kBAAkB,MAAM,WAAW;AAC5C,gBAAI;AAAO,sBAAQ,MAAM,oCAAoC;AAC7D,qBAAQ;AACR,6BAAiB,EAAC,KAAK,IAAIC,UAAS,WAAW,CAAC,CAAA,CAAE,CAAC,EAAC,CAAC;;SAExD;MACH;eCvBgB1B,KAAI,OAA+C;AACjE,eAAO,IAAIE,kBAAiB,EAAC,KAAK,MAAK,CAAC;MAC1C;eCFgBwC,QAAO,OAA+C;AACpE,eAAO,IAAIxC,kBAAiB,EAAC,QAAQ,MAAK,CAAC;MAC7C;eCFgByC,eAAc,GAAW,GAAQ;AAC/C,eAAO,IAAIzC,kBAAiB,EAAC,eAAe,CAAC,GAAG,CAAC,EAAC,CAAC;MACrD;ACmBA,mBAAa,kBAAkB;AAG/BuC,eAAeD,KAAkC;;;;;;;;;;;;;;;;ACnBjD,eAASf,SAAO,cAAc,EAAC,SAASA,QAAK,CAAC;;;;;;;ACJ9C,mBAAmB;AACnB,IAAM,cAAc,OAAO,IAAI,OAAO;AACtC,IAAM,QAAQ,WAAW,WAAW,MAAM,WAAW,WAAW,IAAI,aAAAmB;AACpE,IAAI,aAAAA,QAAO,WAAW,MAAM,QAAQ;AAChC,QAAM,IAAI,MAAM,2DAA2D,aAAAA,QAAO,MAAM,QAAQ,MAAM,MAAM,EAAE;AAClH;AACA,IAAM;AAAA,EAAE;AAAA,EAAW;AAAA,EAAa;AAAA,EAAe;AAAA,EAAU;AAAA,EAAK;AAAA,EAC1D;AAAA,EAAkB;AAAA,EAAe;AAAA,EAAK;AAAO,IAAI;AAGrD,IAAO,yBAAQ;", "names": ["d", "b", "__assign", "asap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_a", "type", "resolve", "reject", "props", "Entity", "cmp", "Table", "task", "trans", "Promise", "keys", "i", "add", "args", "PropModification", "Collection", "count", "direction", "<PERSON><PERSON><PERSON><PERSON>", "addRange", "ranges", "Transaction", "p", "db", "tables", "keyP<PERSON>", "compound", "result", "req", "openCursor", "query", "ev", "hasGetAll", "oldVersion", "tableChange", "Version", "indexedDB", "<PERSON><PERSON>", "RangeSet", "mergeRanges", "target", "rangesOverlap", "safari14Workaround", "index", "<PERSON><PERSON><PERSON>", "dxTrans", "cache", "key", "state", "Observable", "liveQuery", "_<PERSON><PERSON>", "Debug.debug", "Debug.setDebug", "remove", "replacePrefix", "_<PERSON><PERSON>"]}