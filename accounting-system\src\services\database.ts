import Dexie, { Table } from 'dexie';
import { Transaction, QueryConditions, TransactionStats } from '../types';

export class AccountingDatabase extends Dexie {
  transactions!: Table<Transaction>;

  constructor() {
    super('AccountingDatabase');
    this.version(1).stores({
      transactions: '++id, date, amount, type, category, description, createdAt, updatedAt'
    });
  }
}

export const db = new AccountingDatabase();

export class DatabaseService {
  // 創建交易記錄
  static async createTransaction(transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<number> {
    const now = new Date().toISOString();
    const newTransaction: Omit<Transaction, 'id'> = {
      ...transaction,
      createdAt: now,
      updatedAt: now
    };
    return await db.transactions.add(newTransaction);
  }

  // 獲取所有交易記錄
  static async getAllTransactions(): Promise<Transaction[]> {
    return await db.transactions.orderBy('date').reverse().toArray();
  }

  // 根據ID獲取交易記錄
  static async getTransactionById(id: number): Promise<Transaction | undefined> {
    return await db.transactions.get(id);
  }

  // 更新交易記錄
  static async updateTransaction(id: number, updates: Partial<Transaction>): Promise<number> {
    const updatedData = {
      ...updates,
      updatedAt: new Date().toISOString()
    };
    return await db.transactions.update(id, updatedData);
  }

  // 刪除交易記錄
  static async deleteTransaction(id: number): Promise<void> {
    await db.transactions.delete(id);
  }

  // 根據條件查詢交易記錄
  static async queryTransactions(conditions: QueryConditions): Promise<Transaction[]> {
    let collection = db.transactions.toCollection();

    // 按日期範圍篩選
    if (conditions.startDate && conditions.endDate) {
      collection = collection.filter(t => 
        t.date >= conditions.startDate! && t.date <= conditions.endDate!
      );
    } else if (conditions.startDate) {
      collection = collection.filter(t => t.date >= conditions.startDate!);
    } else if (conditions.endDate) {
      collection = collection.filter(t => t.date <= conditions.endDate!);
    }

    // 按類型篩選
    if (conditions.type) {
      collection = collection.filter(t => t.type === conditions.type);
    }

    // 按類別篩選
    if (conditions.category) {
      collection = collection.filter(t => 
        t.category.toLowerCase().includes(conditions.category!.toLowerCase())
      );
    }

    // 按金額範圍篩選
    if (conditions.minAmount !== undefined) {
      collection = collection.filter(t => t.amount >= conditions.minAmount!);
    }
    if (conditions.maxAmount !== undefined) {
      collection = collection.filter(t => t.amount <= conditions.maxAmount!);
    }

    // 按描述篩選
    if (conditions.description) {
      collection = collection.filter(t => 
        t.description.toLowerCase().includes(conditions.description!.toLowerCase())
      );
    }

    return await collection.sortBy('date');
  }

  // 獲取交易統計
  static async getTransactionStats(conditions?: QueryConditions): Promise<TransactionStats> {
    const transactions = conditions 
      ? await this.queryTransactions(conditions)
      : await this.getAllTransactions();

    const totalIncome = transactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpense = transactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    return {
      totalIncome,
      totalExpense,
      balance: totalIncome - totalExpense,
      transactionCount: transactions.length
    };
  }

  // 獲取所有類別
  static async getAllCategories(): Promise<string[]> {
    const transactions = await this.getAllTransactions();
    const categories = [...new Set(transactions.map(t => t.category))];
    return categories.sort();
  }

  // 搜尋相關交易（模糊搜尋）
  static async searchTransactions(keyword: string): Promise<Transaction[]> {
    const lowerKeyword = keyword.toLowerCase();
    return await db.transactions
      .filter(t => 
        t.description.toLowerCase().includes(lowerKeyword) ||
        t.category.toLowerCase().includes(lowerKeyword)
      )
      .sortBy('date');
  }

  // 清空所有資料
  static async clearAllData(): Promise<void> {
    await db.transactions.clear();
  }
}
