// 交易類型
export interface Transaction {
  id?: number;
  date: string;
  amount: number;
  type: 'income' | 'expense';
  category: string;
  description: string;
  createdAt: string;
  updatedAt?: string;
}

// 交易統計
export interface TransactionStats {
  totalIncome: number;
  totalExpense: number;
  balance: number;
  transactionCount: number;
}

// 查詢條件
export interface QueryConditions {
  startDate?: string;
  endDate?: string;
  type?: 'income' | 'expense';
  category?: string;
  minAmount?: number;
  maxAmount?: number;
  description?: string;
}

// AI聊天消息
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  transactionData?: Partial<Transaction>;
}

// 設定
export interface AppSettings {
  geminiApiKey?: string;
  defaultCategories: string[];
  currency: string;
}

// API回應
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}
